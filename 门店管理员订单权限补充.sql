-- 门店管理员订单权限补充脚本
-- 为门店管理员角色添加订单相关权限

-- 1. 查看当前门店管理员权限情况
SELECT 
    r.role_name,
    r.role_code,
    COUNT(p.id) as current_permission_count
FROM jy_role r
LEFT JOIN jy_role_permission rp ON r.id = rp.role_id
LEFT JOIN jy_permission p ON rp.permission_id = p.id AND p.status = 1
WHERE r.role_code = 'shop_admin'
GROUP BY r.id, r.role_name, r.role_code;

-- 2. 查看缺失的订单相关权限
SELECT p.* 
FROM jy_permission p 
WHERE p.permission LIKE 'shop:order:%' 
  AND p.status = 1
  AND p.id NOT IN (
    SELECT rp.permission_id 
    FROM jy_role_permission rp 
    INNER JOIN jy_role r ON rp.role_id = r.id 
    WHERE r.role_code = 'shop_admin'
  );

-- 3. 添加订单相关权限到权限表（如果不存在）
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) VALUES
('shop:order:read', '门店订单查看权限', 1, NOW(), NOW()),
('shop:order:list', '门店订单列表权限', 1, NOW(), NOW()),
('shop:order:detail', '门店订单详情权限', 1, NOW(), NOW()),
('shop:order:update', '门店订单更新权限', 1, NOW(), NOW()),
('shop:order:complete', '门店订单完成权限', 1, NOW(), NOW()),
('shop:order:cancel', '门店订单取消权限', 1, NOW(), NOW()),
('shop:order:refund', '门店订单退款权限', 1, NOW(), NOW()),
('shop:order:export', '门店订单导出权限', 1, NOW(), NOW());

-- 4. 为门店管理员角色分配订单权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_role r
CROSS JOIN jy_permission p
WHERE r.role_code = 'shop_admin'
  AND p.permission IN (
    'shop:order:read',
    'shop:order:list',
    'shop:order:detail',
    'shop:order:update',
    'shop:order:complete',
    'shop:order:cancel',
    'shop:order:refund',
    'shop:order:export'
  )
  AND p.status = 1;

-- 5. 添加其他可能缺失的门店权限
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) VALUES
('shop:statistics:read', '门店统计数据查看权限', 1, NOW(), NOW()),
('shop:statistics:export', '门店统计数据导出权限', 1, NOW(), NOW()),
('shop:device:read', '门店设备查看权限', 1, NOW(), NOW()),
('shop:device:list', '门店设备列表权限', 1, NOW(), NOW()),
('shop:device:detail', '门店设备详情权限', 1, NOW(), NOW()),
('shop:device:update', '门店设备更新权限', 1, NOW(), NOW()),
('shop:device:fault', '门店设备故障管理权限', 1, NOW(), NOW()),
('shop:device:maintenance', '门店设备维护权限', 1, NOW(), NOW()),
('shop:user:read', '门店用户查看权限', 1, NOW(), NOW()),
('shop:user:list', '门店用户列表权限', 1, NOW(), NOW()),
('shop:user:detail', '门店用户详情权限', 1, NOW(), NOW()),
('shop:notification:read', '门店通知查看权限', 1, NOW(), NOW()),
('shop:notification:list', '门店通知列表权限', 1, NOW(), NOW()),
('shop:profile:read', '个人信息查看权限', 1, NOW(), NOW()),
('shop:profile:update', '个人信息修改权限', 1, NOW(), NOW());

-- 6. 为门店管理员角色分配其他权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_role r
CROSS JOIN jy_permission p
WHERE r.role_code = 'shop_admin'
  AND p.permission IN (
    'shop:statistics:read',
    'shop:statistics:export',
    'shop:device:read',
    'shop:device:list',
    'shop:device:detail',
    'shop:device:update',
    'shop:device:fault',
    'shop:device:maintenance',
    'shop:user:read',
    'shop:user:list',
    'shop:user:detail',
    'shop:notification:read',
    'shop:notification:list',
    'shop:profile:read',
    'shop:profile:update'
  )
  AND p.status = 1;

-- 7. 验证权限分配结果
SELECT 
    r.role_name,
    r.role_code,
    p.permission,
    p.description
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.status = 1
  AND (p.permission LIKE 'shop:order:%' OR p.permission LIKE 'shop:statistics:%' OR p.permission LIKE 'shop:device:%' OR p.permission LIKE 'shop:user:%' OR p.permission LIKE 'shop:notification:%' OR p.permission LIKE 'shop:profile:%')
ORDER BY p.permission;

-- 8. 统计最终权限数量
SELECT 
    r.role_name,
    r.role_code,
    COUNT(p.id) as total_permissions
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.status = 1
GROUP BY r.id, r.role_name, r.role_code;

-- 9. 清除权限缓存（需要重新登录生效）
-- 注意：执行此脚本后，门店管理员需要重新登录以获取最新权限

-- 执行完成提示
SELECT '门店管理员订单权限补充完成！门店管理员现在应该有完整的订单管理权限。请重新登录以获取最新权限。' AS message;
