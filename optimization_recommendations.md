# 今夜城堡系统性能优化方案

## 问题分析

基于日志分析，发现以下关键问题：

### 1. 并发处理问题
- 多个异步任务同时处理同一订单的分成
- 存在重复处理和竞态条件
- 缺乏有效的分布式锁机制

### 2. 数据库性能问题
- 重复查询相同数据（设备、订单信息）
- 缺乏有效的缓存机制
- 不必要的零金额账户操作

### 3. 事务管理问题
- 多个异步任务可能导致事务冲突
- 缺乏统一的事务协调机制

## 优化方案

### 1. 分布式锁优化

#### 实现Redis分布式锁
```java
@Component
public class DistributedLockService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    private static final String LOCK_PREFIX = "order_commission_lock:";
    private static final int LOCK_EXPIRE_TIME = 30; // 30秒
    
    /**
     * 获取订单分成处理锁
     */
    public boolean acquireCommissionLock(String orderNo) {
        String lockKey = LOCK_PREFIX + orderNo;
        String lockValue = UUID.randomUUID().toString();
        
        Boolean result = redisTemplate.opsForValue()
            .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(LOCK_EXPIRE_TIME));
        
        if (Boolean.TRUE.equals(result)) {
            // 设置锁的持有者信息，用于释放时验证
            ThreadLocal<String> lockHolder = new ThreadLocal<>();
            lockHolder.set(lockValue);
            return true;
        }
        return false;
    }
    
    /**
     * 释放订单分成处理锁
     */
    public void releaseCommissionLock(String orderNo, String lockValue) {
        String lockKey = LOCK_PREFIX + orderNo;
        
        // Lua脚本确保原子性释放
        String luaScript = 
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
            "    return redis.call('del', KEYS[1]) " +
            "else " +
            "    return 0 " +
            "end";
        
        redisTemplate.execute(new DefaultRedisScript<>(luaScript, Long.class), 
            Collections.singletonList(lockKey), lockValue);
    }
}
```

#### 优化CommissionServiceImpl
```java
@Service
public class CommissionServiceImpl implements CommissionService {
    
    @Autowired
    private DistributedLockService lockService;
    
    @Override
    @Async("commissionTaskExecutor")
    public void processOrderCommission(String orderNo) {
        // 尝试获取分布式锁
        if (!lockService.acquireCommissionLock(orderNo)) {
            log.info("订单分成处理中，跳过重复处理，订单号: {}", orderNo);
            return;
        }
        
        String lockValue = null;
        try {
            // 获取锁值用于释放
            lockValue = getCurrentLockValue(orderNo);
            
            // 双重检查，确保订单状态
            Order order = orderService.getByOrderNo(orderNo);
            if (order == null || order.getCommissionStatus() != 0) {
                log.info("订单状态已变更，跳过分成处理，订单号: {}", orderNo);
                return;
            }
            
            // 执行分成处理
            doProcessCommission(order);
            
        } catch (Exception e) {
            log.error("订单分成处理失败，订单号: {}", orderNo, e);
            throw e;
        } finally {
            // 释放锁
            if (lockValue != null) {
                lockService.releaseCommissionLock(orderNo, lockValue);
            }
        }
    }
}
```

### 2. 数据库查询优化

#### 实现查询结果缓存
```java
@Service
public class CachedDataService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String DEVICE_CACHE_PREFIX = "device:";
    private static final String ORDER_CACHE_PREFIX = "order:";
    private static final int CACHE_EXPIRE_TIME = 300; // 5分钟
    
    /**
     * 缓存设备信息查询
     */
    @Cacheable(value = "device", key = "#deviceId", unless = "#result == null")
    public Device getDeviceById(Integer deviceId) {
        return deviceMapper.selectById(deviceId);
    }
    
    /**
     * 缓存订单信息查询
     */
    @Cacheable(value = "order", key = "#orderId", unless = "#result == null")
    public Order getOrderById(Long orderId) {
        return orderMapper.selectById(orderId);
    }
    
    /**
     * 批量查询优化
     */
    public Map<Integer, Device> getDevicesByIds(List<Integer> deviceIds) {
        // 先从缓存获取
        Map<Integer, Device> cachedDevices = new HashMap<>();
        List<Integer> uncachedIds = new ArrayList<>();
        
        for (Integer deviceId : deviceIds) {
            String cacheKey = DEVICE_CACHE_PREFIX + deviceId;
            Device device = (Device) redisTemplate.opsForValue().get(cacheKey);
            if (device != null) {
                cachedDevices.put(deviceId, device);
            } else {
                uncachedIds.add(deviceId);
            }
        }
        
        // 批量查询未缓存的数据
        if (!uncachedIds.isEmpty()) {
            List<Device> devices = deviceMapper.selectBatchIds(uncachedIds);
            for (Device device : devices) {
                cachedDevices.put(device.getId(), device);
                // 更新缓存
                String cacheKey = DEVICE_CACHE_PREFIX + device.getId();
                redisTemplate.opsForValue().set(cacheKey, device, 
                    Duration.ofSeconds(CACHE_EXPIRE_TIME));
            }
        }
        
        return cachedDevices;
    }
}
```

#### 数据库索引优化建议
```sql
-- 基于日志分析的索引优化建议

-- 1. 订单表优化索引
ALTER TABLE jy_order ADD INDEX idx_order_no_status (order_no, order_status);
ALTER TABLE jy_order ADD INDEX idx_commission_status_time (commission_status, create_time);
ALTER TABLE jy_order ADD INDEX idx_device_pay_status (device_id, pay_status);

-- 2. 设备表优化索引
ALTER TABLE jy_device ADD INDEX idx_device_no_status (device_no, status);
ALTER TABLE jy_device ADD INDEX idx_entity_partner_shop (entity_id, partner_id, shop_id);

-- 3. 财务账户表优化索引
ALTER TABLE jy_finance_account ADD INDEX idx_account_lookup (account_type, account_id, status);
ALTER TABLE jy_finance_account ADD INDEX idx_balance_status (available_balance, status);

-- 4. 财务流水表优化索引
ALTER TABLE jy_finance_log ADD INDEX idx_account_order_time (account_type, account_id, order_id, create_time);
ALTER TABLE jy_finance_log ADD INDEX idx_type_amount_time (type, amount, create_time);

-- 5. 设备日志表优化索引
ALTER TABLE jy_device_log ADD INDEX idx_device_operation_time (device_id, operation_type, create_time);
ALTER TABLE jy_device_log ADD INDEX idx_log_type_time (log_type, create_time);
```

### 3. 异步任务优化

#### 配置专用线程池
```java
@Configuration
@EnableAsync
public class AsyncConfig {
    
    /**
     * 订单处理专用线程池
     */
    @Bean("orderTaskExecutor")
    public TaskExecutor orderTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("order-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    
    /**
     * 分成处理专用线程池
     */
    @Bean("commissionTaskExecutor")
    public TaskExecutor commissionTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(3);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("commission-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
    
    /**
     * 通知处理专用线程池
     */
    @Bean("notificationTaskExecutor")
    public TaskExecutor notificationTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("notification-task-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        executor.initialize();
        return executor;
    }
}
```

### 4. 事件驱动架构优化

#### 实现事件去重机制
```java
@Component
public class OrderEventProcessor {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    private static final String EVENT_PROCESSED_PREFIX = "event_processed:";
    private static final int EVENT_EXPIRE_TIME = 3600; // 1小时
    
    /**
     * 检查事件是否已处理
     */
    public boolean isEventProcessed(String eventType, String orderId) {
        String key = EVENT_PROCESSED_PREFIX + eventType + ":" + orderId;
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }
    
    /**
     * 标记事件已处理
     */
    public void markEventProcessed(String eventType, String orderId) {
        String key = EVENT_PROCESSED_PREFIX + eventType + ":" + orderId;
        redisTemplate.opsForValue().set(key, "1", Duration.ofSeconds(EVENT_EXPIRE_TIME));
    }
    
    /**
     * 处理订单支付成功事件（防重复）
     */
    @EventListener
    @Async("orderTaskExecutor")
    public void handleOrderPaymentSuccess(OrderPaymentSuccessEvent event) {
        String eventKey = "payment_success";
        String orderId = event.getOrderId().toString();
        
        if (isEventProcessed(eventKey, orderId)) {
            log.debug("订单支付成功事件已处理，跳过，订单ID: {}", orderId);
            return;
        }
        
        try {
            // 处理业务逻辑
            processPaymentSuccess(event);
            
            // 标记事件已处理
            markEventProcessed(eventKey, orderId);
            
        } catch (Exception e) {
            log.error("处理订单支付成功事件失败，订单ID: {}", orderId, e);
            throw e;
        }
    }
}
```

### 5. 财务操作优化

#### 优化零金额处理
```java
@Service
public class OptimizedFinanceAccountService {
    
    /**
     * 批量处理账户操作，过滤零金额
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean batchProcessAccountOperations(List<AccountOperation> operations) {
        // 过滤零金额操作
        List<AccountOperation> validOperations = operations.stream()
            .filter(op -> op.getAmount().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.toList());
        
        if (validOperations.isEmpty()) {
            log.debug("所有账户操作金额为0，跳过处理");
            return true;
        }
        
        // 按账户类型分组，减少数据库查询
        Map<String, List<AccountOperation>> groupedOps = validOperations.stream()
            .collect(Collectors.groupingBy(AccountOperation::getAccountType));
        
        for (Map.Entry<String, List<AccountOperation>> entry : groupedOps.entrySet()) {
            processAccountOperationsByType(entry.getKey(), entry.getValue());
        }
        
        return true;
    }
    
    /**
     * 预检查分成金额，避免不必要的处理
     */
    public CommissionCalculationResult preCalculateCommission(Order order) {
        CommissionCalculationResult result = new CommissionCalculationResult();
        
        // 计算各账户分成金额
        BigDecimal totalAmount = order.getAmount();
        Map<String, BigDecimal> commissions = calculateCommissions(order, totalAmount);
        
        // 过滤零金额分成
        Map<String, BigDecimal> validCommissions = commissions.entrySet().stream()
            .filter(entry -> entry.getValue().compareTo(BigDecimal.ZERO) > 0)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        
        result.setValidCommissions(validCommissions);
        result.setHasValidCommissions(!validCommissions.isEmpty());
        
        return result;
    }
}
```

## 性能监控建议

### 1. 添加关键指标监控
```java
@Component
public class PerformanceMonitor {
    
    private final MeterRegistry meterRegistry;
    private final Timer orderProcessingTimer;
    private final Counter duplicateCommissionCounter;
    
    public PerformanceMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.orderProcessingTimer = Timer.builder("order.processing.time")
            .description("Order processing time")
            .register(meterRegistry);
        this.duplicateCommissionCounter = Counter.builder("commission.duplicate.attempts")
            .description("Duplicate commission processing attempts")
            .register(meterRegistry);
    }
    
    public void recordOrderProcessingTime(Duration duration) {
        orderProcessingTimer.record(duration);
    }
    
    public void incrementDuplicateCommissionAttempt() {
        duplicateCommissionCounter.increment();
    }
}
```

### 2. 数据库连接池优化
```yaml
# application.yml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      max-lifetime: 1800000
      connection-timeout: 30000
      validation-timeout: 5000
      leak-detection-threshold: 60000
```

## 预期优化效果

1. **减少重复处理**: 通过分布式锁，消除重复的分成处理
2. **提升查询性能**: 通过缓存和索引优化，减少数据库查询时间50%+
3. **降低数据库负载**: 通过批量操作和零金额过滤，减少无效数据库操作
4. **提高并发处理能力**: 通过专用线程池，提升系统并发处理能力
5. **增强系统稳定性**: 通过事件去重和异常处理，提升系统稳定性

## 实施步骤

### 第一阶段：分布式锁实施（优先级最高）
1. **部署DistributedLockService**
2. **修改CommissionServiceImpl**，集成分布式锁
3. **测试并发分成处理**，确保无重复处理

### 第二阶段：数据库查询优化
1. **部署CachedDataService**
2. **执行数据库索引优化脚本**
3. **修改相关Service使用缓存查询**

### 第三阶段：异步任务优化
1. **配置专用线程池**
2. **实施事件去重机制**
3. **优化财务操作流程**

### 第四阶段：监控和调优
1. **部署性能监控**
2. **收集性能指标**
3. **根据监控结果进一步优化**

## 关键配置

### Redis配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
```

### 缓存配置
```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 300000  # 5分钟
      cache-null-values: false
```

## 预期效果

1. **消除重复处理**: 分布式锁确保同一订单分成只处理一次
2. **提升查询性能**: 缓存机制减少数据库查询50%+
3. **降低数据库负载**: 批量操作和索引优化减少数据库压力
4. **提高系统稳定性**: 异常处理和监控机制提升系统可靠性

## 风险控制

1. **渐进式部署**: 分阶段实施，降低风险
2. **充分测试**: 在测试环境验证所有功能
3. **监控告警**: 实时监控系统性能指标
4. **快速回滚**: 准备回滚方案，确保系统稳定
