-- 添加微信支付V3版本必要的配置项
-- 注意：WxPayServiceImpl.java中使用merchantSerialNumber，而WxPayConfig.java中使用serialNo
INSERT INTO jy_system (config_type, config_key, config_value, remark, create_time, update_time) 
VALUES 
('wx_pay', 'merchantSerialNumber', '1234567890', '微信支付商户API证书序列号', NOW(), NOW()),
('wx_pay', 'serialNo', '1234567890', '微信支付商户API证书序列号(兼容字段)', NOW(), NOW());

-- 添加API V3密钥（32位字符）
-- 注意：WxPayConfig.java使用apiV3Key字段从key配置读取，而WxPayServiceImpl.java直接使用apiV3Key字段
INSERT INTO jy_system (config_type, config_key, config_value, remark, create_time, update_time) 
VALUES 
('wx_pay', 'apiV3Key', 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef', '微信支付APIv3密钥', NOW(), NOW()),
('wx_pay', 'key', 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef', '微信支付APIv3密钥(兼容字段)', NOW(), NOW())
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = NOW();

-- 添加商户API私钥（测试环境示例，生产环境请使用真实私钥）
INSERT INTO jy_system (config_type, config_key, config_value, remark, create_time, update_time) 
VALUES 
('wx_pay', 'privateKey', 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDZUJN33V+dSfvdfYv5Pnt2rOcmDW7G2EHGrQfDZHJQCCQzjYU7hXRTHJ3LGKrTcJ5HGIjQAi+FNtEw6DP9D9MZHEkXXxZQ4PB5dPmGQQQYJJH+T0GcPGKJBW5J2iM3UDLFHLaWRIjD6BT/B5FpDj1+LyPAUjYsZxCVJFMJqDT8xuhCIGIrLxt9KrLAOZTZs6mUMiUe/qRN8BZUKvZS/TkxZJlV4KjIwKqEYV6m7vSYxZZ5Pxb4v0gj+O+uNH8eHUy8Lm3CKzJZXcCjDqsk9Q8KF+QLRZ1lC3wXEUbh5+5VTQXjPnQJZvPzD/jnGKIiC0NyIvlWTCOGf1TnS1FDAgMBAAECggEAFeFCdZlXrWFj7BvDHRYN7CQ+KFbq7GKy6cmCi7RS+AUVh5etIYbdX3LGYpkWptZBKR45/Fd+GEJKnqEIqBVSEwrGMx5+3gqgVUMhGNs0N0L7jBs17+UzRrJZ9BjY4hh0gOFUQws0h7Ongr6dqRfyVUwJEVxYI9bOQfHaUEI5XLDUj4bjVWKhTUYSeFELs2Zxcpv7vEKvXcEgQmgaygTFrAsGxKMZXyN7YEEwjLqiWXNzixCBHfyYPYz8DIUrf8HGzEfmXJFaClwXYQjTxUJEXIbWXKL1s7gzp5bCQ0h3RQnHfSbsdEwP3fGKc0GQVoRxJwzYHqJtKlLnwuLGUQSdAQKBgQDzlLwXoJ5HGXLvXgUwpNYw3CWgiRJxjYR8TmVM6kFE2BoMQVVdJzl2WuCl5s7+HYQTc4djBLzQHvHxNaC6JRle2kZbOWKqRZZk+mGnlQZi3cFJJmP7yHQwJDZQ+QxYzTOXEEj5Z45/GfZa0xjOzTaZjE5QP0Tcw9MZYWoWNwKBgQDkdGOUBKCsYvW8YbvU/GGnXIOu5OUxCOjEbGxPVxJ4c3egypRbHd5mZD7wZ2v9FQG5q4BIBwXvDONE+RjI6wGqxQJvYnzH9+QKCdU7sn9SuUJPxCYXlZZYK0nBLBEfq8h51xmGlGLQYN9jWHG+xMbq3uXUl6LE/dTOi1KsdQKBgBQNNYy1EdbXWCK5U5Q6QikHG/p+/XDYQGnvM+VkvkB1JjMGe0Fs2xYQJQZhNP5/BUlUYlzFTJoRTJFIQJBVm8AnlmVQEP5w3OUVhEQZFTX15Gror2QgwJTc0+pAJtX5v9lUYMXVj7R5UcYIVUYvj0WTLtYzNxRGO9BTvPnBAoGAYa7JNPNMlMxbGJE17pGS3dSqR3GYCnqxbRNrQlYgCsmiVJxUVgYbQqXTTQnroYsgQyoeSzJiJvFIY9xvpgFNkHyEZLNJaKHqUKgVHXLzT9CQCCLzYUwY3Pn9TJwyZK1rqY53IoHOHOLZnZLCzv8n8SLWcuKxTJuaMRXBWNkCgYEAo4HUjJz43TMJ+RRWZwz4m8NjJGnWk0MSQaELYsd0F5lXEwXYJxzj5Kv3BXIRSXfz4+MkXXQY8R1yYR4qQKUXMbTDJY3XVGqEQKlQFzhXKb9+pUgQC/wQyxDcCkZmFBVvziorqiSKNQwmXNbzzMoRjgpRio7zyQYXXD6QjKWwKDQ=', '微信支付商户API私钥', NOW(), NOW());

-- 修正appId字段（确保appId字段直接存在，不使用wx_pay_appId）
-- 如果已存在appId，则更新它；如果不存在，则插入
INSERT INTO jy_system (config_type, config_key, config_value, remark, create_time, update_time) 
VALUES 
('wx_pay', 'appId', 'wx23b7c6a78842ee76', '微信支付AppID', NOW(), NOW())
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = NOW();

-- 确保设置了商户号
INSERT INTO jy_system (config_type, config_key, config_value, remark, create_time, update_time)
VALUES
('wx_pay', 'mchId', '1721114476', '微信支付商户号', NOW(), NOW())
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = NOW();

-- 确保设置了回调通知URL
INSERT INTO jy_system (config_type, config_key, config_value, remark, create_time, update_time)
VALUES
('wx_pay', 'notifyUrl', 'https://api.example.com/api/wx/pay/callback/notify', '微信支付回调通知URL', NOW(), NOW())
ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = NOW();

-- 请在执行以上SQL脚本后，重启应用使配置生效 