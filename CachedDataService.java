package com.jycb.jycbz.common.service;

import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.mapper.DeviceMapper;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.mapper.OrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 缓存数据服务
 * 用于优化数据库查询性能，减少重复查询
 */
@Slf4j
@Service
public class CachedDataService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private DeviceMapper deviceMapper;
    
    @Autowired
    private OrderMapper orderMapper;
    
    private static final String DEVICE_CACHE_PREFIX = "device:";
    private static final String ORDER_CACHE_PREFIX = "order:";
    private static final String DEVICE_BY_NO_CACHE_PREFIX = "device_by_no:";
    private static final int CACHE_EXPIRE_TIME = 300; // 5分钟
    private static final int BATCH_SIZE = 100; // 批量查询大小
    
    /**
     * 缓存设备信息查询（按ID）
     * 
     * @param deviceId 设备ID
     * @return 设备信息
     */
    @Cacheable(value = "device", key = "#deviceId", unless = "#result == null")
    public Device getDeviceById(Integer deviceId) {
        if (deviceId == null) {
            return null;
        }
        
        log.debug("从数据库查询设备信息，设备ID: {}", deviceId);
        return deviceMapper.selectById(deviceId);
    }
    
    /**
     * 缓存设备信息查询（按设备编号）
     * 
     * @param deviceNo 设备编号
     * @return 设备信息
     */
    @Cacheable(value = "deviceByNo", key = "#deviceNo", unless = "#result == null")
    public Device getDeviceByNo(String deviceNo) {
        if (deviceNo == null || deviceNo.trim().isEmpty()) {
            return null;
        }
        
        log.debug("从数据库查询设备信息，设备编号: {}", deviceNo);
        return deviceMapper.selectOne(
            new QueryWrapper<Device>().eq("device_no", deviceNo)
        );
    }
    
    /**
     * 缓存订单信息查询（按ID）
     * 
     * @param orderId 订单ID
     * @return 订单信息
     */
    @Cacheable(value = "order", key = "#orderId", unless = "#result == null")
    public Order getOrderById(Long orderId) {
        if (orderId == null) {
            return null;
        }
        
        log.debug("从数据库查询订单信息，订单ID: {}", orderId);
        return orderMapper.selectById(orderId);
    }
    
    /**
     * 缓存订单信息查询（按订单号）
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Cacheable(value = "orderByNo", key = "#orderNo", unless = "#result == null")
    public Order getOrderByNo(String orderNo) {
        if (orderNo == null || orderNo.trim().isEmpty()) {
            return null;
        }
        
        log.debug("从数据库查询订单信息，订单号: {}", orderNo);
        return orderMapper.selectOne(
            new QueryWrapper<Order>().eq("order_no", orderNo)
        );
    }
    
    /**
     * 批量查询设备信息（优化版）
     * 先从缓存获取，未命中的再批量查询数据库
     * 
     * @param deviceIds 设备ID列表
     * @return 设备信息映射
     */
    public Map<Integer, Device> getDevicesByIds(List<Integer> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Integer, Device> result = new HashMap<>();
        List<Integer> uncachedIds = new ArrayList<>();
        
        // 先从缓存获取
        for (Integer deviceId : deviceIds) {
            if (deviceId == null) continue;
            
            String cacheKey = DEVICE_CACHE_PREFIX + deviceId;
            try {
                Device device = (Device) redisTemplate.opsForValue().get(cacheKey);
                if (device != null) {
                    result.put(deviceId, device);
                } else {
                    uncachedIds.add(deviceId);
                }
            } catch (Exception e) {
                log.warn("从缓存获取设备信息失败，设备ID: {}", deviceId, e);
                uncachedIds.add(deviceId);
            }
        }
        
        // 批量查询未缓存的数据
        if (!uncachedIds.isEmpty()) {
            log.debug("批量查询未缓存的设备信息，数量: {}", uncachedIds.size());
            
            // 分批查询，避免IN子句过长
            for (int i = 0; i < uncachedIds.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, uncachedIds.size());
                List<Integer> batchIds = uncachedIds.subList(i, endIndex);
                
                List<Device> devices = deviceMapper.selectBatchIds(batchIds);
                for (Device device : devices) {
                    result.put(device.getId(), device);
                    
                    // 更新缓存
                    String cacheKey = DEVICE_CACHE_PREFIX + device.getId();
                    try {
                        redisTemplate.opsForValue().set(cacheKey, device, 
                            Duration.ofSeconds(CACHE_EXPIRE_TIME));
                    } catch (Exception e) {
                        log.warn("更新设备缓存失败，设备ID: {}", device.getId(), e);
                    }
                }
            }
        }
        
        log.debug("批量查询设备完成，请求数量: {}, 缓存命中: {}, 数据库查询: {}", 
            deviceIds.size(), deviceIds.size() - uncachedIds.size(), uncachedIds.size());
        
        return result;
    }
    
    /**
     * 批量查询订单信息（优化版）
     * 
     * @param orderIds 订单ID列表
     * @return 订单信息映射
     */
    public Map<Long, Order> getOrdersByIds(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return new HashMap<>();
        }
        
        Map<Long, Order> result = new HashMap<>();
        List<Long> uncachedIds = new ArrayList<>();
        
        // 先从缓存获取
        for (Long orderId : orderIds) {
            if (orderId == null) continue;
            
            String cacheKey = ORDER_CACHE_PREFIX + orderId;
            try {
                Order order = (Order) redisTemplate.opsForValue().get(cacheKey);
                if (order != null) {
                    result.put(orderId, order);
                } else {
                    uncachedIds.add(orderId);
                }
            } catch (Exception e) {
                log.warn("从缓存获取订单信息失败，订单ID: {}", orderId, e);
                uncachedIds.add(orderId);
            }
        }
        
        // 批量查询未缓存的数据
        if (!uncachedIds.isEmpty()) {
            log.debug("批量查询未缓存的订单信息，数量: {}", uncachedIds.size());
            
            // 分批查询
            for (int i = 0; i < uncachedIds.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, uncachedIds.size());
                List<Long> batchIds = uncachedIds.subList(i, endIndex);
                
                List<Order> orders = orderMapper.selectBatchIds(batchIds);
                for (Order order : orders) {
                    result.put(order.getId(), order);
                    
                    // 更新缓存
                    String cacheKey = ORDER_CACHE_PREFIX + order.getId();
                    try {
                        redisTemplate.opsForValue().set(cacheKey, order, 
                            Duration.ofSeconds(CACHE_EXPIRE_TIME));
                    } catch (Exception e) {
                        log.warn("更新订单缓存失败，订单ID: {}", order.getId(), e);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 清除设备缓存
     * 
     * @param deviceId 设备ID
     */
    @CacheEvict(value = "device", key = "#deviceId")
    public void evictDeviceCache(Integer deviceId) {
        if (deviceId != null) {
            String cacheKey = DEVICE_CACHE_PREFIX + deviceId;
            redisTemplate.delete(cacheKey);
            log.debug("清除设备缓存，设备ID: {}", deviceId);
        }
    }
    
    /**
     * 清除订单缓存
     * 
     * @param orderId 订单ID
     */
    @CacheEvict(value = "order", key = "#orderId")
    public void evictOrderCache(Long orderId) {
        if (orderId != null) {
            String cacheKey = ORDER_CACHE_PREFIX + orderId;
            redisTemplate.delete(cacheKey);
            log.debug("清除订单缓存，订单ID: {}", orderId);
        }
    }
    
    /**
     * 清除设备编号缓存
     * 
     * @param deviceNo 设备编号
     */
    @CacheEvict(value = "deviceByNo", key = "#deviceNo")
    public void evictDeviceByNoCache(String deviceNo) {
        if (deviceNo != null && !deviceNo.trim().isEmpty()) {
            String cacheKey = DEVICE_BY_NO_CACHE_PREFIX + deviceNo;
            redisTemplate.delete(cacheKey);
            log.debug("清除设备编号缓存，设备编号: {}", deviceNo);
        }
    }
    
    /**
     * 预热缓存 - 设备信息
     * 
     * @param deviceIds 要预热的设备ID列表
     */
    public void warmUpDeviceCache(List<Integer> deviceIds) {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return;
        }
        
        log.info("开始预热设备缓存，数量: {}", deviceIds.size());
        
        // 分批预热
        for (int i = 0; i < deviceIds.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, deviceIds.size());
            List<Integer> batchIds = deviceIds.subList(i, endIndex);
            
            List<Device> devices = deviceMapper.selectBatchIds(batchIds);
            for (Device device : devices) {
                String cacheKey = DEVICE_CACHE_PREFIX + device.getId();
                try {
                    redisTemplate.opsForValue().set(cacheKey, device, 
                        Duration.ofSeconds(CACHE_EXPIRE_TIME));
                } catch (Exception e) {
                    log.warn("预热设备缓存失败，设备ID: {}", device.getId(), e);
                }
            }
        }
        
        log.info("设备缓存预热完成");
    }
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 统计设备缓存数量
            Set<String> deviceKeys = redisTemplate.keys(DEVICE_CACHE_PREFIX + "*");
            stats.put("deviceCacheCount", deviceKeys != null ? deviceKeys.size() : 0);
            
            // 统计订单缓存数量
            Set<String> orderKeys = redisTemplate.keys(ORDER_CACHE_PREFIX + "*");
            stats.put("orderCacheCount", orderKeys != null ? orderKeys.size() : 0);
            
        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            stats.put("error", e.getMessage());
        }
        
        return stats;
    }
}
