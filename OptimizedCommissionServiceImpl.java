package com.jycb.jycbz.modules.finance.service.impl;

import com.jycb.jycbz.common.service.DistributedLockService;
import com.jycb.jycbz.modules.finance.service.CommissionService;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 优化后的分成服务实现
 * 解决重复处理和并发问题
 */
@Slf4j
@Service
public class OptimizedCommissionServiceImpl implements CommissionService {
    
    @Autowired
    private DistributedLockService lockService;
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private FinanceAccountService financeAccountService;
    
    /**
     * 处理订单分成（异步，带分布式锁）
     * 
     * @param orderNo 订单号
     */
    @Override
    @Async("commissionTaskExecutor")
    public void processOrderCommission(String orderNo) {
        // 尝试获取分布式锁
        if (!lockService.acquireCommissionLock(orderNo)) {
            log.info("订单分成处理中，跳过重复处理，订单号: {}", orderNo);
            return;
        }
        
        try {
            // 双重检查订单状态
            Order order = orderService.getByOrderNo(orderNo);
            if (order == null) {
                log.warn("订单不存在，跳过分成处理，订单号: {}", orderNo);
                return;
            }
            
            if (order.getCommissionStatus() != 0) {
                log.info("订单分成状态已变更，跳过处理，订单号: {}, 当前状态: {}", 
                    orderNo, order.getCommissionStatus());
                return;
            }
            
            // 预计算分成金额，过滤零金额
            CommissionCalculationResult calculation = preCalculateCommission(order);
            if (!calculation.hasValidCommissions()) {
                log.info("订单无有效分成金额，跳过处理，订单号: {}", orderNo);
                // 直接标记为已处理
                markCommissionAsProcessed(order);
                return;
            }
            
            // 执行分成处理
            boolean success = doProcessCommission(order, calculation);
            
            if (success) {
                log.info("订单分成处理成功，订单号: {}", orderNo);
            } else {
                log.error("订单分成处理失败，订单号: {}", orderNo);
            }
            
        } catch (Exception e) {
            log.error("订单分成处理异常，订单号: {}", orderNo, e);
            throw e;
        } finally {
            // 释放分布式锁
            lockService.releaseCommissionLock(orderNo);
        }
    }
    
    /**
     * 预计算分成金额
     * 
     * @param order 订单信息
     * @return 分成计算结果
     */
    private CommissionCalculationResult preCalculateCommission(Order order) {
        CommissionCalculationResult result = new CommissionCalculationResult();
        Map<String, BigDecimal> commissions = new HashMap<>();
        
        BigDecimal totalAmount = order.getAmount();
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            result.setValidCommissions(commissions);
            result.setHasValidCommissions(false);
            return result;
        }
        
        // 获取分成配置
        CommissionConfig config = getCommissionConfig(order);
        
        // 计算各账户分成金额
        BigDecimal systemCommission = calculateSystemCommission(totalAmount, config);
        BigDecimal entityCommission = calculateEntityCommission(totalAmount, config);
        BigDecimal partnerCommission = calculatePartnerCommission(totalAmount, config);
        BigDecimal shopCommission = calculateShopCommission(totalAmount, config);
        
        // 只保留大于0的分成
        if (systemCommission.compareTo(BigDecimal.ZERO) > 0) {
            commissions.put("system", systemCommission);
        }
        if (entityCommission.compareTo(BigDecimal.ZERO) > 0) {
            commissions.put("entity", entityCommission);
        }
        if (partnerCommission.compareTo(BigDecimal.ZERO) > 0) {
            commissions.put("partner", partnerCommission);
        }
        if (shopCommission.compareTo(BigDecimal.ZERO) > 0) {
            commissions.put("shop", shopCommission);
        }
        
        result.setValidCommissions(commissions);
        result.setHasValidCommissions(!commissions.isEmpty());
        
        log.debug("订单分成预计算完成，订单号: {}, 有效分成数: {}", 
            order.getOrderNo(), commissions.size());
        
        return result;
    }
    
    /**
     * 执行分成处理
     * 
     * @param order 订单信息
     * @param calculation 分成计算结果
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    private boolean doProcessCommission(Order order, CommissionCalculationResult calculation) {
        try {
            // 批量处理账户操作
            Map<String, BigDecimal> commissions = calculation.getValidCommissions();
            
            for (Map.Entry<String, BigDecimal> entry : commissions.entrySet()) {
                String accountType = entry.getKey();
                BigDecimal amount = entry.getValue();
                
                // 获取账户ID
                Integer accountId = getAccountId(order, accountType);
                if (accountId == null) {
                    log.warn("无法获取账户ID，跳过分成，订单号: {}, 账户类型: {}", 
                        order.getOrderNo(), accountType);
                    continue;
                }
                
                // 增加账户余额
                boolean success = financeAccountService.increaseBalance(
                    accountType, accountId, amount, order.getOrderNo(), 
                    "订单分成收入", null, "系统"
                );
                
                if (!success) {
                    log.error("账户余额增加失败，订单号: {}, 账户类型: {}, 金额: {}", 
                        order.getOrderNo(), accountType, amount);
                    throw new RuntimeException("分成处理失败：账户余额增加失败");
                }
                
                log.debug("账户分成成功，订单号: {}, 账户类型: {}, 金额: {}", 
                    order.getOrderNo(), accountType, amount);
            }
            
            // 更新订单分成状态
            markCommissionAsProcessed(order);
            
            return true;
            
        } catch (Exception e) {
            log.error("执行分成处理失败，订单号: {}", order.getOrderNo(), e);
            throw e;
        }
    }
    
    /**
     * 标记分成为已处理
     * 
     * @param order 订单信息
     */
    private void markCommissionAsProcessed(Order order) {
        order.setCommissionStatus(1);
        order.setCommissionTime(LocalDateTime.now());
        orderService.updateById(order);
    }
    
    /**
     * 获取分成配置
     * 
     * @param order 订单信息
     * @return 分成配置
     */
    private CommissionConfig getCommissionConfig(Order order) {
        // 根据订单信息获取分成配置
        // 这里可以从数据库或缓存中获取
        CommissionConfig config = new CommissionConfig();
        
        // 设置默认分成比例（可以从配置表获取）
        config.setSystemRate(BigDecimal.valueOf(0.10)); // 10%
        config.setEntityRate(BigDecimal.valueOf(0.20));  // 20%
        config.setPartnerRate(BigDecimal.valueOf(0.30)); // 30%
        config.setShopRate(BigDecimal.valueOf(0.40));    // 40%
        
        return config;
    }
    
    /**
     * 计算系统分成
     */
    private BigDecimal calculateSystemCommission(BigDecimal totalAmount, CommissionConfig config) {
        return totalAmount.multiply(config.getSystemRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算业务主体分成
     */
    private BigDecimal calculateEntityCommission(BigDecimal totalAmount, CommissionConfig config) {
        return totalAmount.multiply(config.getEntityRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算合作商分成
     */
    private BigDecimal calculatePartnerCommission(BigDecimal totalAmount, CommissionConfig config) {
        return totalAmount.multiply(config.getPartnerRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算门店分成
     */
    private BigDecimal calculateShopCommission(BigDecimal totalAmount, CommissionConfig config) {
        return totalAmount.multiply(config.getShopRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 获取账户ID
     * 
     * @param order 订单信息
     * @param accountType 账户类型
     * @return 账户ID
     */
    private Integer getAccountId(Order order, String accountType) {
        switch (accountType) {
            case "system":
                return 1; // 系统账户ID固定为1
            case "entity":
                return order.getEntityId();
            case "partner":
                return order.getPartnerId();
            case "shop":
                return order.getShopId();
            default:
                return null;
        }
    }
    
    /**
     * 分成计算结果
     */
    private static class CommissionCalculationResult {
        private Map<String, BigDecimal> validCommissions;
        private boolean hasValidCommissions;
        
        // getters and setters
        public Map<String, BigDecimal> getValidCommissions() {
            return validCommissions;
        }
        
        public void setValidCommissions(Map<String, BigDecimal> validCommissions) {
            this.validCommissions = validCommissions;
        }
        
        public boolean hasValidCommissions() {
            return hasValidCommissions;
        }
        
        public void setHasValidCommissions(boolean hasValidCommissions) {
            this.hasValidCommissions = hasValidCommissions;
        }
    }
    
    /**
     * 分成配置
     */
    private static class CommissionConfig {
        private BigDecimal systemRate;
        private BigDecimal entityRate;
        private BigDecimal partnerRate;
        private BigDecimal shopRate;
        
        // getters and setters
        public BigDecimal getSystemRate() {
            return systemRate;
        }
        
        public void setSystemRate(BigDecimal systemRate) {
            this.systemRate = systemRate;
        }
        
        public BigDecimal getEntityRate() {
            return entityRate;
        }
        
        public void setEntityRate(BigDecimal entityRate) {
            this.entityRate = entityRate;
        }
        
        public BigDecimal getPartnerRate() {
            return partnerRate;
        }
        
        public void setPartnerRate(BigDecimal partnerRate) {
            this.partnerRate = partnerRate;
        }
        
        public BigDecimal getShopRate() {
            return shopRate;
        }
        
        public void setShopRate(BigDecimal shopRate) {
            this.shopRate = shopRate;
        }
    }
}
