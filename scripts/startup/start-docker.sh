#!/bin/bash

# ========================================
# 今夜城堡项目Docker环境启动脚本
# 项目名称: jycb-z
# 环境: Docker
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"
DOCKER_IMAGE_NAME="jycb-z"
DOCKER_TAG="latest"
CONTAINER_NAME="jycb-z-container"
HOST_PORT="8080"
CONTAINER_PORT="8080"
NETWORK_NAME="jycb-network"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    log_info "Docker环境检查通过，版本: $(docker --version)"
}

# 检查Docker Compose环境
check_docker_compose() {
    log_info "检查Docker Compose环境..."
    
    if command -v docker-compose &> /dev/null; then
        log_info "Docker Compose环境检查通过，版本: $(docker-compose --version)"
        return 0
    elif docker compose version &> /dev/null; then
        log_info "Docker Compose环境检查通过，版本: $(docker compose version)"
        return 0
    else
        log_warn "Docker Compose未安装，将使用纯Docker命令"
        return 1
    fi
}

# 创建Docker网络
create_network() {
    log_info "创建Docker网络..."
    
    if docker network ls | grep -q "$NETWORK_NAME"; then
        log_info "Docker网络 $NETWORK_NAME 已存在"
    else
        docker network create "$NETWORK_NAME"
        log_info "创建Docker网络: $NETWORK_NAME"
    fi
}

# 构建Docker镜像
build_image() {
    log_info "构建Docker镜像..."
    
    # 检查Dockerfile是否存在
    if [[ ! -f "$PROJECT_ROOT/Dockerfile" ]]; then
        log_warn "Dockerfile不存在，创建默认Dockerfile..."
        create_dockerfile
    fi
    
    # 构建镜像
    cd "$PROJECT_ROOT"
    docker build -t "$DOCKER_IMAGE_NAME:$DOCKER_TAG" .
    
    if [[ $? -eq 0 ]]; then
        log_info "Docker镜像构建成功: $DOCKER_IMAGE_NAME:$DOCKER_TAG"
    else
        log_error "Docker镜像构建失败"
        exit 1
    fi
}

# 创建默认Dockerfile
create_dockerfile() {
    log_info "创建默认Dockerfile..."

    cat > "$PROJECT_ROOT/Dockerfile" << 'EOF'
# 使用OpenJDK 17作为基础镜像
FROM openjdk:17-jdk-slim

# 设置工作目录
WORKDIR /jycb

# 复制JAR文件
COPY target/jycb-z-*.jar /jycb/target/jycb-z-0.0.1-SNAPSHOT.jar

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx2g -XX:+UseG1GC"

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -Dspring.profiles.active=docker -jar /jycb/target/jycb-z-0.0.1-SNAPSHOT.jar"]
EOF

    log_info "默认Dockerfile创建完成"
}

# 启动MySQL容器
start_mysql_container() {
    log_info "启动MySQL容器..."
    
    MYSQL_CONTAINER="jycb-mysql"
    
    if docker ps | grep -q "$MYSQL_CONTAINER"; then
        log_info "MySQL容器已在运行"
        return 0
    fi
    
    if docker ps -a | grep -q "$MYSQL_CONTAINER"; then
        log_info "启动已存在的MySQL容器..."
        docker start "$MYSQL_CONTAINER"
    else
        log_info "创建新的MySQL容器..."
        docker run -d \
            --name "$MYSQL_CONTAINER" \
            --network "$NETWORK_NAME" \
            -e MYSQL_ROOT_PASSWORD=root123 \
            -e MYSQL_DATABASE=jycb_z \
            -e MYSQL_USER=jycb \
            -e MYSQL_PASSWORD=jycb123 \
            -p 3306:3306 \
            mysql:8.0
    fi
    
    # 等待MySQL启动
    log_info "等待MySQL启动..."
    for i in {1..30}; do
        if docker exec "$MYSQL_CONTAINER" mysqladmin ping -h localhost --silent; then
            log_info "MySQL容器启动成功"
            return 0
        fi
        sleep 2
    done
    
    log_error "MySQL容器启动超时"
    exit 1
}

# 启动Redis容器
start_redis_container() {
    log_info "启动Redis容器..."
    
    REDIS_CONTAINER="jycb-redis"
    
    if docker ps | grep -q "$REDIS_CONTAINER"; then
        log_info "Redis容器已在运行"
        return 0
    fi
    
    if docker ps -a | grep -q "$REDIS_CONTAINER"; then
        log_info "启动已存在的Redis容器..."
        docker start "$REDIS_CONTAINER"
    else
        log_info "创建新的Redis容器..."
        docker run -d \
            --name "$REDIS_CONTAINER" \
            --network "$NETWORK_NAME" \
            -p 6379:6379 \
            redis:7-alpine
    fi
    
    log_info "Redis容器启动成功"
}

# 检查容器是否运行
check_container_running() {
    if docker ps | grep -q "$CONTAINER_NAME"; then
        return 0  # 容器正在运行
    fi
    return 1  # 容器未运行
}

# 启动应用容器
start_app_container() {
    log_info "启动 $PROJECT_NAME Docker容器..."
    
    if check_container_running; then
        log_warn "应用容器已在运行中"
        return 0
    fi
    
    # 停止并删除已存在的容器
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        log_info "删除已存在的容器..."
        docker rm -f "$CONTAINER_NAME"
    fi
    
    # 启动新容器
    docker run -d \
        --name "$CONTAINER_NAME" \
        --network "$NETWORK_NAME" \
        -p "$HOST_PORT:$CONTAINER_PORT" \
        -e SPRING_PROFILES_ACTIVE=docker \
        -e SPRING_DATASOURCE_URL="********************************************************************************************************************" \
        -e SPRING_DATASOURCE_USERNAME=jycb \
        -e SPRING_DATASOURCE_PASSWORD=jycb123 \
        -e SPRING_REDIS_HOST=jycb-redis \
        -e SPRING_REDIS_PORT=6379 \
        "$DOCKER_IMAGE_NAME:$DOCKER_TAG"
    
    if [[ $? -eq 0 ]]; then
        log_info "Docker容器启动成功！"
        log_info "容器名称: $CONTAINER_NAME"
        log_info "端口映射: $HOST_PORT:$CONTAINER_PORT"
        log_info "访问地址: http://localhost:$HOST_PORT"
        
        # 等待应用启动
        log_info "等待应用启动..."
        sleep 10
        
        # 健康检查
        if curl -f -s "http://localhost:$HOST_PORT/actuator/health" > /dev/null 2>&1; then
            log_info "健康检查通过"
        else
            log_warn "健康检查失败，应用可能还在启动中"
        fi
    else
        log_error "Docker容器启动失败"
        exit 1
    fi
}

# 停止应用容器
stop_app_container() {
    log_info "停止 $PROJECT_NAME Docker容器..."
    
    if ! check_container_running; then
        log_warn "应用容器未运行"
        return 0
    fi
    
    docker stop "$CONTAINER_NAME"
    log_info "Docker容器已停止"
}

# 重启应用容器
restart_app_container() {
    log_info "重启 $PROJECT_NAME Docker容器..."
    stop_app_container
    sleep 2
    start_app_container
}

# 查看容器状态
status_container() {
    log_info "检查 $PROJECT_NAME Docker容器状态..."
    
    if check_container_running; then
        log_info "容器正在运行"
        docker ps | grep "$CONTAINER_NAME"
        
        # 显示容器资源使用情况
        log_info "容器资源使用情况:"
        docker stats "$CONTAINER_NAME" --no-stream
        
        # 健康检查
        if curl -f -s "http://localhost:$HOST_PORT/actuator/health" > /dev/null 2>&1; then
            log_info "健康状态: 正常"
        else
            log_warn "健康状态: 异常"
        fi
    else
        log_warn "容器未运行"
        
        # 显示所有相关容器
        log_info "相关容器状态:"
        docker ps -a | grep -E "(jycb|$CONTAINER_NAME)"
    fi
}

# 查看容器日志
show_container_logs() {
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        docker logs -f "$CONTAINER_NAME"
    else
        log_error "容器不存在: $CONTAINER_NAME"
    fi
}

# 清理Docker资源
cleanup_docker() {
    log_info "清理Docker资源..."
    
    # 停止并删除应用容器
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        docker rm -f "$CONTAINER_NAME"
        log_info "删除应用容器: $CONTAINER_NAME"
    fi
    
    # 可选：删除MySQL和Redis容器
    read -p "是否删除MySQL和Redis容器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rm -f jycb-mysql jycb-redis 2>/dev/null || true
        log_info "删除数据库容器"
    fi
    
    # 可选：删除镜像
    read -p "是否删除Docker镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker rmi "$DOCKER_IMAGE_NAME:$DOCKER_TAG" 2>/dev/null || true
        log_info "删除Docker镜像"
    fi
    
    # 可选：删除网络
    read -p "是否删除Docker网络? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker network rm "$NETWORK_NAME" 2>/dev/null || true
        log_info "删除Docker网络"
    fi
    
    log_info "Docker资源清理完成"
}

# 使用docker-compose启动
start_with_compose() {
    log_info "使用docker-compose启动服务..."

    cd "$PROJECT_ROOT"

    # 检查docker-compose文件是否存在
    if [[ ! -f "$PROJECT_ROOT/docker-compose.yml" ]]; then
        log_error "docker-compose.yml文件不存在"
        exit 1
    fi

    # 启动服务
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d
    elif docker compose version &> /dev/null; then
        docker compose up -d
    else
        log_error "Docker Compose未安装"
        exit 1
    fi

    if [[ $? -eq 0 ]]; then
        log_info "Docker Compose服务启动成功！"
        log_info "访问地址: http://localhost:$HOST_PORT"

        # 等待服务启动
        log_info "等待服务启动..."
        sleep 15

        # 健康检查
        if curl -f -s "http://localhost:$HOST_PORT/actuator/health" > /dev/null 2>&1; then
            log_info "健康检查通过"
        else
            log_warn "健康检查失败，服务可能还在启动中"
        fi
    else
        log_error "Docker Compose服务启动失败"
        exit 1
    fi
}

# 使用docker-compose停止
stop_with_compose() {
    log_info "使用docker-compose停止服务..."

    cd "$PROJECT_ROOT"

    if command -v docker-compose &> /dev/null; then
        docker-compose down
    elif docker compose version &> /dev/null; then
        docker compose down
    else
        log_error "Docker Compose未安装"
        exit 1
    fi

    log_info "Docker Compose服务已停止"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {build|start|stop|restart|status|logs|cleanup|compose-up|compose-down|help}"
    echo ""
    echo "命令说明:"
    echo "  build        - 构建Docker镜像"
    echo "  start        - 启动Docker容器（单容器模式）"
    echo "  stop         - 停止Docker容器（单容器模式）"
    echo "  restart      - 重启Docker容器"
    echo "  status       - 查看容器状态"
    echo "  logs         - 查看容器日志"
    echo "  cleanup      - 清理Docker资源"
    echo "  compose-up   - 使用docker-compose启动所有服务（推荐）"
    echo "  compose-down - 使用docker-compose停止所有服务"
    echo "  help         - 显示帮助信息"
    echo ""
    echo "配置信息:"
    echo "  项目名称: $PROJECT_NAME"
    echo "  环境: Docker"
    echo "  镜像名称: $DOCKER_IMAGE_NAME:$DOCKER_TAG"
    echo "  容器名称: $CONTAINER_NAME"
    echo "  端口映射: $HOST_PORT:$CONTAINER_PORT"
    echo "  网络名称: $NETWORK_NAME"
    echo ""
    echo "推荐使用:"
    echo "  $0 compose-up    # 启动完整环境（应用+数据库+Redis）"
    echo "  $0 compose-down  # 停止完整环境"
}

# 主函数
main() {
    case "$1" in
        build)
            check_docker
            build_image
            ;;
        start)
            check_docker
            create_network
            start_mysql_container
            start_redis_container
            start_app_container
            ;;
        stop)
            stop_app_container
            ;;
        restart)
            check_docker
            restart_app_container
            ;;
        status)
            status_container
            ;;
        logs)
            show_container_logs
            ;;
        cleanup)
            cleanup_docker
            ;;
        compose-up)
            check_docker
            start_with_compose
            ;;
        compose-down)
            check_docker
            stop_with_compose
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
