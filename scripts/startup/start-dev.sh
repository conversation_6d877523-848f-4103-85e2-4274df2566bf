#!/bin/bash

# ========================================
# 今夜城堡项目开发环境启动脚本
# 项目名称: jycb-z
# 环境: Development
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"
JAR_NAME="jycb-z-0.0.1-SNAPSHOT.jar"
JAVA_OPTS="-Xms256m -Xmx1g -XX:+UseG1GC -Dspring.profiles.active=dev"
SERVER_PORT="8080"
LOG_DIR="$PROJECT_ROOT/logs"
PID_FILE="$PROJECT_ROOT/jycb-z-dev.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Java环境
check_java() {
    log_info "检查Java环境..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装Java 17或更高版本"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | awk -F '"' '{print $2}' | awk -F '.' '{print $1}')
    if [[ $JAVA_VERSION -lt 17 ]]; then
        log_error "Java版本过低，当前版本: $JAVA_VERSION，需要Java 17或更高版本"
        exit 1
    fi
    
    log_info "Java环境检查通过，版本: $(java -version 2>&1 | head -n1)"
}

# 检查端口是否被占用
check_port() {
    log_info "检查端口 $SERVER_PORT 是否可用..."
    
    if netstat -tuln 2>/dev/null | grep -q ":$SERVER_PORT " || ss -tuln 2>/dev/null | grep -q ":$SERVER_PORT "; then
        log_error "端口 $SERVER_PORT 已被占用，请检查是否有其他应用在使用该端口"
        if command -v netstat &> /dev/null; then
            netstat -tuln | grep ":$SERVER_PORT "
        elif command -v ss &> /dev/null; then
            ss -tuln | grep ":$SERVER_PORT "
        fi
        exit 1
    fi
    
    log_info "端口 $SERVER_PORT 可用"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    # 创建日志目录
    if [[ ! -d "$LOG_DIR" ]]; then
        mkdir -p "$LOG_DIR"
        log_info "创建日志目录: $LOG_DIR"
    fi
}

# 查找JAR文件
find_jar() {
    log_info "查找JAR文件..."
    
    # 在target目录查找
    if [[ -f "$PROJECT_ROOT/target/$JAR_NAME" ]]; then
        JAR_PATH="$PROJECT_ROOT/target/$JAR_NAME"
    # 查找任何jycb-z开头的jar文件
    else
        JAR_PATH=$(find "$PROJECT_ROOT/target" -name "jycb-z*.jar" -type f 2>/dev/null | head -n1)
    fi
    
    if [[ -z "$JAR_PATH" || ! -f "$JAR_PATH" ]]; then
        log_error "未找到JAR文件，请先编译项目"
        log_error "运行命令: mvn clean package -DskipTests"
        exit 1
    fi
    
    log_info "找到JAR文件: $JAR_PATH"
}

# 检查应用是否已运行
check_running() {
    if [[ -f "$PID_FILE" ]]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0  # 应用正在运行
        else
            # PID文件存在但进程不存在，删除PID文件
            rm -f "$PID_FILE"
        fi
    fi
    return 1  # 应用未运行
}

# 启动应用
start_app() {
    log_info "启动 $PROJECT_NAME 开发环境..."
    
    if check_running; then
        log_warn "应用已在运行中，PID: $(cat $PID_FILE)"
        return 0
    fi
    
    # 构建启动命令
    JAVA_CMD="java $JAVA_OPTS"
    JAVA_CMD="$JAVA_CMD -Dserver.port=$SERVER_PORT"
    JAVA_CMD="$JAVA_CMD -Dlogging.file.path=$LOG_DIR"
    JAVA_CMD="$JAVA_CMD -jar $JAR_PATH"
    
    log_info "启动命令: $JAVA_CMD"
    
    # 后台启动应用
    nohup $JAVA_CMD > "$LOG_DIR/application-dev.log" 2>&1 &
    APP_PID=$!
    
    # 保存PID
    echo $APP_PID > "$PID_FILE"
    
    # 等待应用启动
    log_info "等待应用启动..."
    sleep 5
    
    # 检查应用是否成功启动
    if ps -p "$APP_PID" > /dev/null 2>&1; then
        log_info "开发环境启动成功！"
        log_info "PID: $APP_PID"
        log_info "端口: $SERVER_PORT"
        log_info "环境: Development"
        log_info "日志文件: $LOG_DIR/application-dev.log"
        log_info "访问地址: http://localhost:$SERVER_PORT"
        log_info "API文档: http://localhost:$SERVER_PORT/swagger-ui.html"
    else
        log_error "应用启动失败，请检查日志文件: $LOG_DIR/application-dev.log"
        rm -f "$PID_FILE"
        exit 1
    fi
}

# 停止应用
stop_app() {
    log_info "停止 $PROJECT_NAME 开发环境..."
    
    if ! check_running; then
        log_warn "应用未运行"
        return 0
    fi
    
    PID=$(cat "$PID_FILE")
    log_info "正在停止应用，PID: $PID"
    
    # 优雅停止
    kill -TERM "$PID"
    
    # 等待应用停止
    for i in {1..30}; do
        if ! ps -p "$PID" > /dev/null 2>&1; then
            log_info "应用已停止"
            rm -f "$PID_FILE"
            return 0
        fi
        sleep 1
    done
    
    # 强制停止
    log_warn "应用未能优雅停止，强制终止..."
    kill -KILL "$PID"
    rm -f "$PID_FILE"
    log_info "应用已强制停止"
}

# 重启应用
restart_app() {
    log_info "重启 $PROJECT_NAME 开发环境..."
    stop_app
    sleep 2
    start_app
}

# 查看应用状态
status_app() {
    log_info "检查 $PROJECT_NAME 开发环境状态..."
    
    if check_running; then
        PID=$(cat "$PID_FILE")
        log_info "应用正在运行，PID: $PID"
        log_info "端口: $SERVER_PORT"
        log_info "环境: Development"
        if command -v ps &> /dev/null; then
            MEMORY=$(ps -p $PID -o rss= 2>/dev/null | awk '{print $1/1024 " MB"}')
            RUNTIME=$(ps -p $PID -o etime= 2>/dev/null | tr -d ' ')
            log_info "内存使用: $MEMORY"
            log_info "运行时间: $RUNTIME"
        fi
    else
        log_warn "应用未运行"
    fi
}

# 查看日志
show_logs() {
    if [[ -f "$LOG_DIR/application-dev.log" ]]; then
        tail -f "$LOG_DIR/application-dev.log"
    else
        log_error "日志文件不存在: $LOG_DIR/application-dev.log"
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {start|stop|restart|status|logs|help}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动开发环境"
    echo "  stop    - 停止开发环境"
    echo "  restart - 重启开发环境"
    echo "  status  - 查看应用状态"
    echo "  logs    - 查看应用日志"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "配置信息:"
    echo "  项目名称: $PROJECT_NAME"
    echo "  环境: Development"
    echo "  JAR文件: $JAR_NAME"
    echo "  端口: $SERVER_PORT"
    echo "  日志目录: $LOG_DIR"
    echo "  PID文件: $PID_FILE"
}

# 主函数
main() {
    case "$1" in
        start)
            check_java
            create_directories
            check_port
            find_jar
            start_app
            ;;
        stop)
            stop_app
            ;;
        restart)
            check_java
            create_directories
            find_jar
            restart_app
            ;;
        status)
            status_app
            ;;
        logs)
            show_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
