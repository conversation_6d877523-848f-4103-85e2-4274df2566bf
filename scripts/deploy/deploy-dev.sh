#!/bin/bash

# ========================================
# 今夜城堡项目开发环境部署脚本
# 项目名称: jycb-z
# 环境: Development
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"
DEV_DIR="$PROJECT_ROOT/dev-deploy"
LOG_DIR="$PROJECT_ROOT/logs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查Java环境
check_java() {
    log_info "检查Java环境..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装Java 17或更高版本"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | awk -F '"' '{print $2}' | awk -F '.' '{print $1}')
    if [[ $JAVA_VERSION -lt 17 ]]; then
        log_error "Java版本过低，当前版本: $JAVA_VERSION，需要Java 17或更高版本"
        exit 1
    fi
    
    log_info "Java环境检查通过，版本: $(java -version 2>&1 | head -n1)"
}

# 检查Maven环境
check_maven() {
    log_info "检查Maven环境..."
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_info "Maven环境检查通过，版本: $(mvn --version | head -n1)"
}

# 创建开发部署目录
create_dev_directories() {
    log_info "创建开发部署目录..."
    
    mkdir -p "$DEV_DIR"
    mkdir -p "$LOG_DIR"
    
    log_info "开发部署目录: $DEV_DIR"
    log_info "日志目录: $LOG_DIR"
}

# 编译项目
compile_project() {
    log_info "编译项目..."
    
    cd "$PROJECT_ROOT"
    
    # 清理并编译
    mvn clean package -DskipTests -q
    
    if [[ $? -eq 0 ]]; then
        log_info "项目编译成功"
    else
        log_error "项目编译失败"
        exit 1
    fi
}

# 运行测试
run_tests() {
    log_info "运行单元测试..."
    
    cd "$PROJECT_ROOT"
    
    mvn test -q
    
    if [[ $? -eq 0 ]]; then
        log_info "单元测试通过"
    else
        log_warn "单元测试失败，但继续部署"
    fi
}

# 部署到开发环境
deploy_to_dev() {
    log_info "部署到开发环境..."
    
    # 查找JAR文件
    JAR_FILE=$(find "$PROJECT_ROOT/target" -name "jycb-z*.jar" -type f | head -n1)
    if [[ -z "$JAR_FILE" || ! -f "$JAR_FILE" ]]; then
        log_error "未找到JAR文件"
        exit 1
    fi
    
    # 复制JAR文件到开发部署目录
    cp "$JAR_FILE" "$DEV_DIR/"
    JAR_NAME=$(basename "$JAR_FILE")
    
    log_info "部署JAR文件: $JAR_NAME"
    
    # 复制配置文件
    if [[ -f "$PROJECT_ROOT/src/main/resources/application-dev.yml" ]]; then
        cp "$PROJECT_ROOT/src/main/resources/application-dev.yml" "$DEV_DIR/application.yml"
        log_info "部署开发配置文件"
    elif [[ -f "$PROJECT_ROOT/src/main/resources/application.yml" ]]; then
        cp "$PROJECT_ROOT/src/main/resources/application.yml" "$DEV_DIR/"
        log_info "部署默认配置文件"
    fi
    
    # 复制启动脚本
    if [[ -f "$PROJECT_ROOT/scripts/startup/start-dev.sh" ]]; then
        cp "$PROJECT_ROOT/scripts/startup/start-dev.sh" "$DEV_DIR/"
        chmod +x "$DEV_DIR/start-dev.sh"
        log_info "部署开发启动脚本"
    fi
}

# 停止开发环境应用
stop_dev_app() {
    log_info "停止开发环境应用..."
    
    if [[ -f "$PROJECT_ROOT/jycb-z-dev.pid" ]]; then
        PID=$(cat "$PROJECT_ROOT/jycb-z-dev.pid")
        if ps -p "$PID" > /dev/null 2>&1; then
            kill -TERM "$PID"
            
            # 等待进程停止
            for i in {1..30}; do
                if ! ps -p "$PID" > /dev/null 2>&1; then
                    log_info "应用已停止"
                    rm -f "$PROJECT_ROOT/jycb-z-dev.pid"
                    return 0
                fi
                sleep 1
            done
            
            # 强制停止
            kill -KILL "$PID"
            rm -f "$PROJECT_ROOT/jycb-z-dev.pid"
            log_info "应用已强制停止"
        else
            rm -f "$PROJECT_ROOT/jycb-z-dev.pid"
        fi
    else
        log_info "应用未运行"
    fi
}

# 启动开发环境应用
start_dev_app() {
    log_info "启动开发环境应用..."
    
    if [[ -f "$DEV_DIR/start-dev.sh" ]]; then
        cd "$DEV_DIR"
        ./start-dev.sh start
    else
        # 直接启动JAR文件
        JAR_FILE=$(find "$DEV_DIR" -name "jycb-z*.jar" -type f | head -n1)
        if [[ -n "$JAR_FILE" && -f "$JAR_FILE" ]]; then
            cd "$DEV_DIR"
            nohup java -Xms256m -Xmx1g -XX:+UseG1GC -Dspring.profiles.active=dev -jar "$JAR_FILE" > "$LOG_DIR/application-dev.log" 2>&1 &
            echo $! > "$PROJECT_ROOT/jycb-z-dev.pid"
            log_info "应用启动成功，PID: $!"
        else
            log_error "未找到JAR文件"
            exit 1
        fi
    fi
}

# 验证部署
verify_dev_deployment() {
    log_info "验证开发环境部署..."
    
    # 等待应用启动
    sleep 10
    
    # 检查进程是否运行
    if [[ -f "$PROJECT_ROOT/jycb-z-dev.pid" ]]; then
        PID=$(cat "$PROJECT_ROOT/jycb-z-dev.pid")
        if ps -p "$PID" > /dev/null 2>&1; then
            log_info "✓ 应用进程运行正常，PID: $PID"
        else
            log_error "✗ 应用进程未运行"
            return 1
        fi
    else
        log_error "✗ 未找到PID文件"
        return 1
    fi
    
    # 检查端口是否监听
    if netstat -tuln 2>/dev/null | grep -q ":8080 " || ss -tuln 2>/dev/null | grep -q ":8080 "; then
        log_info "✓ 端口8080正在监听"
    else
        log_error "✗ 端口8080未监听"
        return 1
    fi
    
    # 检查健康端点
    for i in {1..30}; do
        if curl -f -s "http://localhost:8080/actuator/health" > /dev/null 2>&1; then
            log_info "✓ 健康检查端点响应正常"
            return 0
        fi
        sleep 2
    done
    
    log_error "✗ 健康检查端点无响应"
    return 1
}

# 显示开发环境信息
show_dev_info() {
    log_info "开发环境部署完成！"
    echo ""
    echo "=========================================="
    echo "  今夜城堡项目开发环境部署完成"
    echo "=========================================="
    echo ""
    echo "部署信息:"
    echo "  项目目录: $PROJECT_ROOT"
    echo "  部署目录: $DEV_DIR"
    echo "  日志目录: $LOG_DIR"
    echo "  环境: Development"
    echo ""
    echo "访问地址:"
    echo "  应用首页: http://localhost:8080"
    echo "  API文档: http://localhost:8080/swagger-ui.html"
    echo "  健康检查: http://localhost:8080/actuator/health"
    echo ""
    echo "管理命令:"
    echo "  查看日志: tail -f $LOG_DIR/application-dev.log"
    echo "  停止应用: $PROJECT_ROOT/scripts/startup/start-dev.sh stop"
    echo "  重启应用: $PROJECT_ROOT/scripts/startup/start-dev.sh restart"
    echo ""
    echo "开发提示:"
    echo "1. 修改代码后重新运行部署脚本即可"
    echo "2. 日志文件位于 $LOG_DIR/application-dev.log"
    echo "3. 可以使用IDE直接调试，无需通过脚本启动"
    echo ""
}

# 清理开发环境
cleanup_dev() {
    log_info "清理开发环境..."
    
    # 停止应用
    stop_dev_app
    
    # 清理部署目录
    if [[ -d "$DEV_DIR" ]]; then
        rm -rf "$DEV_DIR"
        log_info "清理部署目录: $DEV_DIR"
    fi
    
    # 清理日志文件
    if [[ -f "$LOG_DIR/application-dev.log" ]]; then
        rm -f "$LOG_DIR/application-dev.log"
        log_info "清理日志文件"
    fi
    
    # 清理PID文件
    if [[ -f "$PROJECT_ROOT/jycb-z-dev.pid" ]]; then
        rm -f "$PROJECT_ROOT/jycb-z-dev.pid"
        log_info "清理PID文件"
    fi
    
    log_info "开发环境清理完成"
}

# 快速重新部署
quick_redeploy() {
    log_info "快速重新部署..."
    
    stop_dev_app
    compile_project
    deploy_to_dev
    start_dev_app
    verify_dev_deployment
    
    log_info "快速重新部署完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {deploy|redeploy|start|stop|verify|cleanup|help}"
    echo ""
    echo "命令说明:"
    echo "  deploy   - 完整部署（编译+测试+部署+启动）"
    echo "  redeploy - 快速重新部署（编译+部署+启动）"
    echo "  start    - 启动开发环境应用"
    echo "  stop     - 停止开发环境应用"
    echo "  verify   - 验证当前部署"
    echo "  cleanup  - 清理开发环境"
    echo "  help     - 显示帮助信息"
    echo ""
    echo "部署流程:"
    echo "1. 检查Java和Maven环境"
    echo "2. 创建开发部署目录"
    echo "3. 编译项目"
    echo "4. 运行单元测试"
    echo "5. 部署到开发环境"
    echo "6. 启动应用"
    echo "7. 验证部署"
}

# 主函数
main() {
    case "$1" in
        deploy)
            check_java
            check_maven
            create_dev_directories
            stop_dev_app
            compile_project
            run_tests
            deploy_to_dev
            start_dev_app
            verify_dev_deployment
            show_dev_info
            ;;
        redeploy)
            check_java
            check_maven
            quick_redeploy
            ;;
        start)
            start_dev_app
            ;;
        stop)
            stop_dev_app
            ;;
        verify)
            verify_dev_deployment
            ;;
        cleanup)
            cleanup_dev
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
