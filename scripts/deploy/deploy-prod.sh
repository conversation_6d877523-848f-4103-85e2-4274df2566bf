#!/bin/bash

# ========================================
# 今夜城堡项目生产环境部署脚本
# 项目名称: jycb-z
# 环境: Production
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

set -e  # 遇到错误立即退出

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"
APP_USER="jycb"
APP_HOME="/opt/jycb-z"
BACKUP_DIR="/opt/jycb-z/backup"
LOG_DIR="/var/log/jycb-z"
SERVICE_NAME="jycb-z"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查应用用户是否存在
check_app_user() {
    if ! id "$APP_USER" &>/dev/null; then
        log_error "应用用户 $APP_USER 不存在，请先运行安装脚本"
        exit 1
    fi
}

# 检查JAR文件
check_jar_file() {
    log_info "检查JAR文件..."
    
    JAR_FILE=$(find "$PROJECT_ROOT/target" -name "jycb-z*.jar" -type f | head -n1)
    if [[ -z "$JAR_FILE" || ! -f "$JAR_FILE" ]]; then
        log_error "未找到JAR文件，请先编译项目"
        log_error "运行命令: mvn clean package -DskipTests"
        exit 1
    fi
    
    log_info "找到JAR文件: $JAR_FILE"
}

# 创建备份
create_backup() {
    log_info "创建备份..."
    
    TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
    BACKUP_PATH="$BACKUP_DIR/$TIMESTAMP"
    
    # 创建备份目录
    mkdir -p "$BACKUP_PATH"
    
    # 备份当前JAR文件
    if [[ -f "$APP_HOME/jycb-z"*.jar ]]; then
        cp "$APP_HOME"/jycb-z*.jar "$BACKUP_PATH/"
        log_info "备份JAR文件到: $BACKUP_PATH"
    fi
    
    # 备份配置文件
    if [[ -f "$APP_HOME/application.yml" ]]; then
        cp "$APP_HOME/application.yml" "$BACKUP_PATH/"
        log_info "备份配置文件到: $BACKUP_PATH"
    fi
    
    # 备份启动脚本
    if [[ -f "$APP_HOME/start-jycb-z.sh" ]]; then
        cp "$APP_HOME/start-jycb-z.sh" "$BACKUP_PATH/"
        log_info "备份启动脚本到: $BACKUP_PATH"
    fi
    
    # 设置备份目录权限
    chown -R "$APP_USER:$APP_USER" "$BACKUP_PATH"
    
    log_info "备份完成: $BACKUP_PATH"
}

# 停止应用服务
stop_service() {
    log_info "停止应用服务..."
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "服务已停止: $SERVICE_NAME"
        
        # 等待服务完全停止
        for i in {1..30}; do
            if ! systemctl is-active --quiet "$SERVICE_NAME"; then
                break
            fi
            sleep 1
        done
    else
        log_info "服务未运行: $SERVICE_NAME"
    fi
}

# 部署新版本
deploy_new_version() {
    log_info "部署新版本..."
    
    # 删除旧的JAR文件
    rm -f "$APP_HOME"/jycb-z*.jar
    
    # 复制新的JAR文件
    cp "$JAR_FILE" "$APP_HOME/"
    NEW_JAR_NAME=$(basename "$JAR_FILE")
    
    # 设置文件权限
    chown "$APP_USER:$APP_USER" "$APP_HOME/$NEW_JAR_NAME"
    chmod 644 "$APP_HOME/$NEW_JAR_NAME"
    
    log_info "部署JAR文件: $NEW_JAR_NAME"
    
    # 复制启动脚本
    if [[ -f "$PROJECT_ROOT/scripts/startup/start-prod.sh" ]]; then
        cp "$PROJECT_ROOT/scripts/startup/start-prod.sh" "$APP_HOME/start-jycb-z.sh"
        chmod +x "$APP_HOME/start-jycb-z.sh"
        chown "$APP_USER:$APP_USER" "$APP_HOME/start-jycb-z.sh"
        log_info "更新启动脚本"
    fi
    
    # 复制配置文件（如果存在）
    if [[ -f "$PROJECT_ROOT/src/main/resources/application-prod.yml" ]]; then
        cp "$PROJECT_ROOT/src/main/resources/application-prod.yml" "$APP_HOME/application.yml"
        chown "$APP_USER:$APP_USER" "$APP_HOME/application.yml"
        log_info "更新配置文件"
    fi
}

# 更新systemd服务文件
update_systemd_service() {
    log_info "更新systemd服务文件..."
    
    if [[ -f "$PROJECT_ROOT/jycb-z.service" ]]; then
        cp "$PROJECT_ROOT/jycb-z.service" "/etc/systemd/system/"
        systemctl daemon-reload
        log_info "systemd服务文件已更新"
    fi
}

# 启动应用服务
start_service() {
    log_info "启动应用服务..."
    
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    for i in {1..60}; do
        if systemctl is-active --quiet "$SERVICE_NAME"; then
            log_info "服务启动成功: $SERVICE_NAME"
            return 0
        fi
        sleep 1
    done
    
    log_error "服务启动失败: $SERVICE_NAME"
    log_error "请检查日志: journalctl -u $SERVICE_NAME -f"
    exit 1
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 等待应用完全启动
    sleep 15
    
    # 检查端口是否监听
    if netstat -tuln | grep -q ":8080 "; then
        log_info "✓ 端口8080正在监听"
    else
        log_error "✗ 端口8080未监听"
        return 1
    fi
    
    # 检查健康端点
    for i in {1..30}; do
        if curl -f -s "http://localhost:8080/actuator/health" > /dev/null 2>&1; then
            log_info "✓ 健康检查端点响应正常"
            return 0
        fi
        sleep 2
    done
    
    log_error "✗ 健康检查端点无响应"
    return 1
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查服务状态
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "✓ 服务运行正常"
    else
        log_error "✗ 服务未运行"
        return 1
    fi
    
    # 执行健康检查
    if health_check; then
        log_info "✓ 健康检查通过"
    else
        log_error "✗ 健康检查失败"
        return 1
    fi
    
    # 检查日志中是否有错误
    if journalctl -u "$SERVICE_NAME" --since "5 minutes ago" | grep -i error > /dev/null; then
        log_warn "⚠ 日志中发现错误信息，请检查"
        journalctl -u "$SERVICE_NAME" --since "5 minutes ago" | grep -i error | tail -5
    else
        log_info "✓ 日志检查正常"
    fi
    
    log_info "部署验证完成"
}

# 回滚到上一个版本
rollback() {
    log_info "回滚到上一个版本..."
    
    # 查找最新的备份
    LATEST_BACKUP=$(ls -1t "$BACKUP_DIR" | head -n1)
    if [[ -z "$LATEST_BACKUP" ]]; then
        log_error "未找到备份文件，无法回滚"
        exit 1
    fi
    
    BACKUP_PATH="$BACKUP_DIR/$LATEST_BACKUP"
    log_info "使用备份: $BACKUP_PATH"
    
    # 停止服务
    stop_service
    
    # 恢复JAR文件
    if [[ -f "$BACKUP_PATH"/jycb-z*.jar ]]; then
        rm -f "$APP_HOME"/jycb-z*.jar
        cp "$BACKUP_PATH"/jycb-z*.jar "$APP_HOME/"
        chown "$APP_USER:$APP_USER" "$APP_HOME"/jycb-z*.jar
        log_info "恢复JAR文件"
    fi
    
    # 恢复配置文件
    if [[ -f "$BACKUP_PATH/application.yml" ]]; then
        cp "$BACKUP_PATH/application.yml" "$APP_HOME/"
        chown "$APP_USER:$APP_USER" "$APP_HOME/application.yml"
        log_info "恢复配置文件"
    fi
    
    # 恢复启动脚本
    if [[ -f "$BACKUP_PATH/start-jycb-z.sh" ]]; then
        cp "$BACKUP_PATH/start-jycb-z.sh" "$APP_HOME/"
        chmod +x "$APP_HOME/start-jycb-z.sh"
        chown "$APP_USER:$APP_USER" "$APP_HOME/start-jycb-z.sh"
        log_info "恢复启动脚本"
    fi
    
    # 启动服务
    start_service
    
    # 验证回滚
    if verify_deployment; then
        log_info "回滚成功"
    else
        log_error "回滚失败"
        exit 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份..."
    
    # 保留最近10个备份
    BACKUP_COUNT=$(ls -1 "$BACKUP_DIR" 2>/dev/null | wc -l)
    if [[ $BACKUP_COUNT -gt 10 ]]; then
        OLD_BACKUPS=$(ls -1t "$BACKUP_DIR" | tail -n +11)
        for backup in $OLD_BACKUPS; do
            rm -rf "$BACKUP_DIR/$backup"
            log_info "删除旧备份: $backup"
        done
    fi
}

# 显示部署信息
show_deployment_info() {
    log_info "部署完成！"
    echo ""
    echo "=========================================="
    echo "  今夜城堡项目生产环境部署完成"
    echo "=========================================="
    echo ""
    echo "部署信息:"
    echo "  应用目录: $APP_HOME"
    echo "  日志目录: $LOG_DIR"
    echo "  备份目录: $BACKUP_DIR"
    echo "  服务名称: $SERVICE_NAME"
    echo ""
    echo "管理命令:"
    echo "  查看状态: sudo systemctl status $SERVICE_NAME"
    echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
    echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
    echo ""
    echo "访问地址: http://localhost:8080"
    echo ""
    echo "注意事项:"
    echo "1. 请监控应用日志确保正常运行"
    echo "2. 建议设置监控和告警"
    echo "3. 定期检查磁盘空间和备份"
    echo ""
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {deploy|rollback|verify|cleanup|help}"
    echo ""
    echo "命令说明:"
    echo "  deploy   - 部署新版本"
    echo "  rollback - 回滚到上一个版本"
    echo "  verify   - 验证当前部署"
    echo "  cleanup  - 清理旧备份"
    echo "  help     - 显示帮助信息"
    echo ""
    echo "部署流程:"
    echo "1. 检查环境和文件"
    echo "2. 创建备份"
    echo "3. 停止服务"
    echo "4. 部署新版本"
    echo "5. 启动服务"
    echo "6. 验证部署"
    echo "7. 清理旧备份"
}

# 主函数
main() {
    case "$1" in
        deploy)
            check_root
            check_app_user
            check_jar_file
            create_backup
            stop_service
            deploy_new_version
            update_systemd_service
            start_service
            verify_deployment
            cleanup_old_backups
            show_deployment_info
            ;;
        rollback)
            check_root
            check_app_user
            rollback
            ;;
        verify)
            verify_deployment
            ;;
        cleanup)
            check_root
            cleanup_old_backups
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
