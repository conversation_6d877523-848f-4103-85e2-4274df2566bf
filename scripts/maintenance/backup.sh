#!/bin/bash

# ========================================
# 今夜城堡项目数据库备份脚本
# 项目名称: jycb-z
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="jycb_z"
DB_USER="root"
DB_PASSWORD=""

# 备份配置
BACKUP_DIR="/opt/jycb-z/backup/database"
BACKUP_RETENTION_DAYS=30
COMPRESS_BACKUP=true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查MySQL环境
check_mysql() {
    log_info "检查MySQL环境..."
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump命令未找到，请安装MySQL客户端"
        exit 1
    fi
    
    if ! command -v mysql &> /dev/null; then
        log_error "mysql命令未找到，请安装MySQL客户端"
        exit 1
    fi
    
    log_info "MySQL客户端检查通过"
}

# 检查数据库连接
check_db_connection() {
    log_info "检查数据库连接..."
    
    if [[ -n "$DB_PASSWORD" ]]; then
        mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" > /dev/null 2>&1
    else
        mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -e "SELECT 1;" > /dev/null 2>&1
    fi
    
    if [[ $? -eq 0 ]]; then
        log_info "数据库连接成功"
    else
        log_error "数据库连接失败，请检查连接参数"
        exit 1
    fi
}

# 创建备份目录
create_backup_dir() {
    log_info "创建备份目录..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR"
        log_info "创建备份目录: $BACKUP_DIR"
    fi
    
    # 检查目录权限
    if [[ ! -w "$BACKUP_DIR" ]]; then
        log_error "备份目录没有写权限: $BACKUP_DIR"
        exit 1
    fi
}

# 执行数据库备份
backup_database() {
    log_info "开始备份数据库..."
    
    TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
    BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_${TIMESTAMP}.sql"
    
    # 构建mysqldump命令
    MYSQLDUMP_CMD="mysqldump"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD -h $DB_HOST"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD -P $DB_PORT"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD -u $DB_USER"
    
    if [[ -n "$DB_PASSWORD" ]]; then
        MYSQLDUMP_CMD="$MYSQLDUMP_CMD -p$DB_PASSWORD"
    fi
    
    # 添加备份选项
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD --single-transaction"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD --routines"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD --triggers"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD --events"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD --hex-blob"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD --default-character-set=utf8mb4"
    MYSQLDUMP_CMD="$MYSQLDUMP_CMD $DB_NAME"
    
    log_debug "执行备份命令: $MYSQLDUMP_CMD"
    
    # 执行备份
    eval "$MYSQLDUMP_CMD" > "$BACKUP_FILE" 2>/dev/null
    
    if [[ $? -eq 0 && -f "$BACKUP_FILE" ]]; then
        BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
        log_info "数据库备份成功: $BACKUP_FILE ($BACKUP_SIZE)"
        
        # 压缩备份文件
        if [[ "$COMPRESS_BACKUP" == "true" ]]; then
            compress_backup "$BACKUP_FILE"
        fi
        
        return 0
    else
        log_error "数据库备份失败"
        rm -f "$BACKUP_FILE"
        exit 1
    fi
}

# 压缩备份文件
compress_backup() {
    local backup_file="$1"
    log_info "压缩备份文件..."
    
    if command -v gzip &> /dev/null; then
        gzip "$backup_file"
        COMPRESSED_FILE="${backup_file}.gz"
        
        if [[ -f "$COMPRESSED_FILE" ]]; then
            COMPRESSED_SIZE=$(du -h "$COMPRESSED_FILE" | cut -f1)
            log_info "备份文件压缩完成: $COMPRESSED_FILE ($COMPRESSED_SIZE)"
        else
            log_error "备份文件压缩失败"
        fi
    else
        log_warn "gzip命令未找到，跳过压缩"
    fi
}

# 备份应用配置文件
backup_config() {
    log_info "备份应用配置文件..."
    
    TIMESTAMP=$(date '+%Y%m%d_%H%M%S')
    CONFIG_BACKUP_DIR="$BACKUP_DIR/config_$TIMESTAMP"
    
    mkdir -p "$CONFIG_BACKUP_DIR"
    
    # 备份生产环境配置
    if [[ -f "/opt/jycb-z/application.yml" ]]; then
        cp "/opt/jycb-z/application.yml" "$CONFIG_BACKUP_DIR/"
        log_info "备份生产配置文件"
    fi
    
    # 备份项目配置文件
    if [[ -f "$PROJECT_ROOT/src/main/resources/application.yml" ]]; then
        cp "$PROJECT_ROOT/src/main/resources/application.yml" "$CONFIG_BACKUP_DIR/application-default.yml"
        log_info "备份默认配置文件"
    fi
    
    if [[ -f "$PROJECT_ROOT/src/main/resources/application-prod.yml" ]]; then
        cp "$PROJECT_ROOT/src/main/resources/application-prod.yml" "$CONFIG_BACKUP_DIR/"
        log_info "备份生产环境配置文件"
    fi
    
    # 压缩配置备份
    if [[ -d "$CONFIG_BACKUP_DIR" ]]; then
        cd "$BACKUP_DIR"
        tar -czf "config_$TIMESTAMP.tar.gz" "config_$TIMESTAMP"
        rm -rf "$CONFIG_BACKUP_DIR"
        log_info "配置文件备份完成: config_$TIMESTAMP.tar.gz"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warn "备份目录不存在，跳过清理"
        return 0
    fi
    
    # 清理旧的数据库备份
    find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null
    
    # 清理旧的配置备份
    find "$BACKUP_DIR" -name "config_*.tar.gz" -type f -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null
    
    # 统计清理结果
    REMAINING_BACKUPS=$(find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f | wc -l)
    log_info "清理完成，保留 $REMAINING_BACKUPS 个数据库备份文件"
}

# 列出备份文件
list_backups() {
    log_info "备份文件列表:"
    echo ""
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warn "备份目录不存在: $BACKUP_DIR"
        return 0
    fi
    
    # 列出数据库备份
    echo "数据库备份文件:"
    find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f -exec ls -lh {} \; | sort -k9
    
    echo ""
    
    # 列出配置备份
    echo "配置备份文件:"
    find "$BACKUP_DIR" -name "config_*.tar.gz" -type f -exec ls -lh {} \; | sort -k9
    
    echo ""
    
    # 显示总大小
    TOTAL_SIZE=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)
    echo "备份目录总大小: $TOTAL_SIZE"
}

# 恢复数据库
restore_database() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        log_error "请指定要恢复的备份文件"
        echo "用法: $0 restore <backup_file>"
        echo "可用的备份文件:"
        find "$BACKUP_DIR" -name "${DB_NAME}_backup_*.sql*" -type f | sort
        exit 1
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_warn "即将恢复数据库，这将覆盖当前数据！"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消恢复操作"
        exit 0
    fi
    
    log_info "开始恢复数据库: $backup_file"
    
    # 检查文件是否压缩
    if [[ "$backup_file" == *.gz ]]; then
        log_info "解压备份文件..."
        if [[ -n "$DB_PASSWORD" ]]; then
            zcat "$backup_file" | mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME"
        else
            zcat "$backup_file" | mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" "$DB_NAME"
        fi
    else
        if [[ -n "$DB_PASSWORD" ]]; then
            mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$backup_file"
        else
            mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" "$DB_NAME" < "$backup_file"
        fi
    fi
    
    if [[ $? -eq 0 ]]; then
        log_info "数据库恢复成功"
    else
        log_error "数据库恢复失败"
        exit 1
    fi
}

# 验证备份文件
verify_backup() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        log_error "请指定要验证的备份文件"
        exit 1
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "备份文件不存在: $backup_file"
        exit 1
    fi
    
    log_info "验证备份文件: $backup_file"
    
    # 检查文件是否为空
    if [[ ! -s "$backup_file" ]]; then
        log_error "备份文件为空"
        exit 1
    fi
    
    # 检查文件格式
    if [[ "$backup_file" == *.gz ]]; then
        # 检查gzip文件完整性
        if gzip -t "$backup_file" 2>/dev/null; then
            log_info "✓ 压缩文件完整性检查通过"
        else
            log_error "✗ 压缩文件损坏"
            exit 1
        fi
        
        # 检查SQL内容
        if zcat "$backup_file" | head -n 10 | grep -q "MySQL dump"; then
            log_info "✓ SQL文件格式检查通过"
        else
            log_error "✗ 不是有效的MySQL备份文件"
            exit 1
        fi
    else
        # 检查SQL内容
        if head -n 10 "$backup_file" | grep -q "MySQL dump"; then
            log_info "✓ SQL文件格式检查通过"
        else
            log_error "✗ 不是有效的MySQL备份文件"
            exit 1
        fi
    fi
    
    log_info "备份文件验证通过"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {backup|restore|list|verify|cleanup|help} [参数]"
    echo ""
    echo "命令说明:"
    echo "  backup           - 执行完整备份（数据库+配置）"
    echo "  restore <file>   - 恢复指定的数据库备份"
    echo "  list             - 列出所有备份文件"
    echo "  verify <file>    - 验证备份文件完整性"
    echo "  cleanup          - 清理过期备份文件"
    echo "  help             - 显示帮助信息"
    echo ""
    echo "配置信息:"
    echo "  数据库: $DB_HOST:$DB_PORT/$DB_NAME"
    echo "  备份目录: $BACKUP_DIR"
    echo "  保留天数: $BACKUP_RETENTION_DAYS 天"
    echo "  压缩备份: $COMPRESS_BACKUP"
    echo ""
    echo "示例:"
    echo "  $0 backup                                    # 执行完整备份"
    echo "  $0 restore /path/to/backup.sql.gz          # 恢复备份"
    echo "  $0 verify /path/to/backup.sql.gz           # 验证备份"
}

# 主函数
main() {
    case "$1" in
        backup)
            check_mysql
            check_db_connection
            create_backup_dir
            backup_database
            backup_config
            cleanup_old_backups
            ;;
        restore)
            check_mysql
            check_db_connection
            restore_database "$2"
            ;;
        list)
            list_backups
            ;;
        verify)
            verify_backup "$2"
            ;;
        cleanup)
            cleanup_old_backups
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
