#!/bin/bash

# ========================================
# 今夜城堡项目健康检查脚本
# 项目名称: jycb-z
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"

# 健康检查配置
APP_HOST="localhost"
APP_PORT="8080"
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="jycb_z"
REDIS_HOST="localhost"
REDIS_PORT="6379"

# 检查超时时间（秒）
TIMEOUT=10
MAX_RETRIES=3

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 成功和失败计数器
SUCCESS_COUNT=0
FAILURE_COUNT=0
WARNING_COUNT=0

# 记录检查结果
record_result() {
    local status="$1"
    local message="$2"
    
    case "$status" in
        "SUCCESS")
            echo "✓ $message"
            ((SUCCESS_COUNT++))
            ;;
        "FAILURE")
            echo "✗ $message"
            ((FAILURE_COUNT++))
            ;;
        "WARNING")
            echo "⚠ $message"
            ((WARNING_COUNT++))
            ;;
    esac
}

# 检查应用进程
check_app_process() {
    log_info "检查应用进程..."
    
    # 检查systemd服务
    if systemctl is-active --quiet jycb-z 2>/dev/null; then
        record_result "SUCCESS" "systemd服务运行正常"
        
        # 获取服务状态详情
        SERVICE_STATUS=$(systemctl status jycb-z --no-pager -l 2>/dev/null)
        if echo "$SERVICE_STATUS" | grep -q "active (running)"; then
            record_result "SUCCESS" "服务状态: active (running)"
        else
            record_result "WARNING" "服务状态异常"
        fi
    else
        # 检查PID文件
        PID_FILES=(
            "/var/run/jycb-z.pid"
            "$PROJECT_ROOT/jycb-z-dev.pid"
            "$PROJECT_ROOT/jycb-z-test.pid"
        )
        
        PROCESS_FOUND=false
        for pid_file in "${PID_FILES[@]}"; do
            if [[ -f "$pid_file" ]]; then
                PID=$(cat "$pid_file")
                if ps -p "$PID" > /dev/null 2>&1; then
                    record_result "SUCCESS" "应用进程运行正常 (PID: $PID)"
                    PROCESS_FOUND=true
                    break
                else
                    rm -f "$pid_file"
                fi
            fi
        done
        
        if [[ "$PROCESS_FOUND" == "false" ]]; then
            # 检查Java进程
            JAVA_PROCESSES=$(ps aux | grep "jycb-z.*\.jar" | grep -v grep)
            if [[ -n "$JAVA_PROCESSES" ]]; then
                record_result "WARNING" "发现Java进程但无PID文件"
                echo "$JAVA_PROCESSES"
            else
                record_result "FAILURE" "未发现应用进程"
            fi
        fi
    fi
}

# 检查端口监听
check_port_listening() {
    log_info "检查端口监听..."
    
    if netstat -tuln 2>/dev/null | grep -q ":$APP_PORT " || ss -tuln 2>/dev/null | grep -q ":$APP_PORT "; then
        record_result "SUCCESS" "端口 $APP_PORT 正在监听"
    else
        record_result "FAILURE" "端口 $APP_PORT 未监听"
    fi
}

# 检查HTTP健康端点
check_health_endpoint() {
    log_info "检查HTTP健康端点..."
    
    local health_url="http://$APP_HOST:$APP_PORT/actuator/health"
    
    for ((i=1; i<=MAX_RETRIES; i++)); do
        if curl -f -s --connect-timeout $TIMEOUT "$health_url" > /dev/null 2>&1; then
            # 获取详细健康信息
            HEALTH_RESPONSE=$(curl -s --connect-timeout $TIMEOUT "$health_url" 2>/dev/null)
            if echo "$HEALTH_RESPONSE" | grep -q '"status":"UP"'; then
                record_result "SUCCESS" "健康检查端点响应正常"
                
                # 检查各组件状态
                if echo "$HEALTH_RESPONSE" | grep -q '"db"'; then
                    if echo "$HEALTH_RESPONSE" | grep -q '"db":{"status":"UP"'; then
                        record_result "SUCCESS" "数据库连接正常"
                    else
                        record_result "FAILURE" "数据库连接异常"
                    fi
                fi
                
                if echo "$HEALTH_RESPONSE" | grep -q '"redis"'; then
                    if echo "$HEALTH_RESPONSE" | grep -q '"redis":{"status":"UP"'; then
                        record_result "SUCCESS" "Redis连接正常"
                    else
                        record_result "WARNING" "Redis连接异常"
                    fi
                fi
                
                return 0
            else
                record_result "WARNING" "健康检查端点返回异常状态"
                echo "响应内容: $HEALTH_RESPONSE"
                return 1
            fi
        fi
        
        if [[ $i -lt $MAX_RETRIES ]]; then
            log_debug "健康检查失败，重试 $i/$MAX_RETRIES..."
            sleep 2
        fi
    done
    
    record_result "FAILURE" "健康检查端点无响应 (重试 $MAX_RETRIES 次)"
}

# 检查API端点
check_api_endpoints() {
    log_info "检查关键API端点..."
    
    local base_url="http://$APP_HOST:$APP_PORT"
    
    # 检查信息端点
    if curl -f -s --connect-timeout $TIMEOUT "$base_url/actuator/info" > /dev/null 2>&1; then
        record_result "SUCCESS" "信息端点响应正常"
    else
        record_result "WARNING" "信息端点无响应"
    fi
    
    # 检查指标端点
    if curl -f -s --connect-timeout $TIMEOUT "$base_url/actuator/metrics" > /dev/null 2>&1; then
        record_result "SUCCESS" "指标端点响应正常"
    else
        record_result "WARNING" "指标端点无响应"
    fi
    
    # 检查Swagger文档
    if curl -f -s --connect-timeout $TIMEOUT "$base_url/swagger-ui.html" > /dev/null 2>&1; then
        record_result "SUCCESS" "API文档页面可访问"
    else
        record_result "WARNING" "API文档页面无法访问"
    fi
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    if ! command -v mysql &> /dev/null; then
        record_result "WARNING" "MySQL客户端未安装，跳过数据库检查"
        return 0
    fi
    
    # 检查MySQL服务
    if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
        record_result "SUCCESS" "MySQL服务运行正常"
    else
        record_result "FAILURE" "MySQL服务未运行"
        return 1
    fi
    
    # 检查数据库连接
    if mysql -h "$DB_HOST" -P "$DB_PORT" -u root -e "SELECT 1;" > /dev/null 2>&1; then
        record_result "SUCCESS" "数据库连接正常"
        
        # 检查目标数据库是否存在
        if mysql -h "$DB_HOST" -P "$DB_PORT" -u root -e "USE $DB_NAME; SELECT 1;" > /dev/null 2>&1; then
            record_result "SUCCESS" "目标数据库 $DB_NAME 存在"
            
            # 检查表数量
            TABLE_COUNT=$(mysql -h "$DB_HOST" -P "$DB_PORT" -u root -e "USE $DB_NAME; SHOW TABLES;" 2>/dev/null | wc -l)
            if [[ $TABLE_COUNT -gt 1 ]]; then
                record_result "SUCCESS" "数据库包含 $((TABLE_COUNT-1)) 个表"
            else
                record_result "WARNING" "数据库为空或表数量异常"
            fi
        else
            record_result "FAILURE" "目标数据库 $DB_NAME 不存在"
        fi
    else
        record_result "FAILURE" "数据库连接失败"
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    if ! command -v redis-cli &> /dev/null; then
        record_result "WARNING" "Redis客户端未安装，跳过Redis检查"
        return 0
    fi
    
    # 检查Redis服务
    if systemctl is-active --quiet redis-server || systemctl is-active --quiet redis; then
        record_result "SUCCESS" "Redis服务运行正常"
    else
        record_result "WARNING" "Redis服务未运行"
        return 1
    fi
    
    # 检查Redis连接
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1; then
        record_result "SUCCESS" "Redis连接正常"
        
        # 检查Redis信息
        REDIS_INFO=$(redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" info server 2>/dev/null)
        if [[ -n "$REDIS_INFO" ]]; then
            REDIS_VERSION=$(echo "$REDIS_INFO" | grep "redis_version" | cut -d: -f2 | tr -d '\r')
            record_result "SUCCESS" "Redis版本: $REDIS_VERSION"
        fi
    else
        record_result "FAILURE" "Redis连接失败"
    fi
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查CPU使用率
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    if [[ -n "$CPU_USAGE" ]]; then
        CPU_PERCENT=$(echo "$CPU_USAGE" | sed 's/%//')
        if (( $(echo "$CPU_PERCENT > 80" | bc -l) )); then
            record_result "WARNING" "CPU使用率较高: ${CPU_USAGE}"
        else
            record_result "SUCCESS" "CPU使用率正常: ${CPU_USAGE}"
        fi
    fi
    
    # 检查内存使用率
    MEMORY_INFO=$(free | grep Mem)
    TOTAL_MEM=$(echo "$MEMORY_INFO" | awk '{print $2}')
    USED_MEM=$(echo "$MEMORY_INFO" | awk '{print $3}')
    MEMORY_PERCENT=$((USED_MEM * 100 / TOTAL_MEM))
    
    if [[ $MEMORY_PERCENT -gt 80 ]]; then
        record_result "WARNING" "内存使用率较高: ${MEMORY_PERCENT}%"
    else
        record_result "SUCCESS" "内存使用率正常: ${MEMORY_PERCENT}%"
    fi
    
    # 检查磁盘使用率
    DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $DISK_USAGE -gt 80 ]]; then
        record_result "WARNING" "磁盘使用率较高: ${DISK_USAGE}%"
    else
        record_result "SUCCESS" "磁盘使用率正常: ${DISK_USAGE}%"
    fi
}

# 检查日志文件
check_log_files() {
    log_info "检查日志文件..."
    
    LOG_FILES=(
        "/var/log/jycb-z/application.log"
        "/var/log/jycb-z/application-prod.log"
        "$PROJECT_ROOT/logs/application.log"
        "$PROJECT_ROOT/logs/application-dev.log"
        "$PROJECT_ROOT/logs/application-test.log"
    )
    
    LOG_FOUND=false
    for log_file in "${LOG_FILES[@]}"; do
        if [[ -f "$log_file" ]]; then
            LOG_FOUND=true
            
            # 检查日志文件大小
            LOG_SIZE=$(du -h "$log_file" | cut -f1)
            record_result "SUCCESS" "日志文件存在: $log_file ($LOG_SIZE)"
            
            # 检查最近的错误日志
            RECENT_ERRORS=$(tail -n 100 "$log_file" | grep -i "error\|exception" | wc -l)
            if [[ $RECENT_ERRORS -gt 0 ]]; then
                record_result "WARNING" "发现 $RECENT_ERRORS 个最近的错误日志"
                echo "最近的错误:"
                tail -n 100 "$log_file" | grep -i "error\|exception" | tail -3
            else
                record_result "SUCCESS" "最近无错误日志"
            fi
            
            break
        fi
    done
    
    if [[ "$LOG_FOUND" == "false" ]]; then
        record_result "WARNING" "未找到日志文件"
    fi
}

# 生成健康检查报告
generate_health_report() {
    log_info "生成健康检查报告..."
    
    REPORT_FILE="/tmp/jycb-z-health-report-$(date '+%Y%m%d_%H%M%S').txt"
    
    {
        echo "=========================================="
        echo "今夜城堡项目健康检查报告"
        echo "检查时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "=========================================="
        echo ""
        
        echo "检查结果统计:"
        echo "  成功: $SUCCESS_COUNT"
        echo "  警告: $WARNING_COUNT"
        echo "  失败: $FAILURE_COUNT"
        echo "  总计: $((SUCCESS_COUNT + WARNING_COUNT + FAILURE_COUNT))"
        echo ""
        
        if [[ $FAILURE_COUNT -eq 0 && $WARNING_COUNT -eq 0 ]]; then
            echo "整体状态: 健康 ✓"
        elif [[ $FAILURE_COUNT -eq 0 ]]; then
            echo "整体状态: 基本健康 ⚠"
        else
            echo "整体状态: 异常 ✗"
        fi
        echo ""
        
        echo "系统信息:"
        echo "  主机名: $(hostname)"
        echo "  操作系统: $(uname -s)"
        echo "  内核版本: $(uname -r)"
        echo "  运行时间: $(uptime | awk -F'up ' '{print $2}' | awk -F',' '{print $1}')"
        echo ""
        
        echo "应用信息:"
        echo "  项目名称: $PROJECT_NAME"
        echo "  应用地址: http://$APP_HOST:$APP_PORT"
        echo "  数据库: $DB_HOST:$DB_PORT/$DB_NAME"
        echo "  Redis: $REDIS_HOST:$REDIS_PORT"
        echo ""
        
    } > "$REPORT_FILE"
    
    log_info "健康检查报告已生成: $REPORT_FILE"
}

# 显示检查摘要
show_summary() {
    echo ""
    echo "=========================================="
    echo "健康检查摘要"
    echo "=========================================="
    echo "成功: $SUCCESS_COUNT | 警告: $WARNING_COUNT | 失败: $FAILURE_COUNT"
    echo ""
    
    if [[ $FAILURE_COUNT -eq 0 && $WARNING_COUNT -eq 0 ]]; then
        echo -e "${GREEN}整体状态: 健康 ✓${NC}"
        exit 0
    elif [[ $FAILURE_COUNT -eq 0 ]]; then
        echo -e "${YELLOW}整体状态: 基本健康 ⚠${NC}"
        exit 1
    else
        echo -e "${RED}整体状态: 异常 ✗${NC}"
        exit 2
    fi
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {all|app|db|redis|system|logs|help}"
    echo ""
    echo "命令说明:"
    echo "  all     - 执行完整健康检查（推荐）"
    echo "  app     - 仅检查应用状态"
    echo "  db      - 仅检查数据库连接"
    echo "  redis   - 仅检查Redis连接"
    echo "  system  - 仅检查系统资源"
    echo "  logs    - 仅检查日志文件"
    echo "  help    - 显示帮助信息"
    echo ""
    echo "检查项目:"
    echo "1. 应用进程状态"
    echo "2. 端口监听状态"
    echo "3. HTTP健康端点"
    echo "4. 关键API端点"
    echo "5. 数据库连接"
    echo "6. Redis连接"
    echo "7. 系统资源使用"
    echo "8. 日志文件状态"
    echo ""
    echo "退出代码:"
    echo "  0 - 所有检查通过"
    echo "  1 - 存在警告"
    echo "  2 - 存在失败"
}

# 主函数
main() {
    case "$1" in
        all)
            check_app_process
            check_port_listening
            check_health_endpoint
            check_api_endpoints
            check_database
            check_redis
            check_system_resources
            check_log_files
            generate_health_report
            show_summary
            ;;
        app)
            check_app_process
            check_port_listening
            check_health_endpoint
            check_api_endpoints
            show_summary
            ;;
        db)
            check_database
            show_summary
            ;;
        redis)
            check_redis
            show_summary
            ;;
        system)
            check_system_resources
            show_summary
            ;;
        logs)
            check_log_files
            show_summary
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
