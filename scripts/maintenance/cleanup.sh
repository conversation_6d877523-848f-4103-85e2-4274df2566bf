#!/bin/bash

# ========================================
# 今夜城堡项目清理脚本
# 项目名称: jycb-z
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"

# 清理配置
LOG_RETENTION_DAYS=30
BACKUP_RETENTION_DAYS=30
TEMP_RETENTION_DAYS=7
MAX_LOG_SIZE="100M"

# 路径配置
LOG_DIRS=(
    "/var/log/jycb-z"
    "$PROJECT_ROOT/logs"
    "/opt/jycb-z/logs"
)

BACKUP_DIRS=(
    "/opt/jycb-z/backup"
    "$PROJECT_ROOT/backup"
)

TEMP_DIRS=(
    "/tmp/jycb-z"
    "$PROJECT_ROOT/temp"
    "$PROJECT_ROOT/target"
)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    # 检查根分区
    ROOT_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $ROOT_USAGE -gt 80 ]]; then
        log_warn "根分区磁盘使用率较高: ${ROOT_USAGE}%"
    else
        log_info "根分区磁盘使用率: ${ROOT_USAGE}%"
    fi
    
    # 检查日志目录所在分区
    for log_dir in "${LOG_DIRS[@]}"; do
        if [[ -d "$log_dir" ]]; then
            LOG_USAGE=$(df "$log_dir" | awk 'NR==2 {print $5}' | sed 's/%//')
            if [[ $LOG_USAGE -gt 80 ]]; then
                log_warn "日志目录 $log_dir 所在分区使用率较高: ${LOG_USAGE}%"
            else
                log_info "日志目录 $log_dir 所在分区使用率: ${LOG_USAGE}%"
            fi
        fi
    done
}

# 清理日志文件
cleanup_logs() {
    log_info "清理日志文件..."
    
    local cleaned_files=0
    local freed_space=0
    
    for log_dir in "${LOG_DIRS[@]}"; do
        if [[ ! -d "$log_dir" ]]; then
            continue
        fi
        
        log_info "清理日志目录: $log_dir"
        
        # 清理过期的日志文件
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]]; then
                file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
                rm -f "$file"
                ((cleaned_files++))
                ((freed_space += file_size))
                log_debug "删除过期日志: $file"
            fi
        done < <(find "$log_dir" -name "*.log.*" -type f -mtime +$LOG_RETENTION_DAYS -print0 2>/dev/null)
        
        # 清理过大的当前日志文件
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]]; then
                log_warn "日志文件过大，进行轮转: $file"
                
                # 创建轮转文件
                timestamp=$(date '+%Y%m%d_%H%M%S')
                rotated_file="${file}.${timestamp}"
                
                # 移动当前日志文件
                mv "$file" "$rotated_file"
                
                # 压缩轮转的日志文件
                if command -v gzip &> /dev/null; then
                    gzip "$rotated_file"
                    log_info "日志文件已轮转并压缩: ${rotated_file}.gz"
                else
                    log_info "日志文件已轮转: $rotated_file"
                fi
                
                # 创建新的空日志文件
                touch "$file"
                chmod 644 "$file"
                
                # 如果是系统服务，尝试重新加载日志配置
                if systemctl is-active --quiet jycb-z; then
                    systemctl reload jycb-z 2>/dev/null || true
                fi
            fi
        done < <(find "$log_dir" -name "*.log" -type f -size +$MAX_LOG_SIZE -print0 2>/dev/null)
        
        # 清理空的日志文件
        find "$log_dir" -name "*.log" -type f -empty -delete 2>/dev/null
    done
    
    if [[ $cleaned_files -gt 0 ]]; then
        freed_mb=$((freed_space / 1024 / 1024))
        log_info "清理了 $cleaned_files 个日志文件，释放空间: ${freed_mb}MB"
    else
        log_info "没有需要清理的日志文件"
    fi
}

# 清理备份文件
cleanup_backups() {
    log_info "清理备份文件..."
    
    local cleaned_files=0
    local freed_space=0
    
    for backup_dir in "${BACKUP_DIRS[@]}"; do
        if [[ ! -d "$backup_dir" ]]; then
            continue
        fi
        
        log_info "清理备份目录: $backup_dir"
        
        # 清理过期的数据库备份
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]]; then
                file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
                rm -f "$file"
                ((cleaned_files++))
                ((freed_space += file_size))
                log_debug "删除过期备份: $file"
            fi
        done < <(find "$backup_dir" -name "*_backup_*.sql*" -type f -mtime +$BACKUP_RETENTION_DAYS -print0 2>/dev/null)
        
        # 清理过期的配置备份
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]]; then
                file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
                rm -f "$file"
                ((cleaned_files++))
                ((freed_space += file_size))
                log_debug "删除过期配置备份: $file"
            fi
        done < <(find "$backup_dir" -name "config_*.tar.gz" -type f -mtime +$BACKUP_RETENTION_DAYS -print0 2>/dev/null)
        
        # 清理过期的应用备份
        while IFS= read -r -d '' dir; do
            if [[ -d "$dir" ]]; then
                dir_size=$(du -sb "$dir" 2>/dev/null | cut -f1 || echo 0)
                rm -rf "$dir"
                ((cleaned_files++))
                ((freed_space += dir_size))
                log_debug "删除过期应用备份: $dir"
            fi
        done < <(find "$backup_dir" -name "20*" -type d -mtime +$BACKUP_RETENTION_DAYS -print0 2>/dev/null)
    done
    
    if [[ $cleaned_files -gt 0 ]]; then
        freed_mb=$((freed_space / 1024 / 1024))
        log_info "清理了 $cleaned_files 个备份文件，释放空间: ${freed_mb}MB"
    else
        log_info "没有需要清理的备份文件"
    fi
}

# 清理临时文件
cleanup_temp() {
    log_info "清理临时文件..."
    
    local cleaned_files=0
    local freed_space=0
    
    for temp_dir in "${TEMP_DIRS[@]}"; do
        if [[ ! -d "$temp_dir" ]]; then
            continue
        fi
        
        log_info "清理临时目录: $temp_dir"
        
        # 清理过期的临时文件
        while IFS= read -r -d '' file; do
            if [[ -f "$file" ]]; then
                file_size=$(stat -f%z "$file" 2>/dev/null || stat -c%s "$file" 2>/dev/null || echo 0)
                rm -f "$file"
                ((cleaned_files++))
                ((freed_space += file_size))
                log_debug "删除临时文件: $file"
            fi
        done < <(find "$temp_dir" -type f -mtime +$TEMP_RETENTION_DAYS -print0 2>/dev/null)
        
        # 清理空目录
        find "$temp_dir" -type d -empty -delete 2>/dev/null
    done
    
    # 清理Maven构建缓存（如果存在）
    if [[ -d "$PROJECT_ROOT/target" ]]; then
        log_info "清理Maven构建缓存..."
        
        # 保留最新的JAR文件，清理其他构建产物
        find "$PROJECT_ROOT/target" -name "*.class" -type f -delete 2>/dev/null
        find "$PROJECT_ROOT/target" -name "*.jar" -type f -mtime +7 -delete 2>/dev/null
        find "$PROJECT_ROOT/target/classes" -type f -delete 2>/dev/null
        find "$PROJECT_ROOT/target/test-classes" -type f -delete 2>/dev/null
        
        # 清理空目录
        find "$PROJECT_ROOT/target" -type d -empty -delete 2>/dev/null
    fi
    
    if [[ $cleaned_files -gt 0 ]]; then
        freed_mb=$((freed_space / 1024 / 1024))
        log_info "清理了 $cleaned_files 个临时文件，释放空间: ${freed_mb}MB"
    else
        log_info "没有需要清理的临时文件"
    fi
}

# 清理Docker资源
cleanup_docker() {
    log_info "清理Docker资源..."
    
    if ! command -v docker &> /dev/null; then
        log_info "Docker未安装，跳过Docker清理"
        return 0
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_warn "Docker服务未运行，跳过Docker清理"
        return 0
    fi
    
    # 清理停止的容器
    STOPPED_CONTAINERS=$(docker ps -a -q -f status=exited)
    if [[ -n "$STOPPED_CONTAINERS" ]]; then
        docker rm $STOPPED_CONTAINERS
        log_info "清理停止的Docker容器"
    fi
    
    # 清理未使用的镜像
    DANGLING_IMAGES=$(docker images -q -f dangling=true)
    if [[ -n "$DANGLING_IMAGES" ]]; then
        docker rmi $DANGLING_IMAGES
        log_info "清理悬空的Docker镜像"
    fi
    
    # 清理未使用的网络
    docker network prune -f > /dev/null 2>&1
    log_info "清理未使用的Docker网络"
    
    # 清理未使用的卷
    docker volume prune -f > /dev/null 2>&1
    log_info "清理未使用的Docker卷"
    
    log_info "Docker资源清理完成"
}

# 清理系统缓存
cleanup_system_cache() {
    log_info "清理系统缓存..."
    
    # 清理包管理器缓存
    if command -v apt-get &> /dev/null; then
        apt-get clean > /dev/null 2>&1
        log_info "清理APT缓存"
    fi
    
    if command -v yum &> /dev/null; then
        yum clean all > /dev/null 2>&1
        log_info "清理YUM缓存"
    fi
    
    # 清理用户缓存目录
    if [[ -d "$HOME/.cache" ]]; then
        find "$HOME/.cache" -type f -mtime +30 -delete 2>/dev/null
        log_info "清理用户缓存目录"
    fi
    
    # 清理Maven本地仓库中的过期文件
    if [[ -d "$HOME/.m2/repository" ]]; then
        find "$HOME/.m2/repository" -name "*.lastUpdated" -type f -mtime +7 -delete 2>/dev/null
        find "$HOME/.m2/repository" -name "_remote.repositories" -type f -mtime +7 -delete 2>/dev/null
        log_info "清理Maven本地仓库缓存"
    fi
}

# 生成清理报告
generate_cleanup_report() {
    log_info "生成清理报告..."
    
    REPORT_FILE="/tmp/jycb-z-cleanup-report-$(date '+%Y%m%d_%H%M%S').txt"
    
    {
        echo "=========================================="
        echo "今夜城堡项目清理报告"
        echo "生成时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo "=========================================="
        echo ""
        
        echo "磁盘空间使用情况:"
        df -h
        echo ""
        
        echo "日志目录大小:"
        for log_dir in "${LOG_DIRS[@]}"; do
            if [[ -d "$log_dir" ]]; then
                echo "  $log_dir: $(du -sh "$log_dir" 2>/dev/null | cut -f1)"
            fi
        done
        echo ""
        
        echo "备份目录大小:"
        for backup_dir in "${BACKUP_DIRS[@]}"; do
            if [[ -d "$backup_dir" ]]; then
                echo "  $backup_dir: $(du -sh "$backup_dir" 2>/dev/null | cut -f1)"
            fi
        done
        echo ""
        
        echo "清理配置:"
        echo "  日志保留天数: $LOG_RETENTION_DAYS 天"
        echo "  备份保留天数: $BACKUP_RETENTION_DAYS 天"
        echo "  临时文件保留天数: $TEMP_RETENTION_DAYS 天"
        echo "  最大日志文件大小: $MAX_LOG_SIZE"
        echo ""
        
        echo "清理完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
        
    } > "$REPORT_FILE"
    
    log_info "清理报告已生成: $REPORT_FILE"
}

# 显示清理统计
show_cleanup_stats() {
    log_info "清理统计信息:"
    echo ""
    
    echo "当前磁盘使用情况:"
    df -h | grep -E "(Filesystem|/$|/var|/opt|/tmp)"
    echo ""
    
    echo "各目录大小统计:"
    for log_dir in "${LOG_DIRS[@]}"; do
        if [[ -d "$log_dir" ]]; then
            echo "  日志目录 $log_dir: $(du -sh "$log_dir" 2>/dev/null | cut -f1)"
        fi
    done
    
    for backup_dir in "${BACKUP_DIRS[@]}"; do
        if [[ -d "$backup_dir" ]]; then
            echo "  备份目录 $backup_dir: $(du -sh "$backup_dir" 2>/dev/null | cut -f1)"
        fi
    done
    echo ""
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {all|logs|backups|temp|docker|cache|stats|help}"
    echo ""
    echo "命令说明:"
    echo "  all      - 执行完整清理（推荐）"
    echo "  logs     - 仅清理日志文件"
    echo "  backups  - 仅清理备份文件"
    echo "  temp     - 仅清理临时文件"
    echo "  docker   - 仅清理Docker资源"
    echo "  cache    - 仅清理系统缓存"
    echo "  stats    - 显示清理统计信息"
    echo "  help     - 显示帮助信息"
    echo ""
    echo "清理配置:"
    echo "  日志保留天数: $LOG_RETENTION_DAYS 天"
    echo "  备份保留天数: $BACKUP_RETENTION_DAYS 天"
    echo "  临时文件保留天数: $TEMP_RETENTION_DAYS 天"
    echo "  最大日志文件大小: $MAX_LOG_SIZE"
    echo ""
    echo "注意事项:"
    echo "1. 建议定期运行清理脚本以释放磁盘空间"
    echo "2. 清理前会自动检查磁盘空间使用情况"
    echo "3. 重要备份文件不会被清理"
    echo "4. 可以通过修改脚本配置调整保留策略"
}

# 主函数
main() {
    case "$1" in
        all)
            check_disk_space
            cleanup_logs
            cleanup_backups
            cleanup_temp
            cleanup_docker
            cleanup_system_cache
            generate_cleanup_report
            show_cleanup_stats
            ;;
        logs)
            cleanup_logs
            ;;
        backups)
            cleanup_backups
            ;;
        temp)
            cleanup_temp
            ;;
        docker)
            cleanup_docker
            ;;
        cache)
            cleanup_system_cache
            ;;
        stats)
            check_disk_space
            show_cleanup_stats
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
