#!/bin/bash

# ========================================
# 今夜城堡项目构建脚本
# 项目名称: jycb-z
# 作者: System
# 创建时间: $(date '+%Y-%m-%d %H:%M:%S')
# ========================================

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
PROJECT_NAME="jycb-z"

# 构建配置
MAVEN_OPTS="-Xmx2g -XX:+UseG1GC"
SKIP_TESTS=false
CLEAN_BUILD=true
PROFILE="dev"
PARALLEL_BUILDS=true

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查构建环境
check_build_environment() {
    log_info "检查构建环境..."
    
    # 检查Java环境
    if ! command -v java &> /dev/null; then
        log_error "Java未安装，请先安装Java 17或更高版本"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | awk -F '"' '{print $2}' | awk -F '.' '{print $1}')
    if [[ $JAVA_VERSION -lt 17 ]]; then
        log_error "Java版本过低，当前版本: $JAVA_VERSION，需要Java 17或更高版本"
        exit 1
    fi
    
    log_info "Java环境: $(java -version 2>&1 | head -n1)"
    
    # 检查Maven环境
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，请先安装Maven"
        exit 1
    fi
    
    log_info "Maven环境: $(mvn --version | head -n1)"
    
    # 检查项目结构
    if [[ ! -f "$PROJECT_ROOT/pom.xml" ]]; then
        log_error "未找到pom.xml文件，请确保在正确的项目目录中"
        exit 1
    fi
    
    log_info "项目根目录: $PROJECT_ROOT"
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    AVAILABLE_SPACE=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    REQUIRED_SPACE=1048576  # 1GB in KB
    
    if [[ $AVAILABLE_SPACE -lt $REQUIRED_SPACE ]]; then
        log_warn "磁盘空间不足，可用空间: $((AVAILABLE_SPACE/1024))MB，建议至少1GB"
    else
        log_info "磁盘空间充足，可用空间: $((AVAILABLE_SPACE/1024))MB"
    fi
}

# 清理构建目录
clean_build_directory() {
    if [[ "$CLEAN_BUILD" == "true" ]]; then
        log_info "清理构建目录..."
        
        cd "$PROJECT_ROOT"
        mvn clean -q
        
        if [[ $? -eq 0 ]]; then
            log_info "构建目录清理完成"
        else
            log_error "构建目录清理失败"
            exit 1
        fi
    else
        log_info "跳过构建目录清理"
    fi
}

# 下载依赖
download_dependencies() {
    log_info "下载项目依赖..."
    
    cd "$PROJECT_ROOT"
    
    # 构建Maven命令
    MVN_CMD="mvn dependency:resolve"
    if [[ "$PARALLEL_BUILDS" == "true" ]]; then
        MVN_CMD="$MVN_CMD -T 1C"
    fi
    MVN_CMD="$MVN_CMD -q"
    
    eval "$MVN_CMD"
    
    if [[ $? -eq 0 ]]; then
        log_info "依赖下载完成"
    else
        log_error "依赖下载失败"
        exit 1
    fi
}

# 编译源代码
compile_sources() {
    log_info "编译源代码..."
    
    cd "$PROJECT_ROOT"
    
    # 构建Maven命令
    MVN_CMD="mvn compile"
    if [[ "$PARALLEL_BUILDS" == "true" ]]; then
        MVN_CMD="$MVN_CMD -T 1C"
    fi
    MVN_CMD="$MVN_CMD -q"
    
    eval "$MVN_CMD"
    
    if [[ $? -eq 0 ]]; then
        log_info "源代码编译完成"
    else
        log_error "源代码编译失败"
        exit 1
    fi
}

# 编译测试代码
compile_tests() {
    log_info "编译测试代码..."
    
    cd "$PROJECT_ROOT"
    
    # 构建Maven命令
    MVN_CMD="mvn test-compile"
    if [[ "$PARALLEL_BUILDS" == "true" ]]; then
        MVN_CMD="$MVN_CMD -T 1C"
    fi
    MVN_CMD="$MVN_CMD -q"
    
    eval "$MVN_CMD"
    
    if [[ $? -eq 0 ]]; then
        log_info "测试代码编译完成"
    else
        log_error "测试代码编译失败"
        exit 1
    fi
}

# 运行单元测试
run_unit_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_info "跳过单元测试"
        return 0
    fi
    
    log_info "运行单元测试..."
    
    cd "$PROJECT_ROOT"
    
    # 构建Maven命令
    MVN_CMD="mvn test"
    if [[ "$PARALLEL_BUILDS" == "true" ]]; then
        MVN_CMD="$MVN_CMD -T 1C"
    fi
    MVN_CMD="$MVN_CMD -Dspring.profiles.active=$PROFILE"
    
    eval "$MVN_CMD"
    
    if [[ $? -eq 0 ]]; then
        log_info "单元测试通过"
        
        # 显示测试报告
        if [[ -f "$PROJECT_ROOT/target/surefire-reports/TEST-*.xml" ]]; then
            TEST_COUNT=$(grep -h "tests=" "$PROJECT_ROOT/target/surefire-reports/TEST-"*.xml | \
                        sed 's/.*tests="\([0-9]*\)".*/\1/' | \
                        awk '{sum += $1} END {print sum}')
            FAILURE_COUNT=$(grep -h "failures=" "$PROJECT_ROOT/target/surefire-reports/TEST-"*.xml | \
                           sed 's/.*failures="\([0-9]*\)".*/\1/' | \
                           awk '{sum += $1} END {print sum}')
            ERROR_COUNT=$(grep -h "errors=" "$PROJECT_ROOT/target/surefire-reports/TEST-"*.xml | \
                         sed 's/.*errors="\([0-9]*\)".*/\1/' | \
                         awk '{sum += $1} END {print sum}')
            
            log_info "测试统计: 总计 $TEST_COUNT，失败 $FAILURE_COUNT，错误 $ERROR_COUNT"
        fi
    else
        log_error "单元测试失败"
        
        # 显示失败的测试
        if [[ -f "$PROJECT_ROOT/target/surefire-reports/TEST-*.xml" ]]; then
            log_error "失败的测试:"
            grep -h "failure\|error" "$PROJECT_ROOT/target/surefire-reports/TEST-"*.xml | head -5
        fi
        
        exit 1
    fi
}

# 打包应用
package_application() {
    log_info "打包应用..."
    
    cd "$PROJECT_ROOT"
    
    # 构建Maven命令
    MVN_CMD="mvn package"
    if [[ "$SKIP_TESTS" == "true" ]]; then
        MVN_CMD="$MVN_CMD -DskipTests"
    fi
    if [[ "$PARALLEL_BUILDS" == "true" ]]; then
        MVN_CMD="$MVN_CMD -T 1C"
    fi
    MVN_CMD="$MVN_CMD -Dspring.profiles.active=$PROFILE"
    MVN_CMD="$MVN_CMD -q"
    
    eval "$MVN_CMD"
    
    if [[ $? -eq 0 ]]; then
        log_info "应用打包完成"
        
        # 查找生成的JAR文件
        JAR_FILE=$(find "$PROJECT_ROOT/target" -name "*.jar" -not -name "*-sources.jar" -not -name "*-javadoc.jar" | head -n1)
        if [[ -n "$JAR_FILE" && -f "$JAR_FILE" ]]; then
            JAR_SIZE=$(du -h "$JAR_FILE" | cut -f1)
            log_info "生成JAR文件: $(basename "$JAR_FILE") ($JAR_SIZE)"
        else
            log_error "未找到生成的JAR文件"
            exit 1
        fi
    else
        log_error "应用打包失败"
        exit 1
    fi
}

# 生成文档
generate_docs() {
    log_info "生成项目文档..."
    
    cd "$PROJECT_ROOT"
    
    # 生成Javadoc
    mvn javadoc:javadoc -q
    
    if [[ $? -eq 0 ]]; then
        log_info "Javadoc生成完成"
        if [[ -d "$PROJECT_ROOT/target/site/apidocs" ]]; then
            log_info "Javadoc位置: target/site/apidocs/index.html"
        fi
    else
        log_warn "Javadoc生成失败"
    fi
    
    # 生成源码JAR
    mvn source:jar -q
    
    if [[ $? -eq 0 ]]; then
        log_info "源码JAR生成完成"
    else
        log_warn "源码JAR生成失败"
    fi
}

# 代码质量检查
run_code_quality_checks() {
    log_info "运行代码质量检查..."
    
    cd "$PROJECT_ROOT"
    
    # 检查是否配置了SpotBugs
    if grep -q "spotbugs-maven-plugin" "$PROJECT_ROOT/pom.xml"; then
        log_info "运行SpotBugs检查..."
        mvn spotbugs:check -q
        
        if [[ $? -eq 0 ]]; then
            log_info "SpotBugs检查通过"
        else
            log_warn "SpotBugs检查发现问题"
        fi
    fi
    
    # 检查是否配置了Checkstyle
    if grep -q "maven-checkstyle-plugin" "$PROJECT_ROOT/pom.xml"; then
        log_info "运行Checkstyle检查..."
        mvn checkstyle:check -q
        
        if [[ $? -eq 0 ]]; then
            log_info "Checkstyle检查通过"
        else
            log_warn "Checkstyle检查发现问题"
        fi
    fi
}

# 显示构建信息
show_build_info() {
    log_info "构建信息:"
    echo ""
    echo "项目信息:"
    echo "  名称: $PROJECT_NAME"
    echo "  目录: $PROJECT_ROOT"
    echo "  配置文件: $PROFILE"
    echo ""
    echo "构建配置:"
    echo "  清理构建: $CLEAN_BUILD"
    echo "  跳过测试: $SKIP_TESTS"
    echo "  并行构建: $PARALLEL_BUILDS"
    echo "  Maven选项: $MAVEN_OPTS"
    echo ""
    
    # 显示生成的文件
    if [[ -d "$PROJECT_ROOT/target" ]]; then
        echo "生成的文件:"
        find "$PROJECT_ROOT/target" -maxdepth 1 -name "*.jar" -exec ls -lh {} \;
        echo ""
    fi
    
    echo "构建完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
}

# 快速构建（跳过测试和文档）
quick_build() {
    log_info "执行快速构建..."
    
    check_build_environment
    clean_build_directory
    
    cd "$PROJECT_ROOT"
    mvn package -DskipTests -T 1C -q
    
    if [[ $? -eq 0 ]]; then
        log_info "快速构建完成"
        show_build_info
    else
        log_error "快速构建失败"
        exit 1
    fi
}

# 完整构建
full_build() {
    log_info "执行完整构建..."
    
    check_build_environment
    check_disk_space
    clean_build_directory
    download_dependencies
    compile_sources
    compile_tests
    run_unit_tests
    package_application
    generate_docs
    run_code_quality_checks
    show_build_info
}

# 仅编译
compile_only() {
    log_info "仅编译源代码..."
    
    check_build_environment
    compile_sources
    compile_tests
    
    log_info "编译完成"
}

# 仅测试
test_only() {
    log_info "仅运行测试..."
    
    check_build_environment
    run_unit_tests
    
    log_info "测试完成"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 {build|quick|compile|test|docs|clean|help} [选项]"
    echo ""
    echo "命令说明:"
    echo "  build    - 完整构建（编译+测试+打包+文档）"
    echo "  quick    - 快速构建（跳过测试和文档）"
    echo "  compile  - 仅编译源代码"
    echo "  test     - 仅运行测试"
    echo "  docs     - 仅生成文档"
    echo "  clean    - 清理构建目录"
    echo "  help     - 显示帮助信息"
    echo ""
    echo "选项:"
    echo "  --skip-tests     跳过单元测试"
    echo "  --no-clean       不清理构建目录"
    echo "  --profile=<env>  指定构建配置文件 (dev/test/prod)"
    echo "  --no-parallel    禁用并行构建"
    echo ""
    echo "示例:"
    echo "  $0 build                    # 完整构建"
    echo "  $0 quick                    # 快速构建"
    echo "  $0 build --skip-tests       # 构建但跳过测试"
    echo "  $0 build --profile=prod     # 使用生产配置构建"
    echo ""
    echo "环境变量:"
    echo "  MAVEN_OPTS=\"$MAVEN_OPTS\""
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --no-clean)
                CLEAN_BUILD=false
                shift
                ;;
            --profile=*)
                PROFILE="${1#*=}"
                shift
                ;;
            --no-parallel)
                PARALLEL_BUILDS=false
                shift
                ;;
            *)
                # 未知参数，忽略
                shift
                ;;
        esac
    done
}

# 主函数
main() {
    # 设置Maven选项
    export MAVEN_OPTS="$MAVEN_OPTS"
    
    # 解析除第一个参数外的所有参数
    if [[ $# -gt 1 ]]; then
        parse_arguments "${@:2}"
    fi
    
    case "$1" in
        build)
            full_build
            ;;
        quick)
            quick_build
            ;;
        compile)
            compile_only
            ;;
        test)
            test_only
            ;;
        docs)
            check_build_environment
            generate_docs
            ;;
        clean)
            check_build_environment
            clean_build_directory
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "无效的命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
