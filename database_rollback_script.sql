-- ========================================
-- 财务系统数据库回滚脚本
-- 创建时间: 2025-07-17
-- 说明: 回滚财务系统数据库修复操作
-- ========================================

-- 设置安全模式
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 删除添加的触发器
-- ========================================
DROP TRIGGER IF EXISTS `tr_finance_account_update_revenue`;

-- ========================================
-- 2. 删除添加的约束
-- ========================================
ALTER TABLE `jy_finance_account` DROP CONSTRAINT IF EXISTS `chk_account_type`;
ALTER TABLE `jy_finance_account` DROP CONSTRAINT IF EXISTS `chk_available_balance`;
ALTER TABLE `jy_finance_account` DROP CONSTRAINT IF EXISTS `chk_frozen_balance`;
ALTER TABLE `jy_finance_account` DROP CONSTRAINT IF EXISTS `chk_account_status`;

ALTER TABLE `jy_finance_log` DROP CONSTRAINT IF EXISTS `chk_log_account_type`;
ALTER TABLE `jy_finance_record` DROP CONSTRAINT IF EXISTS `chk_record_account_type`;
ALTER TABLE `jy_finance_record` DROP CONSTRAINT IF EXISTS `chk_record_status`;
ALTER TABLE `jy_finance_transaction` DROP CONSTRAINT IF EXISTS `chk_transaction_account_type`;

-- ========================================
-- 3. 删除添加的索引
-- ========================================

-- jy_finance_log 表索引
ALTER TABLE `jy_finance_log` 
DROP INDEX IF EXISTS `idx_account_type_id_time`,
DROP INDEX IF EXISTS `idx_amount`,
DROP INDEX IF EXISTS `idx_type_time`;

-- jy_finance_record 表索引
ALTER TABLE `jy_finance_record` 
DROP INDEX IF EXISTS `idx_account_type_id_time`,
DROP INDEX IF EXISTS `idx_type_status`,
DROP INDEX IF EXISTS `idx_amount`,
DROP INDEX IF EXISTS `idx_operator`;

-- jy_finance_transaction 表索引
ALTER TABLE `jy_finance_transaction` 
DROP INDEX IF EXISTS `idx_account_type_id_time`,
DROP INDEX IF EXISTS `idx_transaction_type_time`,
DROP INDEX IF EXISTS `idx_amount`;

-- jy_finance_account 表索引
ALTER TABLE `jy_finance_account` 
DROP INDEX IF EXISTS `idx_balance`,
DROP INDEX IF EXISTS `idx_status_type`;

-- ========================================
-- 4. 恢复金额字段精度为原始值
-- ========================================

-- 恢复 jy_finance_account 表金额字段
ALTER TABLE `jy_finance_account` 
MODIFY COLUMN `total_revenue` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总收入金额',
MODIFY COLUMN `available_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
MODIFY COLUMN `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
MODIFY COLUMN `total_withdraw` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '已提现总额',
MODIFY COLUMN `device_fee` decimal(10,2) DEFAULT '0.00' COMMENT '设备费',
MODIFY COLUMN `system_fee` decimal(10,2) DEFAULT '0.00' COMMENT '系统使用费';

-- 恢复 jy_finance_log 表金额字段
ALTER TABLE `jy_finance_log` 
MODIFY COLUMN `amount` decimal(10,2) NOT NULL COMMENT '金额',
MODIFY COLUMN `before_balance` decimal(10,2) NOT NULL COMMENT '变动前余额',
MODIFY COLUMN `after_balance` decimal(10,2) NOT NULL COMMENT '变动后余额';

-- 恢复 jy_finance_record 表金额字段
ALTER TABLE `jy_finance_record` 
MODIFY COLUMN `amount` decimal(10,2) NOT NULL COMMENT '金额',
MODIFY COLUMN `fee` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
MODIFY COLUMN `before_balance` decimal(10,2) NOT NULL COMMENT '操作前余额',
MODIFY COLUMN `after_balance` decimal(10,2) NOT NULL COMMENT '操作后余额';

-- 恢复 jy_finance_transaction 表金额字段
ALTER TABLE `jy_finance_transaction` 
MODIFY COLUMN `amount` decimal(10,2) NOT NULL COMMENT '交易金额',
MODIFY COLUMN `balance` decimal(10,2) NOT NULL COMMENT '交易后余额';

-- ========================================
-- 5. 恢复账户类型为大写（jy_finance_transaction表）
-- ========================================
UPDATE `jy_finance_transaction` SET `account_type` = 'PLATFORM' WHERE `account_type` = 'platform';
UPDATE `jy_finance_transaction` SET `account_type` = 'ENTITY' WHERE `account_type` = 'entity';
UPDATE `jy_finance_transaction` SET `account_type` = 'PARTNER' WHERE `account_type` = 'partner';
UPDATE `jy_finance_transaction` SET `account_type` = 'SHOP' WHERE `account_type` = 'shop';
UPDATE `jy_finance_transaction` SET `account_type` = 'USER' WHERE `account_type` = 'user';
UPDATE `jy_finance_transaction` SET `account_type` = 'SYSTEM' WHERE `account_type` = 'system';

-- 恢复字段注释
ALTER TABLE `jy_finance_transaction` 
MODIFY COLUMN `account_type` varchar(50) NOT NULL COMMENT '账户类型：PLATFORM-平台账户，ENTITY-业务主体账户，PARTNER-合作商账户，SHOP-门店账户，USER-用户账户';

-- ========================================
-- 6. 恢复字段类型为原始值
-- ========================================

-- 恢复 jy_finance_log 表的字段类型
ALTER TABLE `jy_finance_log` 
MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '流水ID',
MODIFY COLUMN `account_id` int(11) NOT NULL COMMENT '账户ID',
MODIFY COLUMN `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
MODIFY COLUMN `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
MODIFY COLUMN `shop_id` int(11) DEFAULT NULL COMMENT '门店ID',
MODIFY COLUMN `user_id` int(11) DEFAULT NULL COMMENT '用户ID',
MODIFY COLUMN `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID';

-- 恢复 jy_finance_account 表的字段类型
ALTER TABLE `jy_finance_account` 
MODIFY COLUMN `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
MODIFY COLUMN `account_id` int(11) NOT NULL COMMENT '账户所属ID',
MODIFY COLUMN `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
MODIFY COLUMN `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
MODIFY COLUMN `shop_id` int(11) DEFAULT NULL COMMENT '门店ID';

-- ========================================
-- 7. 恢复安全设置
-- ========================================
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- ========================================
-- 回滚完成提示
-- ========================================
SELECT 'Database rollback completed successfully!' AS status;
