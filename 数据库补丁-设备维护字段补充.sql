-- 设备维护申请表字段补充
-- 添加缺失的字段以支持完整的维护申请功能

-- 检查并添加设备编号字段
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `device_no` VARCHAR(50) NULL COMMENT '设备编号' AFTER `device_id`;

-- 检查并添加业务主体ID字段
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `entity_id` INT NULL COMMENT '业务主体ID' AFTER `device_no`;

-- 检查并添加合作商ID字段
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `partner_id` INT NULL COMMENT '合作商ID' AFTER `entity_id`;

-- 检查并添加维护原因字段（替换description字段的作用）
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `maintenance_reason` TEXT NULL COMMENT '维护原因' AFTER `maintenance_type`;

-- 检查并添加期望维护时间字段（替换preferred_time字段的作用）
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `expected_time` DATETIME NULL COMMENT '期望维护时间' AFTER `maintenance_reason`;

-- 检查并添加申请人ID字段
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `applicant_id` INT NULL COMMENT '申请人ID' AFTER `expected_time`;

-- 检查并添加申请人姓名字段
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `applicant_name` VARCHAR(100) NULL COMMENT '申请人姓名' AFTER `applicant_id`;

-- 检查并添加联系电话字段
ALTER TABLE `jy_device_maintenance_request` 
ADD COLUMN IF NOT EXISTS `contact_phone` VARCHAR(20) NULL COMMENT '联系电话' AFTER `applicant_name`;

-- 添加索引以提高查询性能
ALTER TABLE `jy_device_maintenance_request` 
ADD INDEX IF NOT EXISTS `idx_device_no` (`device_no`);

ALTER TABLE `jy_device_maintenance_request` 
ADD INDEX IF NOT EXISTS `idx_entity_id` (`entity_id`);

ALTER TABLE `jy_device_maintenance_request` 
ADD INDEX IF NOT EXISTS `idx_partner_id` (`partner_id`);

ALTER TABLE `jy_device_maintenance_request` 
ADD INDEX IF NOT EXISTS `idx_applicant_id` (`applicant_id`);

ALTER TABLE `jy_device_maintenance_request` 
ADD INDEX IF NOT EXISTS `idx_expected_time` (`expected_time`);

-- 更新表注释
ALTER TABLE `jy_device_maintenance_request` 
COMMENT = '设备维护申请表 - 支持完整的维护申请流程';
