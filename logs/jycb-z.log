2025-08-01T04:16:22.626+08:00  INFO 38752 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 38752 (C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z)
2025-08-01T04:16:22.629+08:00 DEBUG 38752 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:16:22.629+08:00  INFO 38752 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:16:22.659+08:00  INFO 38752 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:16:22.660+08:00  INFO 38752 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:16:23.503+08:00  INFO 38752 --- [jycb-z] [restartedMain] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'cacheManager' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisCacheConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisCacheConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisConfig.class]]
2025-08-01T04:16:24.749+08:00  INFO 38752 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T04:16:24.763+08:00  INFO 38752 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T04:16:24.763+08:00  INFO 38752 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T04:16:24.803+08:00  INFO 38752 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T04:16:24.803+08:00  INFO 38752 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2143 ms
2025-08-01T04:16:25.107+08:00  WARN 38752 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataPermissionAspect' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\common\aspect\DataPermissionAspect.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'adminModuleServiceImpl' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\service\impl\AdminServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'adminMapper' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\mapper\AdminMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
2025-08-01T04:16:25.110+08:00  INFO 38752 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T04:16:25.120+08:00  INFO 38752 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T04:16:25.137+08:00 ERROR 38752 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataPermissionAspect' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\common\aspect\DataPermissionAspect.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'adminModuleServiceImpl' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\service\impl\AdminServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'adminMapper' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\mapper\AdminMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminModuleServiceImpl' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\service\impl\AdminServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'adminMapper' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\mapper\AdminMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminMapper' defined in file [C:\Users\<USER>\Desktop\今夜城堡-正式项目-前端-后端\后端\jycb-z\target\classes\com\jycb\jycbz\modules\admin\mapper\AdminMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1683) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791) ~[spring-beans-6.2.8.jar:6.2.8]
	... 40 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541) ~[spring-beans-6.2.8.jar:6.2.8]
	... 51 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 64 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: The alias 'Menu' is already mapped to the value 'com.jycb.jycbz.modules.menu.entity.Menu'.
	at org.apache.ibatis.type.TypeAliasRegistry.registerAlias(TypeAliasRegistry.java:166) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.registerAlias(TypeAliasRegistry.java:155) ~[mybatis-3.5.15.jar:3.5.15]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:179) ~[na:na]
	at java.base/java.util.HashMap$KeySpliterator.forEachRemaining(HashMap.java:1707) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:485) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 67 common frames omitted

2025-08-01T04:19:48.752+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 43776 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T04:19:48.753+08:00 DEBUG 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:19:48.754+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:19:48.778+08:00  INFO 43776 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:19:48.779+08:00  INFO 43776 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:19:49.462+08:00  INFO 43776 --- [jycb-z] [restartedMain] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'cacheManager' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisCacheConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisCacheConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisConfig.class]]
2025-08-01T04:19:50.280+08:00  INFO 43776 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T04:19:50.287+08:00  INFO 43776 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T04:19:50.288+08:00  INFO 43776 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T04:19:50.317+08:00  INFO 43776 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T04:19:50.317+08:00  INFO 43776 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1538 ms
2025-08-01T04:19:51.429+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ��΢��С�������...
2025-08-01T04:19:51.429+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T04:19:51.429+08:00 DEBUG 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T04:19:51.455+08:00 DEBUG 43776 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:19:51.462+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T04:19:52.081+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62163023
2025-08-01T04:19:52.082+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T04:19:52.226+08:00 DEBUG 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T04:19:52.227+08:00 DEBUG 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T04:19:52.227+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T04:19:52.227+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T04:19:52.227+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T04:19:52.227+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T04:19:52.227+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T04:19:52.227+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T04:19:52.375+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С�������ó�ʼ�����
2025-08-01T04:19:52.406+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttpClient, ���ӳ�ʱ: 10��, ����ʱ: 10��, д��ʱ: 10��
2025-08-01T04:19:52.455+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttp���ӳ�, ����������: 5, ��������ʱ��: 5����
2025-08-01T04:19:52.467+08:00 DEBUG 43776 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:19:52.531+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����ݿ����΢��֧�����óɹ�: mchId=17****72, appId=wx****76, serialNo=15****1E
2025-08-01T04:19:52.532+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ������ݿ����˽Կ������: 1732
2025-08-01T04:19:52.532+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ�����
2025-08-01T04:19:53.199+08:00  WARN 43776 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'systemConfigInitializer' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\config\SystemConfigInitializer.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'wxPayServiceImpl': Unsatisfied dependency expressed through field 'deviceService': Error creating bean with name 'deviceServiceImpl': Invocation of init method failed
2025-08-01T04:19:53.207+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T04:19:53.673+08:00  INFO 43776 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T04:19:53.676+08:00  INFO 43776 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T04:19:53.685+08:00  INFO 43776 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T04:19:53.695+08:00 ERROR 43776 --- [jycb-z] [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Field deviceService in com.jycb.jycbz.modules.api.service.impl.WxPayServiceImpl required a bean named 'cleanModuleCleanTaskServiceImpl' that could not be found.


Action:

Consider defining a bean named 'cleanModuleCleanTaskServiceImpl' in your configuration.

2025-08-01T04:22:28.085+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 13580 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T04:22:28.086+08:00 DEBUG 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:22:28.087+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:22:28.110+08:00  INFO 13580 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:22:28.110+08:00  INFO 13580 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:22:28.802+08:00  INFO 13580 --- [jycb-z] [restartedMain] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'cacheManager' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisCacheConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisCacheConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisConfig.class]]
2025-08-01T04:22:29.598+08:00  INFO 13580 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T04:22:29.606+08:00  INFO 13580 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T04:22:29.606+08:00  INFO 13580 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T04:22:29.636+08:00  INFO 13580 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T04:22:29.637+08:00  INFO 13580 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1526 ms
2025-08-01T04:22:30.631+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ��΢��С�������...
2025-08-01T04:22:30.631+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T04:22:30.631+08:00 DEBUG 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T04:22:30.656+08:00 DEBUG 13580 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:22:30.662+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T04:22:31.302+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@43b6ef51
2025-08-01T04:22:31.303+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T04:22:31.417+08:00 DEBUG 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T04:22:31.417+08:00 DEBUG 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T04:22:31.419+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T04:22:31.419+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T04:22:31.419+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T04:22:31.420+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T04:22:31.420+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T04:22:31.420+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T04:22:31.519+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С�������ó�ʼ�����
2025-08-01T04:22:31.549+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttpClient, ���ӳ�ʱ: 10��, ����ʱ: 10��, д��ʱ: 10��
2025-08-01T04:22:31.583+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttp���ӳ�, ����������: 5, ��������ʱ��: 5����
2025-08-01T04:22:31.595+08:00 DEBUG 13580 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:22:31.663+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����ݿ����΢��֧�����óɹ�: mchId=17****72, appId=wx****76, serialNo=15****1E
2025-08-01T04:22:31.664+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ������ݿ����˽Կ������: 1732
2025-08-01T04:22:31.664+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ�����
2025-08-01T04:22:33.227+08:00  WARN 13580 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'adminMenuController' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\modules\menu\controller\AdminMenuController.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'com.jycb.jycbz.modules.menu.service.MenuService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01T04:22:33.238+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T04:22:33.241+08:00  INFO 13580 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T04:22:33.244+08:00  INFO 13580 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T04:22:33.256+08:00  INFO 13580 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T04:22:33.267+08:00 ERROR 13580 --- [jycb-z] [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.jycb.jycbz.modules.menu.controller.AdminMenuController required a bean of type 'com.jycb.jycbz.modules.menu.service.MenuService' that could not be found.


Action:

Consider defining a bean of type 'com.jycb.jycbz.modules.menu.service.MenuService' in your configuration.

2025-08-01T04:35:18.340+08:00  INFO 3208 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 3208 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T04:35:18.341+08:00 DEBUG 3208 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:35:18.342+08:00  INFO 3208 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:35:18.368+08:00  INFO 3208 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:35:18.369+08:00  INFO 3208 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:35:18.759+08:00  WARN 3208 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.jycb.jycbz.JycbZApplication]
2025-08-01T04:35:18.761+08:00  INFO 3208 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T04:35:18.772+08:00 ERROR 3208 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanDefinitionStoreException: Failed to parse configuration class [com.jycb.jycbz.JycbZApplication]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:194) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:418) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'menuServiceImpl' for bean class [com.jycb.jycbz.modules.system.service.impl.MenuServiceImpl] conflicts with existing, non-compatible bean definition of same name and class [com.jycb.jycbz.modules.menu.service.impl.MenuServiceImpl]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:346) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:281) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:204) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:172) ~[spring-context-6.2.8.jar:6.2.8]
	... 18 common frames omitted

2025-08-01T04:36:07.255+08:00  INFO 40868 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 40868 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T04:36:07.256+08:00 DEBUG 40868 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:36:07.257+08:00  INFO 40868 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:36:07.278+08:00  INFO 40868 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:36:07.278+08:00  INFO 40868 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:36:07.936+08:00  INFO 40868 --- [jycb-z] [restartedMain] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'cacheManager' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisCacheConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisCacheConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisConfig.class]]
2025-08-01T04:36:08.204+08:00  WARN 40868 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'menuMapper' for bean class [com.jycb.jycbz.modules.system.mapper.MenuMapper] conflicts with existing, non-compatible bean definition of same name and class [com.jycb.jycbz.modules.menu.mapper.MenuMapper]
2025-08-01T04:36:08.209+08:00  INFO 40868 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T04:36:08.220+08:00 ERROR 40868 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.context.annotation.ConflictingBeanDefinitionException: Annotation-specified bean name 'menuMapper' for bean class [com.jycb.jycbz.modules.system.mapper.MenuMapper] conflicts with existing, non-compatible bean definition of same name and class [com.jycb.jycbz.modules.menu.mapper.MenuMapper]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.checkCandidate(ClassPathBeanDefinitionScanner.java:361) ~[spring-context-6.2.8.jar:6.2.8]
	at org.mybatis.spring.mapper.ClassPathMapperScanner.checkCandidate(ClassPathMapperScanner.java:345) ~[mybatis-spring-3.0.3.jar:3.0.3]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:288) ~[spring-context-6.2.8.jar:6.2.8]
	at org.mybatis.spring.mapper.ClassPathMapperScanner.doScan(ClassPathMapperScanner.java:230) ~[mybatis-spring-3.0.3.jar:3.0.3]
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.scan(ClassPathBeanDefinitionScanner.java:255) ~[spring-context-6.2.8.jar:6.2.8]
	at org.mybatis.spring.mapper.MapperScannerConfigurer.postProcessBeanDefinitionRegistry(MapperScannerConfigurer.java:381) ~[mybatis-spring-3.0.3.jar:3.0.3]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:148) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:791) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:609) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]

2025-08-01T04:40:46.188+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 43460 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T04:40:46.189+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:40:46.190+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:40:46.215+08:00  INFO 43460 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:40:46.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:40:46.939+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'cacheManager' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisCacheConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisCacheConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisConfig.class]]
2025-08-01T04:40:47.735+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T04:40:47.743+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T04:40:47.743+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T04:40:47.771+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T04:40:47.771+08:00  INFO 43460 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1555 ms
2025-08-01T04:40:48.810+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ��΢��С�������...
2025-08-01T04:40:48.810+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T04:40:48.811+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T04:40:48.839+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:48.845+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T04:40:49.428+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@a1243af
2025-08-01T04:40:49.429+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T04:40:49.562+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T04:40:49.563+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T04:40:49.563+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T04:40:49.563+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T04:40:49.563+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T04:40:49.563+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T04:40:49.563+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T04:40:49.563+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T04:40:49.666+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С�������ó�ʼ�����
2025-08-01T04:40:49.699+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttpClient, ���ӳ�ʱ: 10��, ����ʱ: 10��, д��ʱ: 10��
2025-08-01T04:40:49.736+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttp���ӳ�, ����������: 5, ��������ʱ��: 5����
2025-08-01T04:40:49.748+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:49.799+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����ݿ����΢��֧�����óɹ�: mchId=17****72, appId=wx****76, serialNo=15****1E
2025-08-01T04:40:49.799+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ������ݿ����˽Կ������: 1732
2025-08-01T04:40:49.799+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ�����
2025-08-01T04:40:52.257+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.RedisHealthConfig    : Redis���Ӽ��ɹ������湦��������
2025-08-01T04:40:52.272+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ����΢��֧��HttpClient������
2025-08-01T04:40:52.272+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T04:40:52.273+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T04:40:52.274+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T04:40:52.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T04:40:52.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAConfig...
2025-08-01T04:40:52.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T04:40:52.865+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ�
2025-08-01T04:40:52.866+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧��HttpClient�����ɹ�
2025-08-01T04:40:52.869+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ʹ��RSAAutoCertificateConfig����΢��֧������
2025-08-01T04:40:52.869+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T04:40:52.869+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T04:40:52.869+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T04:40:52.869+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T04:40:52.870+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAAutoCertificateConfig...
2025-08-01T04:40:52.870+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T04:40:53.097+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ���Bean����: wxPayConfigBean
2025-08-01T04:40:53.098+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ���ɹ�
2025-08-01T04:40:53.100+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.system.config.SystemTaskConfig   : ϵͳ��ʱ�����������ʼ����ɣ��̳߳ش�С: 0
2025-08-01T04:40:53.225+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �û���������������ע��
2025-08-01T04:40:53.225+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �豸����Ȩ����֤��������ע��
2025-08-01T04:40:53.794+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01T04:40:53.890+08:00  INFO 43460 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8081 (http) with context path '/'
2025-08-01T04:40:53.900+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Started JycbZApplication in 7.947 seconds (process running for 8.245)
2025-08-01T04:40:53.901+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.DocumentationConfig  : ----------------------------------------------------------
	Ӧ�ó���"jycb-z"����������......
	�ӿ��ĵ����� URL:
	����: 		http://localhost:8081/doc.html
	�ⲿ: 	http://*************:8081/doc.html
	�����ļ�: 	[dev]
----------------------------------------------------------
2025-08-01T04:40:53.902+08:00  INFO 43460 --- [jycb-z] [scheduled-task-1] c.j.j.m.d.task.DeviceStatusCheckTask     : ��ʼִ���豸״̬�������...
2025-08-01T04:40:53.902+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.t.CommissionCacheRefreshTask   : ��ʼ���ֳ����û��潡��״̬...
2025-08-01T04:40:53.902+08:00  INFO 43460 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderRepairTask       : ��ʼִ�ж����޸�����...
2025-08-01T04:40:53.903+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : === �豸ģ�������޸���������ʼִ�� ===
2025-08-01T04:40:53.903+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : ��ʼ���������豸��ػ���...
2025-08-01T04:40:53.904+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:53.904+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:53.963+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.jycb.jycbz.config.RedisHealthConfig    : Redis���ӽ������ͨ��
2025-08-01T04:40:54.005+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.t.CommissionCacheRefreshTask   : �ֳ����û��潡��״̬���ͨ��
2025-08-01T04:40:54.036+08:00  INFO 43460 --- [jycb-z] [scheduled-task-4] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ����δ֧��������ʱ����
2025-08-01T04:40:54.036+08:00  INFO 43460 --- [jycb-z] [scheduled-task-1] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0̨ʹ���е��豸
2025-08-01T04:40:54.037+08:00  INFO 43460 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderRepairTask       : ��⵽0̨��Ҫ������豸��ά���л���ϣ�
2025-08-01T04:40:54.037+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:54.037+08:00  INFO 43460 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderRepairTask       : �����޸�����ִ����ɣ����޸�0������
2025-08-01T04:40:54.037+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:54.133+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T04:40:54.134+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-4] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ���ʱ��δ֧������
2025-08-01T04:40:54.134+08:00  INFO 43460 --- [jycb-z] [scheduled-task-1] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0�������ɵĶ�����Ҫ����豸״̬һ����
2025-08-01T04:40:54.134+08:00  INFO 43460 --- [jycb-z] [scheduled-task-1] c.j.j.m.d.task.DeviceStatusCheckTask     : �豸״̬�������ִ�����
2025-08-01T04:40:54.134+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:54.204+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T04:40:54.583+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ��������豸��ػ���
2025-08-01T04:40:55.213+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �����豸��ػ���������
2025-08-01T04:40:55.214+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : �豸����������ɣ��ܹ�ɾ�� 0 ��key
2025-08-01T04:40:56.220+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : === �豸ģ�������޸���ɣ����л��������� ===
2025-08-01T04:40:56.220+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : �޸����ݣ�
2025-08-01T04:40:56.220+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 1. �޸����豸��ѯ�߼���ǿ��Ҫ�� is_bound=1
2025-08-01T04:40:56.220+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 2. �޸����豸ͳ�Ʋ�ѯ��ֻͳ���Ѱ��豸
2025-08-01T04:40:56.220+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 3. �����������豸��ػ���
2025-08-01T04:40:56.220+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 4. �ŵ�/�����̽��޷��ٿ���δ���豸
2025-08-01T04:40:56.220+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ��ʼ��ʼ���ֳ����û���...
2025-08-01T04:40:56.221+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ������зֳ����û���...
2025-08-01T04:40:56.350+08:00  INFO 43460 --- [jycb-z] [restartedMain] m.f.s.i.CommissionConfigCacheServiceImpl : ������������û��棬�� 1 ��
2025-08-01T04:40:56.353+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.common.context.UserContextHolder   : �����û�������ʧ��: �� web �������޷���ȡ HttpServletRequest
2025-08-01T04:40:56.354+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ���зֳ����û��������
2025-08-01T04:40:56.354+08:00 DEBUG 43460 --- [jycb-z] [commission-task-1] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ʼִ�У��û�������������: userId=null, username=null, isWebContext=false
2025-08-01T04:40:56.354+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : Ԥ��ϵͳ�ֳ����û���...
2025-08-01T04:40:56.354+08:00  INFO 43460 --- [jycb-z] [commission-task-1] .j.j.m.f.l.CommissionConfigCacheListener : ���������ֳ�������������¼�����������: all
2025-08-01T04:40:56.355+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.common.context.UserContextHolder   : �����û�������ʧ��: �� web �������޷���ȡ HttpServletRequest
2025-08-01T04:40:56.355+08:00 DEBUG 43460 --- [jycb-z] [commission-task-2] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ʼִ�У��û�������������: userId=null, username=null, isWebContext=false
2025-08-01T04:40:56.355+08:00  INFO 43460 --- [jycb-z] [commission-task-2] .j.j.m.f.l.CommissionConfigCacheListener : ��ʼ�ֳ����û���Ԥ�ȣ�ԭ��: Ӧ������Ԥ��
2025-08-01T04:40:56.417+08:00  INFO 43460 --- [jycb-z] [commission-task-1] .j.j.m.f.l.CommissionConfigCacheListener : �����ֳ�������������¼�������ɣ���������л���
2025-08-01T04:40:56.417+08:00 DEBUG 43460 --- [jycb-z] [commission-task-1] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ִ����ɣ��û������������
2025-08-01T04:40:56.490+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:56.499+08:00 DEBUG 43460 --- [jycb-z] [commission-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:56.755+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ϵͳ�ֳ����û���Ԥ�����
2025-08-01T04:40:56.755+08:00  INFO 43460 --- [jycb-z] [commission-task-2] .j.j.m.f.l.CommissionConfigCacheListener : �ֳ����û���Ԥ�����
2025-08-01T04:40:56.755+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : �ֳ����û����ʼ�����
2025-08-01T04:40:56.755+08:00 DEBUG 43460 --- [jycb-z] [commission-task-2] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ִ����ɣ��û������������
2025-08-01T04:40:56.755+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ��ʼִ���豸�������
2025-08-01T04:40:56.755+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:56.918+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸��״̬ͳ�� - ���豸��: 127, �Ѱ�: 12, δ��: 115, ��һ��: 16
2025-08-01T04:40:56.918+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ���� 16 ̨�豸��״̬��һ��: [DEV20250728919223, DEV20250728985125, DEV20250728426128, DEV20250728033370, DEV20250728808073, DEV20250728667449, DEV20250728329772, DEV20250728974982, DEV20250728918848, DEV20250728517748, DEV20250728220366, DEV20250728211309, DEV20250728271493, DEV20250728055783, DEV20250728064937, DEV20250727609878]
2025-08-01T04:40:56.918+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸��״̬��һ�������Ѽ�¼����ͨ����ʱ�����Զ��޸�
2025-08-01T04:40:56.919+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:56.979+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20007
2025-08-01T04:40:57.033+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20007
2025-08-01T04:40:57.095+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20007
2025-08-01T04:40:57.275+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20007
2025-08-01T04:40:57.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20007 �Ļ��治һ��
2025-08-01T04:40:57.275+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:57.399+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20008
2025-08-01T04:40:57.469+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20008
2025-08-01T04:40:57.542+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20008
2025-08-01T04:40:57.722+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20008
2025-08-01T04:40:57.722+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20008 �Ļ��治һ��
2025-08-01T04:40:57.722+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:57.788+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20009
2025-08-01T04:40:57.836+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20009
2025-08-01T04:40:57.897+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20009
2025-08-01T04:40:58.073+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20009
2025-08-01T04:40:58.074+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20009 �Ļ��治һ��
2025-08-01T04:40:58.074+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:58.156+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20010
2025-08-01T04:40:58.216+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20010
2025-08-01T04:40:58.267+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20010
2025-08-01T04:40:58.448+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20010
2025-08-01T04:40:58.448+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20010 �Ļ��治һ��
2025-08-01T04:40:58.448+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:58.507+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20011
2025-08-01T04:40:58.577+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20011
2025-08-01T04:40:58.625+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20011
2025-08-01T04:40:58.813+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20011
2025-08-01T04:40:58.813+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20011 �Ļ��治һ��
2025-08-01T04:40:58.814+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:58.884+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20012
2025-08-01T04:40:58.943+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20012
2025-08-01T04:40:58.998+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20012
2025-08-01T04:40:59.194+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20012
2025-08-01T04:40:59.195+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20012 �Ļ��治һ��
2025-08-01T04:40:59.195+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:59.257+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20013
2025-08-01T04:40:59.318+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20013
2025-08-01T04:40:59.390+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20013
2025-08-01T04:40:59.661+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20013
2025-08-01T04:40:59.661+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20013 �Ļ��治һ��
2025-08-01T04:40:59.662+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:40:59.712+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20014
2025-08-01T04:40:59.786+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20014
2025-08-01T04:40:59.879+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20014
2025-08-01T04:41:00.012+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T04:41:00.012+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T04:41:00.013+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:00.181+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:00.235+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20014
2025-08-01T04:41:00.236+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20014 �Ļ��治һ��
2025-08-01T04:41:00.236+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:00.273+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T04:41:00.273+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T04:41:00.318+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T04:41:00.361+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20015
2025-08-01T04:41:00.425+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20015
2025-08-01T04:41:00.474+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20015
2025-08-01T04:41:00.661+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20015
2025-08-01T04:41:00.661+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20015 �Ļ��治һ��
2025-08-01T04:41:00.662+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:00.746+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20016
2025-08-01T04:41:00.818+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20016
2025-08-01T04:41:00.868+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20016
2025-08-01T04:41:01.025+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20016
2025-08-01T04:41:01.025+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20016 �Ļ��治һ��
2025-08-01T04:41:01.025+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:01.081+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20017
2025-08-01T04:41:01.142+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20017
2025-08-01T04:41:01.190+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20017
2025-08-01T04:41:01.347+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20017
2025-08-01T04:41:01.348+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20017 �Ļ��治һ��
2025-08-01T04:41:01.348+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:01.417+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20018
2025-08-01T04:41:01.464+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20018
2025-08-01T04:41:01.510+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20018
2025-08-01T04:41:01.675+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20018
2025-08-01T04:41:01.675+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20018 �Ļ��治һ��
2025-08-01T04:41:01.675+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:01.749+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20019
2025-08-01T04:41:01.836+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20019
2025-08-01T04:41:01.903+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20019
2025-08-01T04:41:02.105+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20019
2025-08-01T04:41:02.106+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20019 �Ļ��治һ��
2025-08-01T04:41:02.106+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:02.178+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20020
2025-08-01T04:41:02.230+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20020
2025-08-01T04:41:02.283+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20020
2025-08-01T04:41:02.436+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20020
2025-08-01T04:41:02.436+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20020 �Ļ��治һ��
2025-08-01T04:41:02.437+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:02.498+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20021
2025-08-01T04:41:02.551+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20021
2025-08-01T04:41:02.602+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20021
2025-08-01T04:41:02.759+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20021
2025-08-01T04:41:02.759+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20021 �Ļ��治һ��
2025-08-01T04:41:02.760+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:02.819+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20022
2025-08-01T04:41:02.873+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20022
2025-08-01T04:41:02.920+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20022
2025-08-01T04:41:03.080+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20022
2025-08-01T04:41:03.080+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20022 �Ļ��治һ��
2025-08-01T04:41:03.080+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:03.148+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20023
2025-08-01T04:41:03.217+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20023
2025-08-01T04:41:03.277+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20023
2025-08-01T04:41:03.427+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20023
2025-08-01T04:41:03.427+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20023 �Ļ��治һ��
2025-08-01T04:41:03.427+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:03.497+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20024
2025-08-01T04:41:03.546+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20024
2025-08-01T04:41:03.596+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20024
2025-08-01T04:41:03.753+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20024
2025-08-01T04:41:03.753+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20024 �Ļ��治һ��
2025-08-01T04:41:03.753+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:03.817+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20025
2025-08-01T04:41:03.868+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20025
2025-08-01T04:41:03.917+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20025
2025-08-01T04:41:04.071+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20025
2025-08-01T04:41:04.071+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20025 �Ļ��治һ��
2025-08-01T04:41:04.072+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:04.137+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20026
2025-08-01T04:41:04.187+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20026
2025-08-01T04:41:04.237+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20026
2025-08-01T04:41:04.395+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20026
2025-08-01T04:41:04.395+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20026 �Ļ��治һ��
2025-08-01T04:41:04.395+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:04.457+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20027
2025-08-01T04:41:04.507+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20027
2025-08-01T04:41:04.577+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20027
2025-08-01T04:41:04.716+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20027
2025-08-01T04:41:04.716+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20027 �Ļ��治һ��
2025-08-01T04:41:04.716+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:04.776+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20028
2025-08-01T04:41:04.826+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20028
2025-08-01T04:41:04.876+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20028
2025-08-01T04:41:05.035+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20028
2025-08-01T04:41:05.035+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20028 �Ļ��治һ��
2025-08-01T04:41:05.035+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:05.097+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20029
2025-08-01T04:41:05.150+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20029
2025-08-01T04:41:05.195+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20029
2025-08-01T04:41:05.349+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20029
2025-08-01T04:41:05.349+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20029 �Ļ��治һ��
2025-08-01T04:41:05.349+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:05.418+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20030
2025-08-01T04:41:05.471+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20030
2025-08-01T04:41:05.537+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20030
2025-08-01T04:41:05.702+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20030
2025-08-01T04:41:05.702+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20030 �Ļ��治һ��
2025-08-01T04:41:05.702+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:05.760+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20031
2025-08-01T04:41:05.816+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20031
2025-08-01T04:41:05.865+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20031
2025-08-01T04:41:06.028+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20031
2025-08-01T04:41:06.028+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20031 �Ļ��治һ��
2025-08-01T04:41:06.029+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:06.098+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20032
2025-08-01T04:41:06.152+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20032
2025-08-01T04:41:06.199+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20032
2025-08-01T04:41:06.351+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20032
2025-08-01T04:41:06.351+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20032 �Ļ��治һ��
2025-08-01T04:41:06.352+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:06.426+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20033
2025-08-01T04:41:06.499+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20033
2025-08-01T04:41:06.546+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20033
2025-08-01T04:41:06.696+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20033
2025-08-01T04:41:06.696+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20033 �Ļ��治һ��
2025-08-01T04:41:06.696+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:06.752+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20034
2025-08-01T04:41:06.817+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20034
2025-08-01T04:41:06.864+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20034
2025-08-01T04:41:07.027+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20034
2025-08-01T04:41:07.027+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20034 �Ļ��治һ��
2025-08-01T04:41:07.027+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:07.096+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20035
2025-08-01T04:41:07.146+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20035
2025-08-01T04:41:07.197+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20035
2025-08-01T04:41:07.376+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20035
2025-08-01T04:41:07.376+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20035 �Ļ��治һ��
2025-08-01T04:41:07.377+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:07.439+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20036
2025-08-01T04:41:07.495+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20036
2025-08-01T04:41:07.542+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20036
2025-08-01T04:41:07.697+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20036
2025-08-01T04:41:07.697+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20036 �Ļ��治һ��
2025-08-01T04:41:07.698+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:07.757+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20037
2025-08-01T04:41:07.815+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20037
2025-08-01T04:41:07.862+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20037
2025-08-01T04:41:08.017+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20037
2025-08-01T04:41:08.017+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20037 �Ļ��治һ��
2025-08-01T04:41:08.018+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:08.077+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20038
2025-08-01T04:41:08.136+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20038
2025-08-01T04:41:08.187+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20038
2025-08-01T04:41:08.342+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20038
2025-08-01T04:41:08.342+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20038 �Ļ��治һ��
2025-08-01T04:41:08.343+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:08.400+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20039
2025-08-01T04:41:08.455+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20039
2025-08-01T04:41:08.510+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20039
2025-08-01T04:41:08.661+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20039
2025-08-01T04:41:08.662+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20039 �Ļ��治һ��
2025-08-01T04:41:08.662+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:08.718+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20040
2025-08-01T04:41:08.779+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20040
2025-08-01T04:41:08.828+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20040
2025-08-01T04:41:08.990+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20040
2025-08-01T04:41:08.991+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20040 �Ļ��治һ��
2025-08-01T04:41:08.991+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:09.071+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20041
2025-08-01T04:41:09.144+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20041
2025-08-01T04:41:09.191+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20041
2025-08-01T04:41:09.375+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20041
2025-08-01T04:41:09.375+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20041 �Ļ��治һ��
2025-08-01T04:41:09.375+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:09.454+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20042
2025-08-01T04:41:09.502+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20042
2025-08-01T04:41:09.549+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20042
2025-08-01T04:41:09.753+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20042
2025-08-01T04:41:09.753+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20042 �Ļ��治һ��
2025-08-01T04:41:09.753+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:09.829+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20043
2025-08-01T04:41:09.895+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20043
2025-08-01T04:41:09.946+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20043
2025-08-01T04:41:10.145+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20043
2025-08-01T04:41:10.145+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20043 �Ļ��治һ��
2025-08-01T04:41:10.145+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:10.201+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20044
2025-08-01T04:41:10.256+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20044
2025-08-01T04:41:10.301+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20044
2025-08-01T04:41:10.469+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20044
2025-08-01T04:41:10.469+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20044 �Ļ��治һ��
2025-08-01T04:41:10.469+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:10.536+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20045
2025-08-01T04:41:10.592+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20045
2025-08-01T04:41:10.657+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20045
2025-08-01T04:41:10.817+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20045
2025-08-01T04:41:10.817+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20045 �Ļ��治һ��
2025-08-01T04:41:10.817+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:10.873+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20046
2025-08-01T04:41:10.922+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20046
2025-08-01T04:41:10.977+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20046
2025-08-01T04:41:11.137+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20046
2025-08-01T04:41:11.137+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20046 �Ļ��治һ��
2025-08-01T04:41:11.137+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:11.194+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20047
2025-08-01T04:41:11.255+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20047
2025-08-01T04:41:11.334+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20047
2025-08-01T04:41:11.496+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20047
2025-08-01T04:41:11.496+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20047 �Ļ��治һ��
2025-08-01T04:41:11.496+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:11.557+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20048
2025-08-01T04:41:11.619+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20048
2025-08-01T04:41:11.667+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20048
2025-08-01T04:41:11.824+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20048
2025-08-01T04:41:11.824+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20048 �Ļ��治һ��
2025-08-01T04:41:11.824+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:11.882+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20049
2025-08-01T04:41:11.936+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20049
2025-08-01T04:41:11.981+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20049
2025-08-01T04:41:12.142+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20049
2025-08-01T04:41:12.143+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20049 �Ļ��治һ��
2025-08-01T04:41:12.143+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:12.197+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20050
2025-08-01T04:41:12.257+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20050
2025-08-01T04:41:12.308+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20050
2025-08-01T04:41:12.464+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20050
2025-08-01T04:41:12.465+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20050 �Ļ��治һ��
2025-08-01T04:41:12.465+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:12.537+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20051
2025-08-01T04:41:12.592+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20051
2025-08-01T04:41:12.657+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20051
2025-08-01T04:41:12.817+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20051
2025-08-01T04:41:12.817+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20051 �Ļ��治һ��
2025-08-01T04:41:12.818+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:12.876+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20052
2025-08-01T04:41:12.935+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20052
2025-08-01T04:41:12.981+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20052
2025-08-01T04:41:13.141+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20052
2025-08-01T04:41:13.141+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20052 �Ļ��治һ��
2025-08-01T04:41:13.142+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:13.200+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20053
2025-08-01T04:41:13.256+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20053
2025-08-01T04:41:13.307+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20053
2025-08-01T04:41:13.462+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20053
2025-08-01T04:41:13.462+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20053 �Ļ��治һ��
2025-08-01T04:41:13.462+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:13.520+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20054
2025-08-01T04:41:13.575+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20054
2025-08-01T04:41:13.622+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20054
2025-08-01T04:41:13.776+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20054
2025-08-01T04:41:13.776+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20054 �Ļ��治һ��
2025-08-01T04:41:13.777+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:13.836+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20055
2025-08-01T04:41:13.895+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20055
2025-08-01T04:41:13.945+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20055
2025-08-01T04:41:14.104+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20055
2025-08-01T04:41:14.104+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20055 �Ļ��治һ��
2025-08-01T04:41:14.104+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:14.180+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20056
2025-08-01T04:41:14.232+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20056
2025-08-01T04:41:14.295+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20056
2025-08-01T04:41:14.466+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20056
2025-08-01T04:41:14.466+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20056 �Ļ��治һ��
2025-08-01T04:41:14.466+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:14.521+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20057
2025-08-01T04:41:14.576+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20057
2025-08-01T04:41:14.625+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20057
2025-08-01T04:41:14.786+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20057
2025-08-01T04:41:14.786+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20057 �Ļ��治һ��
2025-08-01T04:41:14.786+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:14.858+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20058
2025-08-01T04:41:14.912+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20058
2025-08-01T04:41:14.976+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20058
2025-08-01T04:41:15.136+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20058
2025-08-01T04:41:15.136+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20058 �Ļ��治һ��
2025-08-01T04:41:15.137+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:15.194+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20059
2025-08-01T04:41:15.255+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20059
2025-08-01T04:41:15.305+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20059
2025-08-01T04:41:15.465+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20059
2025-08-01T04:41:15.465+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20059 �Ļ��治һ��
2025-08-01T04:41:15.465+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:15.539+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20060
2025-08-01T04:41:15.593+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20060
2025-08-01T04:41:15.642+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20060
2025-08-01T04:41:15.793+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20060
2025-08-01T04:41:15.793+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20060 �Ļ��治һ��
2025-08-01T04:41:15.793+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:15.858+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20061
2025-08-01T04:41:15.910+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20061
2025-08-01T04:41:15.957+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20061
2025-08-01T04:41:16.111+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20061
2025-08-01T04:41:16.111+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20061 �Ļ��治һ��
2025-08-01T04:41:16.111+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:16.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20062
2025-08-01T04:41:16.232+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20062
2025-08-01T04:41:16.282+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20062
2025-08-01T04:41:16.432+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20062
2025-08-01T04:41:16.432+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20062 �Ļ��治һ��
2025-08-01T04:41:16.432+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:16.499+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20063
2025-08-01T04:41:16.550+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20063
2025-08-01T04:41:16.597+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20063
2025-08-01T04:41:16.756+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20063
2025-08-01T04:41:16.756+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20063 �Ļ��治һ��
2025-08-01T04:41:16.756+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:16.827+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20064
2025-08-01T04:41:16.896+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20064
2025-08-01T04:41:16.945+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20064
2025-08-01T04:41:17.104+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20064
2025-08-01T04:41:17.104+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20064 �Ļ��治һ��
2025-08-01T04:41:17.104+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:17.160+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20065
2025-08-01T04:41:17.221+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20065
2025-08-01T04:41:17.274+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20065
2025-08-01T04:41:17.426+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20065
2025-08-01T04:41:17.426+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20065 �Ļ��治һ��
2025-08-01T04:41:17.427+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:17.497+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20066
2025-08-01T04:41:17.552+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20066
2025-08-01T04:41:17.597+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20066
2025-08-01T04:41:17.749+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20066
2025-08-01T04:41:17.749+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20066 �Ļ��治һ��
2025-08-01T04:41:17.750+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:17.817+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20067
2025-08-01T04:41:17.866+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20067
2025-08-01T04:41:17.918+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20067
2025-08-01T04:41:18.071+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20067
2025-08-01T04:41:18.071+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20067 �Ļ��治һ��
2025-08-01T04:41:18.072+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:18.137+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20068
2025-08-01T04:41:18.194+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20068
2025-08-01T04:41:18.256+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20068
2025-08-01T04:41:18.395+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20068
2025-08-01T04:41:18.395+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20068 �Ļ��治һ��
2025-08-01T04:41:18.395+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:18.458+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20069
2025-08-01T04:41:18.511+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20069
2025-08-01T04:41:18.576+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20069
2025-08-01T04:41:18.738+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20069
2025-08-01T04:41:18.738+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20069 �Ļ��治һ��
2025-08-01T04:41:18.738+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:18.799+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20070
2025-08-01T04:41:18.855+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20070
2025-08-01T04:41:18.904+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20070
2025-08-01T04:41:19.056+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20070
2025-08-01T04:41:19.056+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20070 �Ļ��治һ��
2025-08-01T04:41:19.056+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:19.116+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20071
2025-08-01T04:41:19.175+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20071
2025-08-01T04:41:19.221+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20071
2025-08-01T04:41:19.377+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20071
2025-08-01T04:41:19.377+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20071 �Ļ��治һ��
2025-08-01T04:41:19.377+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:19.437+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20072
2025-08-01T04:41:19.496+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20072
2025-08-01T04:41:19.543+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20072
2025-08-01T04:41:19.697+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20072
2025-08-01T04:41:19.698+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20072 �Ļ��治һ��
2025-08-01T04:41:19.698+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:19.756+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20073
2025-08-01T04:41:19.816+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20073
2025-08-01T04:41:19.869+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20073
2025-08-01T04:41:20.021+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20073
2025-08-01T04:41:20.021+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20073 �Ļ��治һ��
2025-08-01T04:41:20.022+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:20.079+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20074
2025-08-01T04:41:20.135+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20074
2025-08-01T04:41:20.181+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20074
2025-08-01T04:41:20.336+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20074
2025-08-01T04:41:20.336+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20074 �Ļ��治һ��
2025-08-01T04:41:20.336+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:20.396+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20075
2025-08-01T04:41:20.455+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20075
2025-08-01T04:41:20.507+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20075
2025-08-01T04:41:20.657+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20075
2025-08-01T04:41:20.657+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20075 �Ļ��治һ��
2025-08-01T04:41:20.657+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:20.716+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20076
2025-08-01T04:41:20.775+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20076
2025-08-01T04:41:20.822+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20076
2025-08-01T04:41:20.976+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20076
2025-08-01T04:41:20.976+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20076 �Ļ��治һ��
2025-08-01T04:41:20.977+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:21.041+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20077
2025-08-01T04:41:21.095+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20077
2025-08-01T04:41:21.146+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20077
2025-08-01T04:41:21.314+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20077
2025-08-01T04:41:21.315+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20077 �Ļ��治һ��
2025-08-01T04:41:21.315+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:21.380+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20078
2025-08-01T04:41:21.436+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20078
2025-08-01T04:41:21.484+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20078
2025-08-01T04:41:21.637+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20078
2025-08-01T04:41:21.637+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20078 �Ļ��治һ��
2025-08-01T04:41:21.638+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:21.707+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20079
2025-08-01T04:41:21.758+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20079
2025-08-01T04:41:21.815+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20079
2025-08-01T04:41:21.960+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20079
2025-08-01T04:41:21.960+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20079 �Ļ��治һ��
2025-08-01T04:41:21.961+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:22.026+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20080
2025-08-01T04:41:22.096+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20080
2025-08-01T04:41:22.142+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20080
2025-08-01T04:41:22.306+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20080
2025-08-01T04:41:22.306+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20080 �Ļ��治һ��
2025-08-01T04:41:22.307+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:22.377+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20081
2025-08-01T04:41:22.434+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20081
2025-08-01T04:41:22.497+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20081
2025-08-01T04:41:22.658+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20081
2025-08-01T04:41:22.658+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20081 �Ļ��治һ��
2025-08-01T04:41:22.658+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:22.715+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20082
2025-08-01T04:41:22.780+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20082
2025-08-01T04:41:22.830+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20082
2025-08-01T04:41:22.988+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20082
2025-08-01T04:41:22.988+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20082 �Ļ��治һ��
2025-08-01T04:41:22.988+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:23.059+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20083
2025-08-01T04:41:23.112+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20083
2025-08-01T04:41:23.176+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20083
2025-08-01T04:41:23.335+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20083
2025-08-01T04:41:23.336+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20083 �Ļ��治һ��
2025-08-01T04:41:23.336+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:23.397+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20084
2025-08-01T04:41:23.458+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20084
2025-08-01T04:41:23.509+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20084
2025-08-01T04:41:23.667+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20084
2025-08-01T04:41:23.667+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20084 �Ļ��治һ��
2025-08-01T04:41:23.667+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:23.738+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20085
2025-08-01T04:41:23.789+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20085
2025-08-01T04:41:23.858+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20085
2025-08-01T04:41:24.018+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20085
2025-08-01T04:41:24.019+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20085 �Ļ��治һ��
2025-08-01T04:41:24.019+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:24.074+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20086
2025-08-01T04:41:24.135+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20086
2025-08-01T04:41:24.181+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20086
2025-08-01T04:41:24.336+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20086
2025-08-01T04:41:24.337+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20086 �Ļ��治һ��
2025-08-01T04:41:24.337+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:24.394+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20087
2025-08-01T04:41:24.455+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20087
2025-08-01T04:41:24.512+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20087
2025-08-01T04:41:24.677+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20087
2025-08-01T04:41:24.677+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20087 �Ļ��治һ��
2025-08-01T04:41:24.677+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:24.747+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20088
2025-08-01T04:41:24.816+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20088
2025-08-01T04:41:24.864+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20088
2025-08-01T04:41:25.026+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20088
2025-08-01T04:41:25.026+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20088 �Ļ��治һ��
2025-08-01T04:41:25.027+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:25.096+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20089
2025-08-01T04:41:25.146+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20089
2025-08-01T04:41:25.191+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20089
2025-08-01T04:41:25.352+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20089
2025-08-01T04:41:25.352+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20089 �Ļ��治һ��
2025-08-01T04:41:25.352+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:25.416+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20090
2025-08-01T04:41:25.472+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20090
2025-08-01T04:41:25.536+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20090
2025-08-01T04:41:25.712+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20090
2025-08-01T04:41:25.713+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20090 �Ļ��治һ��
2025-08-01T04:41:25.713+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:25.787+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20091
2025-08-01T04:41:25.856+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20091
2025-08-01T04:41:25.906+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20091
2025-08-01T04:41:26.071+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20091
2025-08-01T04:41:26.071+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20091 �Ļ��治һ��
2025-08-01T04:41:26.071+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:26.136+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20092
2025-08-01T04:41:26.182+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20092
2025-08-01T04:41:26.230+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20092
2025-08-01T04:41:26.382+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20092
2025-08-01T04:41:26.382+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20092 �Ļ��治һ��
2025-08-01T04:41:26.382+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:26.442+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20093
2025-08-01T04:41:26.496+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20093
2025-08-01T04:41:26.542+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20093
2025-08-01T04:41:26.706+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20093
2025-08-01T04:41:26.706+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20093 �Ļ��治һ��
2025-08-01T04:41:26.707+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:26.777+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20094
2025-08-01T04:41:26.832+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20094
2025-08-01T04:41:26.877+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20094
2025-08-01T04:41:27.058+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20094
2025-08-01T04:41:27.058+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20094 �Ļ��治һ��
2025-08-01T04:41:27.058+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:27.115+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20095
2025-08-01T04:41:27.175+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20095
2025-08-01T04:41:27.222+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20095
2025-08-01T04:41:27.377+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20095
2025-08-01T04:41:27.377+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20095 �Ļ��治һ��
2025-08-01T04:41:27.378+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:27.437+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20096
2025-08-01T04:41:27.496+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20096
2025-08-01T04:41:27.546+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20096
2025-08-01T04:41:27.697+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20096
2025-08-01T04:41:27.697+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20096 �Ļ��治һ��
2025-08-01T04:41:27.697+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:27.757+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20097
2025-08-01T04:41:27.815+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20097
2025-08-01T04:41:27.861+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20097
2025-08-01T04:41:28.018+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20097
2025-08-01T04:41:28.018+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20097 �Ļ��治һ��
2025-08-01T04:41:28.018+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:28.076+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20098
2025-08-01T04:41:28.135+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20098
2025-08-01T04:41:28.182+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20098
2025-08-01T04:41:28.337+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20098
2025-08-01T04:41:28.337+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20098 �Ļ��治һ��
2025-08-01T04:41:28.338+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:28.397+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20099
2025-08-01T04:41:28.465+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20099
2025-08-01T04:41:28.514+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20099
2025-08-01T04:41:28.680+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20099
2025-08-01T04:41:28.680+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20099 �Ļ��治һ��
2025-08-01T04:41:28.680+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:28.752+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20100
2025-08-01T04:41:28.816+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20100
2025-08-01T04:41:28.867+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20100
2025-08-01T04:41:29.034+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20100
2025-08-01T04:41:29.034+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20100 �Ļ��治һ��
2025-08-01T04:41:29.034+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:29.096+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20101
2025-08-01T04:41:29.148+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20101
2025-08-01T04:41:29.196+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20101
2025-08-01T04:41:29.358+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20101
2025-08-01T04:41:29.360+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20101 �Ļ��治һ��
2025-08-01T04:41:29.360+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:29.416+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20102
2025-08-01T04:41:29.470+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20102
2025-08-01T04:41:29.539+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20102
2025-08-01T04:41:29.699+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20102
2025-08-01T04:41:29.700+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20102 �Ļ��治һ��
2025-08-01T04:41:29.700+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:29.756+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20103
2025-08-01T04:41:29.815+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20103
2025-08-01T04:41:29.862+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20103
2025-08-01T04:41:30.022+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20103
2025-08-01T04:41:30.023+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20103 �Ļ��治һ��
2025-08-01T04:41:30.023+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:30.081+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20104
2025-08-01T04:41:30.135+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20104
2025-08-01T04:41:30.181+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20104
2025-08-01T04:41:30.337+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20104
2025-08-01T04:41:30.337+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20104 �Ļ��治һ��
2025-08-01T04:41:30.337+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:30.396+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20105
2025-08-01T04:41:30.455+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20105
2025-08-01T04:41:30.506+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20105
2025-08-01T04:41:30.665+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20105
2025-08-01T04:41:30.665+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20105 �Ļ��治һ��
2025-08-01T04:41:30.665+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:30.738+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20106
2025-08-01T04:41:30.791+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20106
2025-08-01T04:41:30.837+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20106
2025-08-01T04:41:30.992+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20106
2025-08-01T04:41:30.992+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20106 �Ļ��治һ��
2025-08-01T04:41:30.992+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸��� 100 ̨�豸�Ļ��治һ��
2025-08-01T04:41:30.992+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸����������
2025-08-01T04:41:31.057+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ʼ���ϵͳ����...
2025-08-01T04:41:31.058+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.114+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ�������ü������
2025-08-01T04:41:31.114+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - system_name: ���˷������޹���ϵͳ
2025-08-01T04:41:31.114+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - system_logo: /static/logo.png
2025-08-01T04:41:31.114+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ�������ü����ɣ�������������Ѵ���
2025-08-01T04:41:31.115+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С�������ü������
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - msgDataFormat: JSON
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - appid: wx23b7c6a78842ee76
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - aesKey: 
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wechat_token: 
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret: 63****d6
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - token: 
2025-08-01T04:41:31.179+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wechat_aesKey: 
2025-08-01T04:41:31.179+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С�������ü����ɣ�����������ȱʧ: [aesKey, token]
2025-08-01T04:41:31.181+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.242+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'aesKey' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wechat', ���������ϼ�...
2025-08-01T04:41:31.243+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.298+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wechat_aesKey' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.298+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.351+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'token' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wechat', ���������ϼ�...
2025-08-01T04:41:31.352+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.416+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wechat_token' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.417+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.474+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����(����)���ü������
2025-08-01T04:41:31.474+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_aesKey: 
2025-08-01T04:41:31.474+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_secret: 63****d6
2025-08-01T04:41:31.474+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_token: 
2025-08-01T04:41:31.474+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_msgDataFormat: JSON
2025-08-01T04:41:31.474+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_appid: wx23b7c6a78842ee76
2025-08-01T04:41:31.474+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����(����)���ü����ɣ�����������ȱʧ: [msgDataFormat, appid, aesKey, secret, token]
2025-08-01T04:41:31.475+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.540+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'msgDataFormat' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:41:31.540+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.597+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_msgDataFormat' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.597+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.657+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'appid' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:41:31.657+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.713+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_appid' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.714+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.714+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'aesKey' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:41:31.715+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.778+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_aesKey' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.779+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.831+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'secret' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:41:31.831+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.896+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_secret' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.896+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.896+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'token' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:41:31.897+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:31.962+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_token' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:31.963+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:32.027+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧�����ü������
2025-08-01T04:41:32.027+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - privateKey: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
2025-08-01T04:41:32.027+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_pay_appId: wx23b7c6a78842ee76
2025-08-01T04:41:32.028+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - mchId: 1721424072
2025-08-01T04:41:32.028+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - apiV3Key: 5bKMsetvmbbsaPCUY8QrtyAEx3aZDuB7
2025-08-01T04:41:32.028+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - merchantSerialNumber: 15878634CBF0536B67E993E13AFE7C6A6739131E
2025-08-01T04:41:32.028+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - notifyUrl: https://api.example.com/api/wx/pay/callback/notify
2025-08-01T04:41:32.028+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - key: 5b****B7
2025-08-01T04:41:32.028+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧�����ü����ɣ�����������ȱʧ: [appId]
2025-08-01T04:41:32.028+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:32.099+08:00  WARN 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'appId' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_pay', ���������ϼ�...
2025-08-01T04:41:32.100+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:32.154+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_pay_appId' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:41:32.155+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:32.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��Ѷ��COS���ü������
2025-08-01T04:41:32.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret_id: yo****id
2025-08-01T04:41:32.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret_key: yo****ey
2025-08-01T04:41:32.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - bucket_name: your-bucket-name
2025-08-01T04:41:32.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - region: ap-guangzhou
2025-08-01T04:41:32.216+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��Ѷ��COS���ü����ɣ�������������Ѵ���
2025-08-01T04:41:32.217+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:32.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : �������ü������
2025-08-01T04:41:32.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - withdraw_min_amount: 1.00
2025-08-01T04:41:32.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - withdraw_fee_rate: 0.006
2025-08-01T04:41:32.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : �������ü����ɣ�������������Ѵ���
2025-08-01T04:41:32.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ˢ��΢��С��������...
2025-08-01T04:41:32.274+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T04:41:32.274+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T04:41:32.275+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:32.275+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T04:41:32.275+08:00 DEBUG 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С��������ˢ�����
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����������ˢ��
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.m.a.service.impl.WxPayServiceImpl  : ��WxPayConfig��ȡ���óɹ�: appId=wx****76, mchId=17****72
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧��������ˢ��
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ���ü�����
2025-08-01T04:41:32.275+08:00  INFO 43460 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : jycb-z ģ�����ü�����ϣ�������Դ�����ݿ� jy_system ��
2025-08-01T04:41:54.017+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T04:41:54.018+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:41:54.076+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T04:42:00.008+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T04:42:00.008+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T04:42:00.009+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:42:00.063+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:42:00.121+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T04:42:00.121+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T04:42:00.230+08:00 DEBUG 43460 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T04:42:27.486+08:00  INFO 43460 --- [jycb-z] [SpringApplicationShutdownHook] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01T04:42:27.605+08:00  INFO 43460 --- [jycb-z] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-08-01T04:42:27.632+08:00  INFO 43460 --- [jycb-z] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T04:42:27.635+08:00  INFO 43460 --- [jycb-z] [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T04:59:16.895+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T04:59:16.897+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T04:59:16.897+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T04:59:16.920+08:00  INFO 44824 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T04:59:16.920+08:00  INFO 44824 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T04:59:17.593+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.f.s.DefaultListableBeanFactory     : Overriding bean definition for bean 'cacheManager' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisCacheConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisCacheConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=redisConfig; factoryMethodName=cacheManager; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/jycb/jycbz/config/RedisConfig.class]]
2025-08-01T04:59:18.379+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T04:59:18.388+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T04:59:18.389+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T04:59:18.434+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T04:59:18.434+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1514 ms
2025-08-01T04:59:19.451+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ��΢��С�������...
2025-08-01T04:59:19.451+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T04:59:19.451+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T04:59:19.477+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:19.483+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T04:59:20.041+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@30dac041
2025-08-01T04:59:20.042+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T04:59:20.152+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T04:59:20.152+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T04:59:20.153+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T04:59:20.153+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T04:59:20.153+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T04:59:20.153+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T04:59:20.154+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T04:59:20.154+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T04:59:20.256+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С�������ó�ʼ�����
2025-08-01T04:59:20.298+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttpClient, ���ӳ�ʱ: 10��, ����ʱ: 10��, д��ʱ: 10��
2025-08-01T04:59:20.331+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttp���ӳ�, ����������: 5, ��������ʱ��: 5����
2025-08-01T04:59:20.342+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:20.400+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����ݿ����΢��֧�����óɹ�: mchId=17****72, appId=wx****76, serialNo=15****1E
2025-08-01T04:59:20.400+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ������ݿ����˽Կ������: 1732
2025-08-01T04:59:20.400+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ�����
2025-08-01T04:59:22.815+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.RedisHealthConfig    : Redis���Ӽ��ɹ������湦��������
2025-08-01T04:59:22.837+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ����΢��֧��HttpClient������
2025-08-01T04:59:22.837+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T04:59:22.838+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T04:59:22.838+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T04:59:22.838+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T04:59:22.839+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAConfig...
2025-08-01T04:59:22.839+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T04:59:23.394+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ�
2025-08-01T04:59:23.395+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧��HttpClient�����ɹ�
2025-08-01T04:59:23.398+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ʹ��RSAAutoCertificateConfig����΢��֧������
2025-08-01T04:59:23.399+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T04:59:23.399+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T04:59:23.399+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T04:59:23.399+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T04:59:23.399+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAAutoCertificateConfig...
2025-08-01T04:59:23.399+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T04:59:23.633+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ���Bean����: wxPayConfigBean
2025-08-01T04:59:23.634+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ���ɹ�
2025-08-01T04:59:23.636+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.system.config.SystemTaskConfig   : ϵͳ��ʱ�����������ʼ����ɣ��̳߳ش�С: 0
2025-08-01T04:59:23.745+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �û���������������ע��
2025-08-01T04:59:23.745+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �豸����Ȩ����֤��������ע��
2025-08-01T04:59:24.276+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01T04:59:24.359+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8081 (http) with context path '/'
2025-08-01T04:59:24.368+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Started JycbZApplication in 7.692 seconds (process running for 7.968)
2025-08-01T04:59:24.369+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.DocumentationConfig  : ----------------------------------------------------------
	Ӧ�ó���"jycb-z"����������......
	�ӿ��ĵ����� URL:
	����: 		http://localhost:8081/doc.html
	�ⲿ: 	http://*************:8081/doc.html
	�����ļ�: 	[dev]
----------------------------------------------------------
2025-08-01T04:59:24.370+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.f.t.CommissionCacheRefreshTask   : ��ʼ���ֳ����û��潡��״̬...
2025-08-01T04:59:24.370+08:00  INFO 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderRepairTask       : ��ʼִ�ж����޸�����...
2025-08-01T04:59:24.370+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��ʼִ���豸״̬�������...
2025-08-01T04:59:24.371+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : === �豸ģ�������޸���������ʼִ�� ===
2025-08-01T04:59:24.372+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : ��ʼ���������豸��ػ���...
2025-08-01T04:59:24.373+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:24.373+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:24.420+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.jycb.jycbz.config.RedisHealthConfig    : Redis���ӽ������ͨ��
2025-08-01T04:59:24.464+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.f.t.CommissionCacheRefreshTask   : �ֳ����û��潡��״̬���ͨ��
2025-08-01T04:59:24.495+08:00  INFO 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderRepairTask       : ��⵽0̨��Ҫ������豸��ά���л���ϣ�
2025-08-01T04:59:24.495+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0̨ʹ���е��豸
2025-08-01T04:59:24.495+08:00  INFO 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderRepairTask       : �����޸�����ִ����ɣ����޸�0������
2025-08-01T04:59:24.497+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:24.500+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T04:59:24.502+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:24.518+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ����δ֧��������ʱ����
2025-08-01T04:59:24.519+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:24.558+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0�������ɵĶ�����Ҫ����豸״̬һ����
2025-08-01T04:59:24.558+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : �豸״̬�������ִ�����
2025-08-01T04:59:24.577+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ���ʱ��δ֧������
2025-08-01T04:59:24.582+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T04:59:24.815+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ��������豸��ػ���
2025-08-01T04:59:25.214+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �����豸��ػ���������
2025-08-01T04:59:25.215+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : �豸����������ɣ��ܹ�ɾ�� 0 ��key
2025-08-01T04:59:26.221+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : === �豸ģ�������޸���ɣ����л��������� ===
2025-08-01T04:59:26.221+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : �޸����ݣ�
2025-08-01T04:59:26.221+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 1. �޸����豸��ѯ�߼���ǿ��Ҫ�� is_bound=1
2025-08-01T04:59:26.221+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 2. �޸����豸ͳ�Ʋ�ѯ��ֻͳ���Ѱ��豸
2025-08-01T04:59:26.221+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 3. �����������豸��ػ���
2025-08-01T04:59:26.221+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 4. �ŵ�/�����̽��޷��ٿ���δ���豸
2025-08-01T04:59:26.222+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ��ʼ��ʼ���ֳ����û���...
2025-08-01T04:59:26.222+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ������зֳ����û���...
2025-08-01T04:59:26.339+08:00  INFO 44824 --- [jycb-z] [restartedMain] m.f.s.i.CommissionConfigCacheServiceImpl : ������������û��棬�� 1 ��
2025-08-01T04:59:26.342+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.common.context.UserContextHolder   : �����û�������ʧ��: �� web �������޷���ȡ HttpServletRequest
2025-08-01T04:59:26.344+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ���зֳ����û��������
2025-08-01T04:59:26.344+08:00 DEBUG 44824 --- [jycb-z] [commission-task-1] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ʼִ�У��û�������������: userId=null, username=null, isWebContext=false
2025-08-01T04:59:26.344+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : Ԥ��ϵͳ�ֳ����û���...
2025-08-01T04:59:26.344+08:00  INFO 44824 --- [jycb-z] [commission-task-1] .j.j.m.f.l.CommissionConfigCacheListener : ���������ֳ�������������¼�����������: all
2025-08-01T04:59:26.344+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.common.context.UserContextHolder   : �����û�������ʧ��: �� web �������޷���ȡ HttpServletRequest
2025-08-01T04:59:26.344+08:00 DEBUG 44824 --- [jycb-z] [commission-task-2] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ʼִ�У��û�������������: userId=null, username=null, isWebContext=false
2025-08-01T04:59:26.345+08:00  INFO 44824 --- [jycb-z] [commission-task-2] .j.j.m.f.l.CommissionConfigCacheListener : ��ʼ�ֳ����û���Ԥ�ȣ�ԭ��: Ӧ������Ԥ��
2025-08-01T04:59:26.389+08:00  INFO 44824 --- [jycb-z] [commission-task-1] .j.j.m.f.l.CommissionConfigCacheListener : �����ֳ�������������¼�������ɣ���������л���
2025-08-01T04:59:26.389+08:00 DEBUG 44824 --- [jycb-z] [commission-task-1] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ִ����ɣ��û������������
2025-08-01T04:59:26.536+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ϵͳ�ֳ����û���Ԥ�����
2025-08-01T04:59:26.536+08:00  INFO 44824 --- [jycb-z] [commission-task-2] .j.j.m.f.l.CommissionConfigCacheListener : �ֳ����û���Ԥ�����
2025-08-01T04:59:26.536+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : �ֳ����û����ʼ�����
2025-08-01T04:59:26.536+08:00 DEBUG 44824 --- [jycb-z] [commission-task-2] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ִ����ɣ��û������������
2025-08-01T04:59:26.536+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ��ʼִ���豸�������
2025-08-01T04:59:26.537+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:26.766+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸��״̬ͳ�� - ���豸��: 127, �Ѱ�: 12, δ��: 115, ��һ��: 16
2025-08-01T04:59:26.766+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ���� 16 ̨�豸��״̬��һ��: [DEV20250728919223, DEV20250728985125, DEV20250728426128, DEV20250728033370, DEV20250728808073, DEV20250728667449, DEV20250728329772, DEV20250728974982, DEV20250728918848, DEV20250728517748, DEV20250728220366, DEV20250728211309, DEV20250728271493, DEV20250728055783, DEV20250728064937, DEV20250727609878]
2025-08-01T04:59:26.766+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸��״̬��һ�������Ѽ�¼����ͨ����ʱ�����Զ��޸�
2025-08-01T04:59:26.766+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:26.824+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20007
2025-08-01T04:59:26.875+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20007
2025-08-01T04:59:26.934+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20007
2025-08-01T04:59:27.080+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20007
2025-08-01T04:59:27.080+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20007 �Ļ��治һ��
2025-08-01T04:59:27.080+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:27.144+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20008
2025-08-01T04:59:27.214+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20008
2025-08-01T04:59:27.265+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20008
2025-08-01T04:59:27.429+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20008
2025-08-01T04:59:27.429+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20008 �Ļ��治һ��
2025-08-01T04:59:27.429+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:27.495+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20009
2025-08-01T04:59:27.545+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20009
2025-08-01T04:59:27.593+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20009
2025-08-01T04:59:27.748+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20009
2025-08-01T04:59:27.748+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20009 �Ļ��治һ��
2025-08-01T04:59:27.749+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:27.815+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20010
2025-08-01T04:59:27.865+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20010
2025-08-01T04:59:27.910+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20010
2025-08-01T04:59:28.070+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20010
2025-08-01T04:59:28.070+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20010 �Ļ��治һ��
2025-08-01T04:59:28.070+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:28.135+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20011
2025-08-01T04:59:28.187+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20011
2025-08-01T04:59:28.254+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20011
2025-08-01T04:59:28.394+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20011
2025-08-01T04:59:28.394+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20011 �Ļ��治һ��
2025-08-01T04:59:28.394+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:28.454+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20012
2025-08-01T04:59:28.504+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20012
2025-08-01T04:59:28.550+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20012
2025-08-01T04:59:28.700+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20012
2025-08-01T04:59:28.700+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20012 �Ļ��治һ��
2025-08-01T04:59:28.700+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:28.757+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20013
2025-08-01T04:59:28.822+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20013
2025-08-01T04:59:28.870+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20013
2025-08-01T04:59:29.035+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20013
2025-08-01T04:59:29.035+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20013 �Ļ��治һ��
2025-08-01T04:59:29.035+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:29.099+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20014
2025-08-01T04:59:29.150+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20014
2025-08-01T04:59:29.198+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20014
2025-08-01T04:59:29.350+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20014
2025-08-01T04:59:29.350+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20014 �Ļ��治һ��
2025-08-01T04:59:29.351+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:29.415+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20015
2025-08-01T04:59:29.464+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20015
2025-08-01T04:59:29.512+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20015
2025-08-01T04:59:29.664+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20015
2025-08-01T04:59:29.665+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20015 �Ļ��治һ��
2025-08-01T04:59:29.665+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:29.734+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20016
2025-08-01T04:59:29.784+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20016
2025-08-01T04:59:29.832+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20016
2025-08-01T04:59:29.979+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20016
2025-08-01T04:59:29.979+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20016 �Ļ��治һ��
2025-08-01T04:59:29.980+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:30.039+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20017
2025-08-01T04:59:30.095+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20017
2025-08-01T04:59:30.148+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20017
2025-08-01T04:59:30.305+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20017
2025-08-01T04:59:30.305+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20017 �Ļ��治һ��
2025-08-01T04:59:30.305+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:30.359+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20018
2025-08-01T04:59:30.412+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20018
2025-08-01T04:59:30.459+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20018
2025-08-01T04:59:30.602+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20018
2025-08-01T04:59:30.603+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20018 �Ļ��治һ��
2025-08-01T04:59:30.603+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:30.663+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20019
2025-08-01T04:59:30.714+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20019
2025-08-01T04:59:30.764+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20019
2025-08-01T04:59:30.907+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20019
2025-08-01T04:59:30.908+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20019 �Ļ��治һ��
2025-08-01T04:59:30.908+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:30.967+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20020
2025-08-01T04:59:31.020+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20020
2025-08-01T04:59:31.068+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20020
2025-08-01T04:59:31.213+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20020
2025-08-01T04:59:31.214+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20020 �Ļ��治һ��
2025-08-01T04:59:31.214+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:31.272+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20021
2025-08-01T04:59:31.322+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20021
2025-08-01T04:59:31.371+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20021
2025-08-01T04:59:31.514+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20021
2025-08-01T04:59:31.514+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20021 �Ļ��治һ��
2025-08-01T04:59:31.514+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:31.571+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20022
2025-08-01T04:59:31.624+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20022
2025-08-01T04:59:31.672+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20022
2025-08-01T04:59:31.818+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20022
2025-08-01T04:59:31.819+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20022 �Ļ��治һ��
2025-08-01T04:59:31.820+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:31.878+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20023
2025-08-01T04:59:31.931+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20023
2025-08-01T04:59:31.981+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20023
2025-08-01T04:59:32.131+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20023
2025-08-01T04:59:32.131+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20023 �Ļ��治һ��
2025-08-01T04:59:32.131+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:32.190+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20024
2025-08-01T04:59:32.239+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20024
2025-08-01T04:59:32.287+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20024
2025-08-01T04:59:32.432+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20024
2025-08-01T04:59:32.432+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20024 �Ļ��治һ��
2025-08-01T04:59:32.432+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:32.491+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20025
2025-08-01T04:59:32.539+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20025
2025-08-01T04:59:32.588+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20025
2025-08-01T04:59:32.739+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20025
2025-08-01T04:59:32.739+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20025 �Ļ��治һ��
2025-08-01T04:59:32.740+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:32.795+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20026
2025-08-01T04:59:32.849+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20026
2025-08-01T04:59:32.898+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20026
2025-08-01T04:59:33.040+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20026
2025-08-01T04:59:33.040+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20026 �Ļ��治һ��
2025-08-01T04:59:33.040+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:33.096+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20027
2025-08-01T04:59:33.147+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20027
2025-08-01T04:59:33.195+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20027
2025-08-01T04:59:33.332+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20027
2025-08-01T04:59:33.332+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20027 �Ļ��治һ��
2025-08-01T04:59:33.332+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:33.390+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20028
2025-08-01T04:59:33.447+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20028
2025-08-01T04:59:33.498+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20028
2025-08-01T04:59:33.646+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20028
2025-08-01T04:59:33.646+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20028 �Ļ��治һ��
2025-08-01T04:59:33.646+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:33.706+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20029
2025-08-01T04:59:33.763+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20029
2025-08-01T04:59:33.812+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20029
2025-08-01T04:59:33.971+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20029
2025-08-01T04:59:33.972+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20029 �Ļ��治һ��
2025-08-01T04:59:33.972+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:34.029+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20030
2025-08-01T04:59:34.080+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20030
2025-08-01T04:59:34.128+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20030
2025-08-01T04:59:34.273+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20030
2025-08-01T04:59:34.273+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20030 �Ļ��治һ��
2025-08-01T04:59:34.274+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:34.332+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20031
2025-08-01T04:59:34.383+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20031
2025-08-01T04:59:34.432+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20031
2025-08-01T04:59:34.591+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20031
2025-08-01T04:59:34.591+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20031 �Ļ��治һ��
2025-08-01T04:59:34.592+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:34.647+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20032
2025-08-01T04:59:34.698+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20032
2025-08-01T04:59:34.748+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20032
2025-08-01T04:59:34.900+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20032
2025-08-01T04:59:34.900+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20032 �Ļ��治һ��
2025-08-01T04:59:34.900+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:34.956+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20033
2025-08-01T04:59:35.002+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20033
2025-08-01T04:59:35.050+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20033
2025-08-01T04:59:35.201+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20033
2025-08-01T04:59:35.202+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20033 �Ļ��治һ��
2025-08-01T04:59:35.202+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:35.259+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20034
2025-08-01T04:59:35.306+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20034
2025-08-01T04:59:35.356+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20034
2025-08-01T04:59:35.500+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20034
2025-08-01T04:59:35.500+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20034 �Ļ��治һ��
2025-08-01T04:59:35.500+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:35.561+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20035
2025-08-01T04:59:35.609+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20035
2025-08-01T04:59:35.659+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20035
2025-08-01T04:59:35.799+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20035
2025-08-01T04:59:35.799+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20035 �Ļ��治һ��
2025-08-01T04:59:35.799+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:35.864+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20036
2025-08-01T04:59:35.934+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20036
2025-08-01T04:59:35.983+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20036
2025-08-01T04:59:36.152+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20036
2025-08-01T04:59:36.152+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20036 �Ļ��治һ��
2025-08-01T04:59:36.152+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:36.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20037
2025-08-01T04:59:36.270+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20037
2025-08-01T04:59:36.334+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20037
2025-08-01T04:59:36.470+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20037
2025-08-01T04:59:36.470+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20037 �Ļ��治һ��
2025-08-01T04:59:36.470+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:36.534+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20038
2025-08-01T04:59:36.584+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20038
2025-08-01T04:59:36.632+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20038
2025-08-01T04:59:36.784+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20038
2025-08-01T04:59:36.784+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20038 �Ļ��治һ��
2025-08-01T04:59:36.784+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:36.854+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20039
2025-08-01T04:59:36.910+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20039
2025-08-01T04:59:36.975+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20039
2025-08-01T04:59:37.113+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20039
2025-08-01T04:59:37.113+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20039 �Ļ��治һ��
2025-08-01T04:59:37.114+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:37.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20040
2025-08-01T04:59:37.227+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20040
2025-08-01T04:59:37.294+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20040
2025-08-01T04:59:37.454+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20040
2025-08-01T04:59:37.454+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20040 �Ļ��治һ��
2025-08-01T04:59:37.454+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:37.513+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20041
2025-08-01T04:59:37.573+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20041
2025-08-01T04:59:37.619+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20041
2025-08-01T04:59:37.779+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20041
2025-08-01T04:59:37.779+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20041 �Ļ��治һ��
2025-08-01T04:59:37.780+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:37.855+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20042
2025-08-01T04:59:37.910+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20042
2025-08-01T04:59:37.959+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20042
2025-08-01T04:59:38.117+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20042
2025-08-01T04:59:38.117+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20042 �Ļ��治һ��
2025-08-01T04:59:38.117+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:38.183+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20043
2025-08-01T04:59:38.235+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20043
2025-08-01T04:59:38.281+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20043
2025-08-01T04:59:38.429+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20043
2025-08-01T04:59:38.429+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20043 �Ļ��治һ��
2025-08-01T04:59:38.430+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:38.504+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20044
2025-08-01T04:59:38.574+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20044
2025-08-01T04:59:38.622+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20044
2025-08-01T04:59:38.784+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20044
2025-08-01T04:59:38.784+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20044 �Ļ��治һ��
2025-08-01T04:59:38.785+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:38.854+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20045
2025-08-01T04:59:38.904+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20045
2025-08-01T04:59:38.954+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20045
2025-08-01T04:59:39.116+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20045
2025-08-01T04:59:39.116+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20045 �Ļ��治һ��
2025-08-01T04:59:39.116+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:39.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20046
2025-08-01T04:59:39.224+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20046
2025-08-01T04:59:39.274+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20046
2025-08-01T04:59:39.419+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20046
2025-08-01T04:59:39.420+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20046 �Ļ��治һ��
2025-08-01T04:59:39.420+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:39.478+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20047
2025-08-01T04:59:39.534+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20047
2025-08-01T04:59:39.585+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20047
2025-08-01T04:59:39.746+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20047
2025-08-01T04:59:39.746+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20047 �Ļ��治һ��
2025-08-01T04:59:39.747+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:39.814+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20048
2025-08-01T04:59:39.865+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20048
2025-08-01T04:59:39.911+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20048
2025-08-01T04:59:40.059+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20048
2025-08-01T04:59:40.059+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20048 �Ļ��治һ��
2025-08-01T04:59:40.059+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:40.134+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20049
2025-08-01T04:59:40.182+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20049
2025-08-01T04:59:40.232+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20049
2025-08-01T04:59:40.383+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20049
2025-08-01T04:59:40.383+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20049 �Ļ��治һ��
2025-08-01T04:59:40.384+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:40.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20050
2025-08-01T04:59:40.495+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20050
2025-08-01T04:59:40.545+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20050
2025-08-01T04:59:40.680+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20050
2025-08-01T04:59:40.680+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20050 �Ļ��治һ��
2025-08-01T04:59:40.680+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:40.744+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20051
2025-08-01T04:59:40.796+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20051
2025-08-01T04:59:40.855+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20051
2025-08-01T04:59:41.028+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20051
2025-08-01T04:59:41.028+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20051 �Ļ��治һ��
2025-08-01T04:59:41.029+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:41.095+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20052
2025-08-01T04:59:41.152+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20052
2025-08-01T04:59:41.215+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20052
2025-08-01T04:59:41.374+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20052
2025-08-01T04:59:41.374+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20052 �Ļ��治һ��
2025-08-01T04:59:41.375+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:41.431+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20053
2025-08-01T04:59:41.494+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20053
2025-08-01T04:59:41.549+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20053
2025-08-01T04:59:41.709+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20053
2025-08-01T04:59:41.709+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20053 �Ļ��治һ��
2025-08-01T04:59:41.709+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:41.784+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20054
2025-08-01T04:59:41.854+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20054
2025-08-01T04:59:41.902+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20054
2025-08-01T04:59:42.064+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20054
2025-08-01T04:59:42.064+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20054 �Ļ��治һ��
2025-08-01T04:59:42.064+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:42.134+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20055
2025-08-01T04:59:42.189+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20055
2025-08-01T04:59:42.257+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20055
2025-08-01T04:59:42.415+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20055
2025-08-01T04:59:42.416+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20055 �Ļ��治һ��
2025-08-01T04:59:42.416+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:42.474+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20056
2025-08-01T04:59:42.533+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20056
2025-08-01T04:59:42.579+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20056
2025-08-01T04:59:42.734+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20056
2025-08-01T04:59:42.734+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20056 �Ļ��治һ��
2025-08-01T04:59:42.734+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:42.794+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20057
2025-08-01T04:59:42.853+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20057
2025-08-01T04:59:42.905+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20057
2025-08-01T04:59:43.079+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20057
2025-08-01T04:59:43.079+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20057 �Ļ��治һ��
2025-08-01T04:59:43.079+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:43.156+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20058
2025-08-01T04:59:43.214+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20058
2025-08-01T04:59:43.264+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20058
2025-08-01T04:59:43.413+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20058
2025-08-01T04:59:43.413+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20058 �Ļ��治һ��
2025-08-01T04:59:43.414+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:43.473+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20059
2025-08-01T04:59:43.534+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20059
2025-08-01T04:59:43.584+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20059
2025-08-01T04:59:43.746+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20059
2025-08-01T04:59:43.746+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20059 �Ļ��治һ��
2025-08-01T04:59:43.746+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:43.816+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20060
2025-08-01T04:59:43.865+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20060
2025-08-01T04:59:43.915+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20060
2025-08-01T04:59:44.069+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20060
2025-08-01T04:59:44.070+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20060 �Ļ��治һ��
2025-08-01T04:59:44.070+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:44.134+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20061
2025-08-01T04:59:44.192+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20061
2025-08-01T04:59:44.260+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20061
2025-08-01T04:59:44.422+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20061
2025-08-01T04:59:44.423+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20061 �Ļ��治һ��
2025-08-01T04:59:44.423+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:44.504+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20062
2025-08-01T04:59:44.550+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20062
2025-08-01T04:59:44.600+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20062
2025-08-01T04:59:44.773+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20062
2025-08-01T04:59:44.773+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20062 �Ļ��治һ��
2025-08-01T04:59:44.774+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:44.829+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20063
2025-08-01T04:59:44.896+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20063
2025-08-01T04:59:44.940+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20063
2025-08-01T04:59:45.103+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20063
2025-08-01T04:59:45.103+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20063 �Ļ��治һ��
2025-08-01T04:59:45.103+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:45.161+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20064
2025-08-01T04:59:45.222+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20064
2025-08-01T04:59:45.273+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20064
2025-08-01T04:59:45.422+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20064
2025-08-01T04:59:45.422+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20064 �Ļ��治һ��
2025-08-01T04:59:45.422+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:45.495+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20065
2025-08-01T04:59:45.549+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20065
2025-08-01T04:59:45.615+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20065
2025-08-01T04:59:45.755+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20065
2025-08-01T04:59:45.755+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20065 �Ļ��治һ��
2025-08-01T04:59:45.755+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:45.815+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20066
2025-08-01T04:59:45.864+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20066
2025-08-01T04:59:45.909+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20066
2025-08-01T04:59:46.065+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20066
2025-08-01T04:59:46.065+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20066 �Ļ��治һ��
2025-08-01T04:59:46.065+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:46.135+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20067
2025-08-01T04:59:46.193+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20067
2025-08-01T04:59:46.255+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20067
2025-08-01T04:59:46.414+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20067
2025-08-01T04:59:46.414+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20067 �Ļ��治һ��
2025-08-01T04:59:46.414+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:46.473+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20068
2025-08-01T04:59:46.533+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20068
2025-08-01T04:59:46.582+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20068
2025-08-01T04:59:46.734+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20068
2025-08-01T04:59:46.734+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20068 �Ļ��治һ��
2025-08-01T04:59:46.735+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:46.795+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20069
2025-08-01T04:59:46.854+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20069
2025-08-01T04:59:46.904+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20069
2025-08-01T04:59:47.054+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20069
2025-08-01T04:59:47.054+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20069 �Ļ��治һ��
2025-08-01T04:59:47.054+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:47.114+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20070
2025-08-01T04:59:47.173+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20070
2025-08-01T04:59:47.231+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20070
2025-08-01T04:59:47.397+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20070
2025-08-01T04:59:47.399+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20070 �Ļ��治һ��
2025-08-01T04:59:47.400+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:47.461+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20071
2025-08-01T04:59:47.516+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20071
2025-08-01T04:59:47.573+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20071
2025-08-01T04:59:47.715+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20071
2025-08-01T04:59:47.715+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20071 �Ļ��治һ��
2025-08-01T04:59:47.715+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:47.775+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20072
2025-08-01T04:59:47.829+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20072
2025-08-01T04:59:47.880+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20072
2025-08-01T04:59:48.034+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20072
2025-08-01T04:59:48.035+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20072 �Ļ��治һ��
2025-08-01T04:59:48.035+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:48.097+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20073
2025-08-01T04:59:48.148+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20073
2025-08-01T04:59:48.196+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20073
2025-08-01T04:59:48.355+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20073
2025-08-01T04:59:48.355+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20073 �Ļ��治һ��
2025-08-01T04:59:48.355+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:48.416+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20074
2025-08-01T04:59:48.469+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20074
2025-08-01T04:59:48.520+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20074
2025-08-01T04:59:48.674+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20074
2025-08-01T04:59:48.674+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20074 �Ļ��治һ��
2025-08-01T04:59:48.675+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:48.737+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20075
2025-08-01T04:59:48.789+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20075
2025-08-01T04:59:48.837+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20075
2025-08-01T04:59:49.001+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20075
2025-08-01T04:59:49.001+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20075 �Ļ��治һ��
2025-08-01T04:59:49.001+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:49.065+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20076
2025-08-01T04:59:49.135+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20076
2025-08-01T04:59:49.185+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20076
2025-08-01T04:59:49.349+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20076
2025-08-01T04:59:49.349+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20076 �Ļ��治һ��
2025-08-01T04:59:49.350+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:49.415+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20077
2025-08-01T04:59:49.464+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20077
2025-08-01T04:59:49.514+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20077
2025-08-01T04:59:49.659+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20077
2025-08-01T04:59:49.659+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20077 �Ļ��治һ��
2025-08-01T04:59:49.659+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:49.720+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20078
2025-08-01T04:59:49.775+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20078
2025-08-01T04:59:49.824+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20078
2025-08-01T04:59:49.987+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20078
2025-08-01T04:59:49.988+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20078 �Ļ��治һ��
2025-08-01T04:59:49.988+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:50.055+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20079
2025-08-01T04:59:50.104+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20079
2025-08-01T04:59:50.155+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20079
2025-08-01T04:59:50.303+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20079
2025-08-01T04:59:50.304+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20079 �Ļ��治һ��
2025-08-01T04:59:50.304+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:50.374+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20080
2025-08-01T04:59:50.424+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20080
2025-08-01T04:59:50.473+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20080
2025-08-01T04:59:50.629+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20080
2025-08-01T04:59:50.629+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20080 �Ļ��治һ��
2025-08-01T04:59:50.629+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:50.694+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20081
2025-08-01T04:59:50.744+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20081
2025-08-01T04:59:50.789+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20081
2025-08-01T04:59:50.939+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20081
2025-08-01T04:59:50.939+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20081 �Ļ��治һ��
2025-08-01T04:59:50.939+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:50.998+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20082
2025-08-01T04:59:51.056+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20082
2025-08-01T04:59:51.107+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20082
2025-08-01T04:59:51.273+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20082
2025-08-01T04:59:51.273+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20082 �Ļ��治һ��
2025-08-01T04:59:51.274+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:51.334+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20083
2025-08-01T04:59:51.388+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20083
2025-08-01T04:59:51.433+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20083
2025-08-01T04:59:51.585+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20083
2025-08-01T04:59:51.585+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20083 �Ļ��治һ��
2025-08-01T04:59:51.585+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:51.655+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20084
2025-08-01T04:59:51.710+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20084
2025-08-01T04:59:51.774+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20084
2025-08-01T04:59:51.934+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20084
2025-08-01T04:59:51.935+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20084 �Ļ��治һ��
2025-08-01T04:59:51.935+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:51.995+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20085
2025-08-01T04:59:52.053+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20085
2025-08-01T04:59:52.099+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20085
2025-08-01T04:59:52.257+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20085
2025-08-01T04:59:52.257+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20085 �Ļ��治һ��
2025-08-01T04:59:52.257+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:52.315+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20086
2025-08-01T04:59:52.375+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20086
2025-08-01T04:59:52.430+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20086
2025-08-01T04:59:52.589+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20086
2025-08-01T04:59:52.589+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20086 �Ļ��治һ��
2025-08-01T04:59:52.589+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:52.661+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20087
2025-08-01T04:59:52.712+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20087
2025-08-01T04:59:52.759+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20087
2025-08-01T04:59:52.906+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20087
2025-08-01T04:59:52.906+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20087 �Ļ��治һ��
2025-08-01T04:59:52.907+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:52.976+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20088
2025-08-01T04:59:53.029+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20088
2025-08-01T04:59:53.081+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20088
2025-08-01T04:59:53.229+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20088
2025-08-01T04:59:53.229+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20088 �Ļ��治һ��
2025-08-01T04:59:53.230+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:53.305+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20089
2025-08-01T04:59:53.355+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20089
2025-08-01T04:59:53.413+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20089
2025-08-01T04:59:53.559+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20089
2025-08-01T04:59:53.559+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20089 �Ļ��治һ��
2025-08-01T04:59:53.560+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:53.625+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20090
2025-08-01T04:59:53.695+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20090
2025-08-01T04:59:53.744+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20090
2025-08-01T04:59:53.884+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20090
2025-08-01T04:59:53.884+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20090 �Ļ��治һ��
2025-08-01T04:59:53.884+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:53.946+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20091
2025-08-01T04:59:54.015+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20091
2025-08-01T04:59:54.062+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20091
2025-08-01T04:59:54.214+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20091
2025-08-01T04:59:54.214+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20091 �Ļ��治һ��
2025-08-01T04:59:54.215+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:54.274+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20092
2025-08-01T04:59:54.335+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20092
2025-08-01T04:59:54.390+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20092
2025-08-01T04:59:54.552+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20092
2025-08-01T04:59:54.552+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20092 �Ļ��治һ��
2025-08-01T04:59:54.552+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:54.615+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20093
2025-08-01T04:59:54.670+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20093
2025-08-01T04:59:54.735+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20093
2025-08-01T04:59:54.895+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20093
2025-08-01T04:59:54.895+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20093 �Ļ��治һ��
2025-08-01T04:59:54.895+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:54.958+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20094
2025-08-01T04:59:55.014+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20094
2025-08-01T04:59:55.062+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20094
2025-08-01T04:59:55.216+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20094
2025-08-01T04:59:55.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20094 �Ļ��治һ��
2025-08-01T04:59:55.216+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:55.273+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20095
2025-08-01T04:59:55.333+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20095
2025-08-01T04:59:55.383+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20095
2025-08-01T04:59:55.540+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20095
2025-08-01T04:59:55.540+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20095 �Ļ��治һ��
2025-08-01T04:59:55.541+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:55.617+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20096
2025-08-01T04:59:55.672+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20096
2025-08-01T04:59:55.721+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20096
2025-08-01T04:59:55.872+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20096
2025-08-01T04:59:55.872+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20096 �Ļ��治һ��
2025-08-01T04:59:55.872+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:55.944+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20097
2025-08-01T04:59:56.016+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20097
2025-08-01T04:59:56.065+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20097
2025-08-01T04:59:56.224+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20097
2025-08-01T04:59:56.225+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20097 �Ļ��治һ��
2025-08-01T04:59:56.225+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:56.295+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20098
2025-08-01T04:59:56.344+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20098
2025-08-01T04:59:56.392+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20098
2025-08-01T04:59:56.547+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20098
2025-08-01T04:59:56.548+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20098 �Ļ��治һ��
2025-08-01T04:59:56.548+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:56.615+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20099
2025-08-01T04:59:56.665+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20099
2025-08-01T04:59:56.715+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20099
2025-08-01T04:59:56.867+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20099
2025-08-01T04:59:56.867+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20099 �Ļ��治һ��
2025-08-01T04:59:56.867+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:56.934+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20100
2025-08-01T04:59:56.987+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20100
2025-08-01T04:59:57.058+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20100
2025-08-01T04:59:57.215+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20100
2025-08-01T04:59:57.215+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20100 �Ļ��治һ��
2025-08-01T04:59:57.216+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:57.275+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20101
2025-08-01T04:59:57.333+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20101
2025-08-01T04:59:57.379+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20101
2025-08-01T04:59:57.534+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20101
2025-08-01T04:59:57.535+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20101 �Ļ��治һ��
2025-08-01T04:59:57.535+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:57.597+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20102
2025-08-01T04:59:57.655+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20102
2025-08-01T04:59:57.701+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20102
2025-08-01T04:59:57.854+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20102
2025-08-01T04:59:57.855+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20102 �Ļ��治һ��
2025-08-01T04:59:57.855+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:57.914+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20103
2025-08-01T04:59:57.974+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20103
2025-08-01T04:59:58.019+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20103
2025-08-01T04:59:58.174+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20103
2025-08-01T04:59:58.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20103 �Ļ��治һ��
2025-08-01T04:59:58.174+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:58.234+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20104
2025-08-01T04:59:58.293+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20104
2025-08-01T04:59:58.344+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20104
2025-08-01T04:59:58.499+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20104
2025-08-01T04:59:58.499+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20104 �Ļ��治һ��
2025-08-01T04:59:58.499+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:58.575+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20105
2025-08-01T04:59:58.627+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20105
2025-08-01T04:59:58.674+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20105
2025-08-01T04:59:58.829+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20105
2025-08-01T04:59:58.829+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20105 �Ļ��治һ��
2025-08-01T04:59:58.829+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:58.904+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20106
2025-08-01T04:59:58.974+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20106
2025-08-01T04:59:59.025+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20106
2025-08-01T04:59:59.184+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20106
2025-08-01T04:59:59.184+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20106 �Ļ��治һ��
2025-08-01T04:59:59.184+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸��� 100 ̨�豸�Ļ��治һ��
2025-08-01T04:59:59.185+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸����������
2025-08-01T04:59:59.234+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ʼ���ϵͳ����...
2025-08-01T04:59:59.235+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.296+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ�������ü������
2025-08-01T04:59:59.296+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - system_name: ���˷������޹���ϵͳ
2025-08-01T04:59:59.296+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - system_logo: /static/logo.png
2025-08-01T04:59:59.296+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ�������ü����ɣ�������������Ѵ���
2025-08-01T04:59:59.297+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.355+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С�������ü������
2025-08-01T04:59:59.355+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - msgDataFormat: JSON
2025-08-01T04:59:59.355+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - appid: wx23b7c6a78842ee76
2025-08-01T04:59:59.356+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - aesKey: 
2025-08-01T04:59:59.356+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wechat_token: 
2025-08-01T04:59:59.356+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret: 63****d6
2025-08-01T04:59:59.356+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - token: 
2025-08-01T04:59:59.356+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wechat_aesKey: 
2025-08-01T04:59:59.356+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С�������ü����ɣ�����������ȱʧ: [aesKey, token]
2025-08-01T04:59:59.359+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.423+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'aesKey' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wechat', ���������ϼ�...
2025-08-01T04:59:59.424+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.494+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wechat_aesKey' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:59:59.495+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.548+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'token' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wechat', ���������ϼ�...
2025-08-01T04:59:59.549+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.616+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wechat_token' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:59:59.617+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.675+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����(����)���ü������
2025-08-01T04:59:59.675+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_aesKey: 
2025-08-01T04:59:59.675+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_secret: 63****d6
2025-08-01T04:59:59.675+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_token: 
2025-08-01T04:59:59.675+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_msgDataFormat: JSON
2025-08-01T04:59:59.675+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_appid: wx23b7c6a78842ee76
2025-08-01T04:59:59.675+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����(����)���ü����ɣ�����������ȱʧ: [msgDataFormat, appid, aesKey, secret, token]
2025-08-01T04:59:59.675+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.737+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'msgDataFormat' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:59:59.738+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.790+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_msgDataFormat' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:59:59.791+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.857+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'appid' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:59:59.858+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.910+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_appid' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:59:59.911+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.911+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'aesKey' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T04:59:59.911+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T04:59:59.974+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_aesKey' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T04:59:59.975+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.013+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.f.task.DataConsistencyTask       : ��ʼִ��ʵʱ���ݼ������
2025-08-01T05:00:00.013+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ���δ����ֳɵĶ���
2025-08-01T05:00:00.013+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ��鳬ʱδ֧������
2025-08-01T05:00:00.014+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] m.f.s.i.FinanceDataValidationServiceImpl : ��ʼ����˻����һ����
2025-08-01T05:00:00.013+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:00:00.017+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.017+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.017+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.018+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.030+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'secret' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:00:00.030+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.095+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_secret' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:00:00.095+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.095+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'token' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:00:00.095+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.110+08:00  INFO 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.f.task.SettlementScheduledTask   : ��ʼ�����½�������
2025-08-01T05:00:00.111+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.122+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : ����0����ʱδ֧������
2025-08-01T05:00:00.123+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : ��ʱδ֧������������
2025-08-01T05:00:00.123+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderExpireTask       : ��ʼ����δ֧��������������
2025-08-01T05:00:00.124+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.136+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:00:00.136+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:00:00.136+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ����δ֧��������ʱ...
2025-08-01T05:00:00.136+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.modules.order.task.OrderCheckTask  : ����0��δ����ֳɵĶ���
2025-08-01T05:00:00.136+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.modules.order.task.OrderCheckTask  : δ����ֳɶ���������
2025-08-01T05:00:00.137+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:00:00.151+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_token' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:00:00.151+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.186+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.189+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderExpireTask       : �ҵ�0������δ֧������
2025-08-01T05:00:00.189+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderExpireTask       : ��������δ֧��������������
2025-08-01T05:00:00.189+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.s.task.SystemConfigBackupTask    : ��ʼִ�ж�ʱ����: backupVerify
2025-08-01T05:00:00.190+08:00  WARN 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.s.task.SystemConfigBackupTask    : ����Ŀ¼������: C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\.\backup
2025-08-01T05:00:00.190+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.t.CommissionCacheRefreshTask   : ��ʼ��ʱˢ��ϵͳ�ֳ����û���...
2025-08-01T05:00:00.193+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.198+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧�����ü������
2025-08-01T05:00:00.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - privateKey: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
2025-08-01T05:00:00.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_pay_appId: wx23b7c6a78842ee76
2025-08-01T05:00:00.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - mchId: 1721424072
2025-08-01T05:00:00.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - apiV3Key: 5bKMsetvmbbsaPCUY8QrtyAEx3aZDuB7
2025-08-01T05:00:00.216+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - merchantSerialNumber: 15878634CBF0536B67E993E13AFE7C6A6739131E
2025-08-01T05:00:00.217+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - notifyUrl: https://api.example.com/api/wx/pay/callback/notify
2025-08-01T05:00:00.217+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - key: 5b****B7
2025-08-01T05:00:00.217+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧�����ü����ɣ�����������ȱʧ: [appId]
2025-08-01T05:00:00.217+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.254+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] m.f.s.i.CommissionConfigCacheServiceImpl : �����ϵͳ���û���
2025-08-01T05:00:00.257+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.264+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'appId' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_pay', ���������ϼ�...
2025-08-01T05:00:00.264+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.269+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : �ҵ� 0 ����ʱδ֧������
2025-08-01T05:00:00.269+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : �ɹ�ȡ�� 0 ����ʱδ֧������
2025-08-01T05:00:00.272+08:00 ERROR 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.f.task.SettlementScheduledTask   : �����½�������ʧ��

org.springframework.jdbc.BadSqlGrammarException: 
### Error querying database.  Cause: java.sql.SQLSyntaxErrorException: Unknown column 'last_settlement_time' in 'field list'
### The error may exist in com/jycb/jycbz/modules/finance/mapper/SettlementConfigMapper.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT  id,config_type,config_id,parent_config_id,settlement_type,settlement_day,min_settlement_amount,use_parent_config,status,last_settlement_time,remark,create_by,create_time,update_by,update_time  FROM jy_settlement_config      WHERE  (settlement_type = ? AND status = ?)
### Cause: java.sql.SQLSyntaxErrorException: Unknown column 'last_settlement_time' in 'field list'
; bad SQL grammar []
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246) ~[spring-jdbc-6.2.8.jar:6.2.8]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107) ~[spring-jdbc-6.2.8.jar:6.2.8]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-3.0.3.jar:3.0.3]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-3.0.3.jar:3.0.3]
	at jdk.proxy2/jdk.proxy2.$Proxy132.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-3.0.3.jar:3.0.3]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89) ~[mybatis-plus-core-3.5.5.jar:3.5.5]
	at jdk.proxy3/jdk.proxy3.$Proxy196.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.extension.service.IService.list(IService.java:406) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:724) ~[spring-aop-6.2.8.jar:6.2.8]
	at com.jycb.jycbz.modules.finance.service.impl.SettlementConfigServiceImpl$$SpringCGLIB$$0.list(<generated>) ~[classes/:na]
	at com.jycb.jycbz.modules.finance.task.SettlementScheduledTask.processMonthlySettlement(SettlementScheduledTask.java:124) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380) ~[spring-tx-6.2.8.jar:6.2.8]
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119) ~[spring-tx-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728) ~[spring-aop-6.2.8.jar:6.2.8]
	at com.jycb.jycbz.modules.finance.task.SettlementScheduledTask$$SpringCGLIB$$0.processMonthlySettlement(<generated>) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.runInternal(ScheduledMethodRunnable.java:130) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.lambda$run$2(ScheduledMethodRunnable.java:124) ~[spring-context-6.2.8.jar:6.2.8]
	at io.micrometer.observation.Observation.observe(Observation.java:498) ~[micrometer-observation-1.15.1.jar:1.15.1]
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:124) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.scheduling.config.Task$OutcomeTrackingRunnable.run(Task.java:85) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.scheduling.concurrent.ReschedulingRunnable.run(ReschedulingRunnable.java:96) ~[spring-context-6.2.8.jar:6.2.8]
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539) ~[na:na]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264) ~[na:na]
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
Caused by: java.sql.SQLSyntaxErrorException: Unknown column 'last_settlement_time' in 'field list'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112) ~[mysql-connector-j-9.2.0.jar:9.2.0]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114) ~[mysql-connector-j-9.2.0.jar:9.2.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:990) ~[mysql-connector-j-9.2.0.jar:9.2.0]
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:384) ~[mysql-connector-j-9.2.0.jar:9.2.0]
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44) ~[HikariCP-6.3.0.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java) ~[HikariCP-6.3.0.jar:na]
	at jdk.internal.reflect.GeneratedMethodAccessor55.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58) ~[mybatis-3.5.15.jar:3.5.15]
	at jdk.proxy4/jdk.proxy4.$Proxy161.execute(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.query(PreparedStatementHandler.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.query(RoutingStatementHandler.java:80) ~[mybatis-3.5.15.jar:3.5.15]
	at jdk.internal.reflect.GeneratedMethodAccessor54.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61) ~[mybatis-3.5.15.jar:3.5.15]
	at jdk.proxy2/jdk.proxy2.$Proxy159.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:65) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.15.jar:3.5.15]
	at jdk.proxy2/jdk.proxy2.$Proxy158.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.15.jar:3.5.15]
	at jdk.internal.reflect.GeneratedMethodAccessor75.invoke(Unknown Source) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-3.0.3.jar:3.0.3]
	... 45 common frames omitted

2025-08-01T05:00:00.312+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_pay_appId' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:00:00.312+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.312+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.314+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:00:00.335+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : δ֧��������ʱ�������
2025-08-01T05:00:00.335+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.f.s.impl.CommissionServiceImpl   : ��ʼ����δ�ֳɵ���֧������...
2025-08-01T05:00:00.374+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.f.task.CommissionMonitorTask     : ��ʼ���δ�ֳɵ���֧������...
2025-08-01T05:00:00.375+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.375+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��Ѷ��COS���ü������
2025-08-01T05:00:00.375+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret_id: yo****id
2025-08-01T05:00:00.375+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret_key: yo****ey
2025-08-01T05:00:00.375+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - bucket_name: your-bucket-name
2025-08-01T05:00:00.375+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - region: ap-guangzhou
2025-08-01T05:00:00.375+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��Ѷ��COS���ü����ɣ�������������Ѵ���
2025-08-01T05:00:00.375+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.376+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.389+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.f.task.DataConsistencyTask       : ��ʼִ�в���������
2025-08-01T05:00:00.390+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.390+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.415+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.t.CommissionCacheRefreshTask   : ϵͳ�ֳ����û��涨ʱˢ�����
2025-08-01T05:00:00.416+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.task.DataConsistencyTask       : ��ʼִ�п������һ���Լ��
2025-08-01T05:00:00.437+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.f.task.CommissionMonitorTask     : û�з��ֳ���2Сʱδ�ֳɵĶ���
2025-08-01T05:00:00.437+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : �������ü������
2025-08-01T05:00:00.437+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.437+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - withdraw_min_amount: 1.00
2025-08-01T05:00:00.437+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - withdraw_fee_rate: 0.006
2025-08-01T05:00:00.437+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : �������ü����ɣ�������������Ѵ���
2025-08-01T05:00:00.437+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ˢ��΢��С��������...
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T05:00:00.438+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T05:00:00.438+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.438+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T05:00:00.438+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С��������ˢ�����
2025-08-01T05:00:00.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����������ˢ��
2025-08-01T05:00:00.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.a.service.impl.WxPayServiceImpl  : ��WxPayConfig��ȡ���óɹ�: appId=wx****76, mchId=17****72
2025-08-01T05:00:00.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧��������ˢ��
2025-08-01T05:00:00.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ���ü�����
2025-08-01T05:00:00.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : jycb-z ģ�����ü�����ϣ�������Դ�����ݿ� jy_system ��
2025-08-01T05:00:00.439+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.455+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.f.s.impl.CommissionServiceImpl   : �ҵ� 0 ��δ�ֳɵ���֧������
2025-08-01T05:00:00.455+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.f.s.impl.CommissionServiceImpl   : �ɹ����� 0 �������ֳ�
2025-08-01T05:00:00.504+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.504+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.f.task.DataConsistencyTask       : �������������
2025-08-01T05:00:00.542+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] m.f.s.i.FinanceDataValidationServiceImpl : ��ʼ����˻����һ����
2025-08-01T05:00:00.543+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.577+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.602+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.640+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.656+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.705+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.717+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.774+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.777+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.832+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.834+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.897+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.899+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.954+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:00.955+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.015+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.016+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.076+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.076+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.135+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.138+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.194+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.197+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.255+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.314+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.339+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.378+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.397+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] m.f.s.i.FinanceDataValidationServiceImpl : �˻����һ���Լ����ɣ������ʧ�ܣ�׼ȷ�ʣ�16.67%
2025-08-01T05:00:01.397+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] m.f.s.i.FinanceDataValidationServiceImpl : ��ʼ��鶩��������¼һ���ԣ�ʱ�䷶Χ��2025-08-01T04:00:01.397304500 - 2025-08-01T05:00:01.397304500
2025-08-01T05:00:01.398+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.436+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.467+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.539+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] m.f.s.i.FinanceDataValidationServiceImpl : ��������һ���Լ����ɣ������ͨ����׼ȷ�ʣ�100.0%
2025-08-01T05:00:01.539+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] m.f.s.i.FinanceDataValidationServiceImpl : ��ʼ���ֳɼ���һ���ԣ�ʱ�䷶Χ��2025-08-01T04:00:01.539304700 - 2025-08-01T05:00:01.539304700
2025-08-01T05:00:01.539+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.549+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.599+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.660+08:00  WARN 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.f.task.DataConsistencyTask       : ��������һ��������: [�˻���һ��]
2025-08-01T05:00:01.660+08:00  WARN 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.f.task.DataConsistencyTask       : ��������һ���Ը澯: {abnormalTransactionCount=0, noNegativeAccounts=true, checkTime=2025-08-01T05:00:01.*********, commissionConsistency=true, negativeAccountCount=0, overallConsistent=false, noAbnormalTransactions=true, balanceConsistency=false, issues=[�˻���һ��], orderConsistency=true}
2025-08-01T05:00:01.660+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.f.task.DataConsistencyTask       : ʵʱ���ݼ������ִ�����
2025-08-01T05:00:01.695+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:01.754+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] m.f.s.i.FinanceDataValidationServiceImpl : �˻����һ���Լ����ɣ������ʧ�ܣ�׼ȷ�ʣ�16.67%
2025-08-01T05:00:01.920+08:00  WARN 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.task.DataConsistencyTask       : ������һ�����⣬׼ȷ��: 16.67%
2025-08-01T05:00:01.920+08:00  WARN 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.task.DataConsistencyTask       : ������һ�¸澯���˻����һ���Լ��
2025-08-01T05:00:01.920+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.f.task.DataConsistencyTask       : �������һ���Լ����ɣ�׼ȷ��: 16.67%
2025-08-01T05:00:24.474+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:00:24.475+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:00:24.535+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:01:00.013+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:01:00.013+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:01:00.014+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:01:00.068+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:01:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:01:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:01:00.254+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:01:24.466+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:01:24.467+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:01:24.534+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:02:00.001+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:02:00.001+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:02:00.002+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:02:00.054+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:02:00.114+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:02:00.114+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:02:00.227+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:02:24.476+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:02:24.476+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:02:24.535+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:03:00.012+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:03:00.012+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:03:00.012+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:03:00.067+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:03:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:03:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:03:00.254+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:03:24.467+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:03:24.468+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:03:24.537+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:04:00.006+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:04:00.006+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:04:00.007+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:04:00.060+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:04:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:04:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:04:00.262+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:04:24.380+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.d.task.DeviceStatusCheckTask     : ��ʼִ���豸״̬�������...
2025-08-01T05:04:24.380+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:04:24.429+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.jycb.jycbz.config.RedisHealthConfig    : Redis���ӽ������ͨ��
2025-08-01T05:04:24.494+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:04:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:04:24.496+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ����δ֧��������ʱ����
2025-08-01T05:04:24.496+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:04:24.500+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0̨ʹ���е��豸
2025-08-01T05:04:24.500+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:04:24.556+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ���ʱ��δ֧������
2025-08-01T05:04:24.556+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:04:24.582+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0�������ɵĶ�����Ҫ����豸״̬һ����
2025-08-01T05:04:24.582+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.d.task.DeviceStatusCheckTask     : �豸״̬�������ִ�����
2025-08-01T05:05:00.008+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ����δ֧��������ʱ...
2025-08-01T05:05:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.f.task.DataConsistencyTask       : ��ʼִ�в���������
2025-08-01T05:05:00.008+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ��鳬ʱδ֧������
2025-08-01T05:05:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:05:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:05:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.062+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.063+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.118+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ����0����ʱδ֧������
2025-08-01T05:05:00.118+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʱδ֧������������
2025-08-01T05:05:00.118+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderExpireTask       : ��ʼ����δ֧��������������
2025-08-01T05:05:00.119+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.123+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:05:00.123+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:05:00.124+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:00.172+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : �ҵ� 0 ����ʱδ֧������
2025-08-01T05:05:00.172+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : �ɹ�ȡ�� 0 ����ʱδ֧������
2025-08-01T05:05:00.177+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderExpireTask       : �ҵ�0������δ֧������
2025-08-01T05:05:00.177+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderExpireTask       : ��������δ֧��������������
2025-08-01T05:05:00.186+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.f.task.DataConsistencyTask       : �������������
2025-08-01T05:05:00.222+08:00  INFO 44824 --- [jycb-z] [scheduled-task-3] c.j.jycbz.modules.order.task.OrderTask   : δ֧��������ʱ�������
2025-08-01T05:05:00.227+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:05:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:05:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:05:24.553+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:06:00.007+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:06:00.007+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:06:00.007+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:06:00.059+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:06:00.116+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:06:00.116+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:06:00.234+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:06:24.479+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:06:24.480+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:06:24.544+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:07:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:07:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:07:00.008+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:07:00.059+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:07:00.119+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:07:00.119+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:07:00.234+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:07:24.494+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:07:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:07:24.553+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:08:00.000+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:08:00.000+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:08:00.001+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:08:00.054+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:08:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:08:00.134+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:08:00.235+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:08:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:08:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:08:24.557+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:09:00.009+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:09:00.010+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:09:00.011+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:00.064+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:00.117+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:09:00.117+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:09:00.227+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:09:24.370+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderRepairTask       : ��ʼִ�ж����޸�����...
2025-08-01T05:09:24.370+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��ʼִ���豸״̬�������...
2025-08-01T05:09:24.370+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:24.370+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:24.426+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.jycb.jycbz.config.RedisHealthConfig    : Redis���ӽ������ͨ��
2025-08-01T05:09:24.471+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:09:24.471+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:24.494+08:00  INFO 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ����δ֧��������ʱ����
2025-08-01T05:09:24.494+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderRepairTask       : ��⵽0̨��Ҫ������豸��ά���л���ϣ�
2025-08-01T05:09:24.495+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderRepairTask       : �����޸�����ִ����ɣ����޸�0������
2025-08-01T05:09:24.495+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:24.507+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0̨ʹ���е��豸
2025-08-01T05:09:24.507+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:09:24.533+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:09:24.551+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ���ʱ��δ֧������
2025-08-01T05:09:24.577+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0�������ɵĶ�����Ҫ����豸״̬һ����
2025-08-01T05:09:24.577+08:00  INFO 44824 --- [jycb-z] [scheduled-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : �豸״̬�������ִ�����
2025-08-01T05:10:00.005+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:10:00.005+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ����δ֧��������ʱ...
2025-08-01T05:10:00.005+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.f.task.DataConsistencyTask       : ��ʼִ�в���������
2025-08-01T05:10:00.005+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ��鳬ʱδ֧������
2025-08-01T05:10:00.005+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:10:00.006+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.006+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.006+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.062+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.062+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.114+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ����0����ʱδ֧������
2025-08-01T05:10:00.114+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.modules.order.task.OrderCheckTask  : ��ʱδ֧������������
2025-08-01T05:10:00.114+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderExpireTask       : ��ʼ����δ֧��������������
2025-08-01T05:10:00.115+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.118+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:10:00.118+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:10:00.133+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:00.174+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderExpireTask       : �ҵ�0������δ֧������
2025-08-01T05:10:00.174+08:00  INFO 44824 --- [jycb-z] [scheduled-task-5] c.j.j.m.order.task.OrderExpireTask       : ��������δ֧��������������
2025-08-01T05:10:00.183+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : �ҵ� 0 ����ʱδ֧������
2025-08-01T05:10:00.183+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : �ɹ�ȡ�� 0 ����ʱδ֧������
2025-08-01T05:10:00.200+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.m.f.task.DataConsistencyTask       : �������������
2025-08-01T05:10:00.230+08:00  INFO 44824 --- [jycb-z] [scheduled-task-2] c.j.jycbz.modules.order.task.OrderTask   : δ֧��������ʱ�������
2025-08-01T05:10:00.234+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-4] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:10:24.493+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:10:24.494+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:10:24.552+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-1] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:11:00.009+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.jycbz.modules.order.task.OrderTask   : ��ʼ��������ж���ʵʱ���...
2025-08-01T05:11:00.009+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : ��ʼ�����Ҫ������ɵĶ���
2025-08-01T05:11:00.010+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:11:00.061+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:11:00.133+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : û���ҵ������еĶ���
2025-08-01T05:11:00.133+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-3] c.j.j.modules.order.task.OrderCheckTask  : �������ڼ�����
2025-08-01T05:11:00.227+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-5] c.j.jycbz.modules.order.task.OrderTask   : �����ж���ʵʱ��ɴ������
2025-08-01T05:11:24.473+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:11:24.473+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:11:24.534+08:00 DEBUG 44824 --- [jycb-z] [scheduled-task-2] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:11:30.196+08:00  INFO 44824 --- [jycb-z] [File Watcher] rtingClassPathChangeChangedEventListener : Restarting due to 748 class path changes (0 additions, 748 deletions, 0 modifications)
2025-08-01T05:11:30.200+08:00  INFO 44824 --- [jycb-z] [Thread-5] o.s.b.w.e.tomcat.GracefulShutdown        : Commencing graceful shutdown. Waiting for active requests to complete
2025-08-01T05:11:30.342+08:00  INFO 44824 --- [jycb-z] [tomcat-shutdown] o.s.b.w.e.tomcat.GracefulShutdown        : Graceful shutdown complete
2025-08-01T05:11:30.365+08:00  INFO 44824 --- [jycb-z] [Thread-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T05:11:30.366+08:00  INFO 44824 --- [jycb-z] [Thread-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T05:11:30.442+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:11:30.442+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:11:30.442+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:11:30.652+08:00  WARN 44824 --- [jycb-z] [restartedMain] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.jycb.jycbz]' package. Please check your configuration.
2025-08-01T05:11:30.695+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:11:30.696+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:11:30.696+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:11:30.710+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T05:11:30.710+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 266 ms
2025-08-01T05:11:30.849+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
2025-08-01T05:11:30.851+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:11:30.856+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:11:30.868+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:564) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 29 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:127) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:100) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:562) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:269) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:261) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:253) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123) ~[mybatis-3.5.15.jar:3.5.15]
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101) ~[mybatis-3.5.15.jar:3.5.15]
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124) ~[mybatis-3.5.15.jar:3.5.15]
	... 45 common frames omitted

2025-08-01T05:14:44.960+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:14:44.960+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:14:44.960+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:14:45.095+08:00  WARN 44824 --- [jycb-z] [restartedMain] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.jycb.jycbz]' package. Please check your configuration.
2025-08-01T05:14:45.124+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:14:45.126+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:14:45.126+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:14:45.144+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:14:45.144+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 183 ms
2025-08-01T05:14:45.261+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
2025-08-01T05:14:45.263+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:14:45.266+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:14:45.268+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:564) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 29 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:127) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:100) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:562) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:269) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:261) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:253) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123) ~[mybatis-3.5.15.jar:3.5.15]
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101) ~[mybatis-3.5.15.jar:3.5.15]
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124) ~[mybatis-3.5.15.jar:3.5.15]
	... 45 common frames omitted

2025-08-01T05:15:20.701+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:15:20.702+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:15:20.702+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:15:20.867+08:00  WARN 44824 --- [jycb-z] [restartedMain] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.jycb.jycbz]' package. Please check your configuration.
2025-08-01T05:15:20.898+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:15:20.898+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:15:20.898+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:15:20.915+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-2].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:15:20.915+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 212 ms
2025-08-01T05:15:21.051+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
2025-08-01T05:15:21.054+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:15:21.057+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:15:21.060+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:564) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 29 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:127) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:100) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:562) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:269) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:261) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:253) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123) ~[mybatis-3.5.15.jar:3.5.15]
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101) ~[mybatis-3.5.15.jar:3.5.15]
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124) ~[mybatis-3.5.15.jar:3.5.15]
	... 45 common frames omitted

2025-08-01T05:15:27.693+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:15:27.694+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:15:27.694+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:15:27.844+08:00  WARN 44824 --- [jycb-z] [restartedMain] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.jycb.jycbz]' package. Please check your configuration.
2025-08-01T05:15:27.875+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:15:27.876+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:15:27.876+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:15:27.891+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-3].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:15:27.891+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 196 ms
2025-08-01T05:15:28.012+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
2025-08-01T05:15:28.014+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:15:28.019+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:15:28.021+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:564) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 29 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:127) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:100) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:562) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:269) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:261) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:253) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123) ~[mybatis-3.5.15.jar:3.5.15]
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101) ~[mybatis-3.5.15.jar:3.5.15]
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124) ~[mybatis-3.5.15.jar:3.5.15]
	... 45 common frames omitted

2025-08-01T05:17:22.910+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:17:22.910+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:17:22.911+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:17:23.048+08:00  WARN 44824 --- [jycb-z] [restartedMain] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.jycb.jycbz]' package. Please check your configuration.
2025-08-01T05:17:23.080+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:17:23.081+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:17:23.081+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:17:23.098+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-4].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:17:23.098+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 187 ms
2025-08-01T05:17:23.222+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
2025-08-01T05:17:23.224+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:17:23.229+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:17:23.231+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:564) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 29 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:127) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:100) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:562) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:269) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:261) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:253) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123) ~[mybatis-3.5.15.jar:3.5.15]
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101) ~[mybatis-3.5.15.jar:3.5.15]
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124) ~[mybatis-3.5.15.jar:3.5.15]
	... 45 common frames omitted

2025-08-01T05:17:32.706+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:17:32.707+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:17:32.707+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:17:33.293+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:17:33.294+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:17:33.294+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:17:33.310+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-5].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:17:33.310+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 603 ms
2025-08-01T05:17:33.595+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataPermissionAspect' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\common\aspect\DataPermissionAspect.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'adminModuleServiceImpl' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\modules\admin\service\impl\AdminServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'cacheService' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\common\service\CacheService.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01T05:17:33.596+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:17:33.598+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:17:33.602+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.jycb.jycbz.common.service.CacheService required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-08-01T05:17:44.482+08:00  INFO 44408 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44408 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:17:44.483+08:00 DEBUG 44408 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:17:44.484+08:00  INFO 44408 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:17:44.508+08:00  INFO 44408 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T05:17:44.508+08:00  INFO 44408 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T05:17:45.743+08:00  INFO 44408 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:17:45.752+08:00  INFO 44408 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:17:45.753+08:00  INFO 44408 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:17:45.792+08:00  INFO 44408 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T05:17:45.792+08:00  INFO 44408 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1284 ms
2025-08-01T05:17:46.331+08:00  WARN 44408 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'dataPermissionAspect' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\common\aspect\DataPermissionAspect.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'adminModuleServiceImpl' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\modules\admin\service\impl\AdminServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 3: Error creating bean with name 'cacheService' defined in file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\com\jycb\jycbz\common\service\CacheService.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.data.redis.core.RedisTemplate<java.lang.String, java.lang.Object>' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-01T05:17:46.333+08:00  INFO 44408 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:17:46.340+08:00  INFO 44408 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:17:46.350+08:00 ERROR 44408 --- [jycb-z] [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.jycb.jycbz.common.service.CacheService required a bean of type 'org.springframework.data.redis.core.RedisTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.data.redis.core.RedisTemplate' in your configuration.

2025-08-01T05:19:13.035+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:19:13.035+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:19:13.035+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:19:13.206+08:00  WARN 44824 --- [jycb-z] [restartedMain] o.m.s.mapper.ClassPathMapperScanner      : No MyBatis mapper was found in '[com.jycb.jycbz]' package. Please check your configuration.
2025-08-01T05:19:13.243+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:19:13.244+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:19:13.244+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:19:13.259+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-6].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:19:13.260+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 223 ms
2025-08-01T05:19:13.400+08:00  WARN 44824 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
2025-08-01T05:19:13.403+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Stopping service [Tomcat]
2025-08-01T05:19:13.408+08:00  INFO 44824 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:19:13.409+08:00 ERROR 44824 --- [jycb-z] [restartedMain] o.s.boot.SpringApplication               : Application run failed

org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:657) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:645) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception with message: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:199) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiateWithFactoryMethod(SimpleInstantiationStrategy.java:88) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:168) ~[spring-beans-6.2.8.jar:6.2.8]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-6.2.8.jar:6.2.8]
	... 26 common frames omitted
Caused by: java.io.IOException: Failed to parse mapping resource: 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:564) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:437) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:595) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:224) ~[mybatis-plus-spring-boot-autoconfigure-3.5.5.jar:3.5.5]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.lambda$instantiate$0(SimpleInstantiationStrategy.java:171) ~[spring-beans-6.2.8.jar:6.2.8]
	... 29 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error parsing Mapper XML. The XML location is 'file [C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes\mapper\admin\AdminMapper.xml]'. Cause: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:127) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:100) ~[mybatis-3.5.15.jar:3.5.15]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:562) ~[mybatis-plus-extension-3.5.5.jar:3.5.5]
	... 37 common frames omitted
Caused by: org.apache.ibatis.builder.BuilderException: Error resolving class. Cause: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:269) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElement(XMLMapperBuilder.java:261) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.resultMapElements(XMLMapperBuilder.java:253) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.configurationElement(XMLMapperBuilder.java:123) ~[mybatis-3.5.15.jar:3.5.15]
	... 39 common frames omitted
Caused by: org.apache.ibatis.type.TypeException: Could not resolve type alias 'com.jycb.jycbz.modules.admin.entity.Admin'.  Cause: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:128) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveAlias(BaseBuilder.java:132) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.builder.BaseBuilder.resolveClass(BaseBuilder.java:101) ~[mybatis-3.5.15.jar:3.5.15]
	... 43 common frames omitted
Caused by: java.lang.ClassNotFoundException: Cannot find class: com.jycb.jycbz.modules.admin.entity.Admin
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:226) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.ClassLoaderWrapper.classForName(ClassLoaderWrapper.java:103) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.io.Resources.classForName(Resources.java:322) ~[mybatis-3.5.15.jar:3.5.15]
	at org.apache.ibatis.type.TypeAliasRegistry.resolveAlias(TypeAliasRegistry.java:124) ~[mybatis-3.5.15.jar:3.5.15]
	... 45 common frames omitted

2025-08-01T05:19:22.493+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 44824 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:19:22.493+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:19:22.493+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:19:23.088+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:19:23.088+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:19:23.088+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:19:23.103+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat-7].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-08-01T05:19:23.103+08:00  INFO 44824 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 609 ms
2025-08-01T05:19:23.214+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Starting JycbZApplication using Java 17.0.12 with PID 8040 (C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z\target\classes started by wuji1 in C:\Users\<USER>\Desktop\��ҹ�Ǳ�-��ʽ��Ŀ-ǰ��-���\���\jycb-z)
2025-08-01T05:19:23.216+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Running with Spring Boot v3.5.3, Spring v6.2.8
2025-08-01T05:19:23.216+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : The following 1 profile is active: "dev"
2025-08-01T05:19:23.245+08:00  INFO 8040 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01T05:19:23.245+08:00  INFO 8040 --- [jycb-z] [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01T05:19:23.436+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : ��ʼ��RedisTemplate...
2025-08-01T05:19:23.436+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : RedisTemplate��ʼ�����
2025-08-01T05:19:23.694+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ��΢��С�������...
2025-08-01T05:19:23.694+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T05:19:23.694+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T05:19:23.695+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:23.696+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-01T05:19:24.199+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2ba4b284
2025-08-01T05:19:24.199+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-01T05:19:24.308+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T05:19:24.308+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T05:19:24.308+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T05:19:24.309+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T05:19:24.309+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T05:19:24.309+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T05:19:24.309+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T05:19:24.309+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T05:19:24.309+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С�������ó�ʼ�����
2025-08-01T05:19:24.342+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttpClient, ���ӳ�ʱ: 10��, ����ʱ: 10��, д��ʱ: 10��
2025-08-01T05:19:24.342+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttp���ӳ�, ����������: 5, ��������ʱ��: 5����
2025-08-01T05:19:24.347+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:24.424+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����ݿ����΢��֧�����óɹ�: mchId=17****72, appId=wx****76, serialNo=15****1E
2025-08-01T05:19:24.424+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ������ݿ����˽Կ������: 1732
2025-08-01T05:19:24.424+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ�����
2025-08-01T05:19:24.536+08:00  INFO 8040 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port 8081 (http)
2025-08-01T05:19:24.545+08:00  INFO 8040 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01T05:19:24.546+08:00  INFO 8040 --- [jycb-z] [restartedMain] o.apache.catalina.core.StandardEngine    : Starting Servlet engine: [Apache Tomcat/10.1.42]
2025-08-01T05:19:24.575+08:00  INFO 8040 --- [jycb-z] [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01T05:19:24.575+08:00  INFO 8040 --- [jycb-z] [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1329 ms
2025-08-01T05:19:25.194+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : ��ʼ��RedisTemplate...
2025-08-01T05:19:25.234+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : RedisTemplate��ʼ�����
2025-08-01T05:19:25.531+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ��΢��С�������...
2025-08-01T05:19:25.531+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T05:19:25.531+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T05:19:25.558+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:25.564+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T05:19:26.120+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1a901648
2025-08-01T05:19:26.121+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T05:19:26.242+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T05:19:26.243+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T05:19:26.243+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T05:19:26.243+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T05:19:26.243+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T05:19:26.243+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T05:19:26.243+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T05:19:26.244+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T05:19:26.351+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С�������ó�ʼ�����
2025-08-01T05:19:26.381+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttpClient, ���ӳ�ʱ: 10��, ����ʱ: 10��, д��ʱ: 10��
2025-08-01T05:19:26.390+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : ��ʼ��Redis���������
2025-08-01T05:19:26.391+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : Redis�����������ʼ����ɣ�������8���ض�����
2025-08-01T05:19:26.419+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.OkHttpConfig       : ��ʼ��OkHttp���ӳ�, ����������: 5, ��������ʱ��: 5����
2025-08-01T05:19:26.431+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:26.505+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����ݿ����΢��֧�����óɹ�: mchId=17****72, appId=wx****76, serialNo=15****1E
2025-08-01T05:19:26.505+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ������ݿ����˽Կ������: 1732
2025-08-01T05:19:26.505+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ�����
2025-08-01T05:19:26.655+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.RedisHealthConfig    : Redis���Ӽ��ɹ������湦��������
2025-08-01T05:19:26.660+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ����΢��֧��HttpClient������
2025-08-01T05:19:26.660+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T05:19:26.660+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T05:19:26.661+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T05:19:26.661+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T05:19:26.661+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAConfig...
2025-08-01T05:19:26.661+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T05:19:27.188+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ�
2025-08-01T05:19:27.188+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧��HttpClient�����ɹ�
2025-08-01T05:19:27.191+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ʹ��RSAAutoCertificateConfig����΢��֧������
2025-08-01T05:19:27.191+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T05:19:27.191+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T05:19:27.191+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T05:19:27.191+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T05:19:27.191+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAAutoCertificateConfig...
2025-08-01T05:19:27.191+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T05:19:27.435+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ���Bean����: wxPayConfigBean
2025-08-01T05:19:27.435+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ���ɹ�
2025-08-01T05:19:27.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.system.config.SystemTaskConfig   : ϵͳ��ʱ�����������ʼ����ɣ��̳߳ش�С: 0
2025-08-01T05:19:27.518+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �û���������������ע��
2025-08-01T05:19:27.518+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �豸����Ȩ����֤��������ע��
2025-08-01T05:19:27.851+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01T05:19:27.895+08:00  INFO 44824 --- [jycb-z] [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port 8081 (http) with context path '/'
2025-08-01T05:19:27.898+08:00 DEBUG 44824 --- [jycb-z] [system-task-5] c.j.j.m.f.t.CommissionCacheRefreshTask   : ��ʼ���ֳ����û��潡��״̬...
2025-08-01T05:19:27.898+08:00  INFO 44824 --- [jycb-z] [system-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��ʼִ���豸״̬�������...
2025-08-01T05:19:27.898+08:00  INFO 44824 --- [jycb-z] [system-task-6] c.j.j.m.order.task.OrderRepairTask       : ��ʼִ�ж����޸�����...
2025-08-01T05:19:27.899+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.JycbZApplication          : Started JycbZApplication in 5.418 seconds (process running for 1211.498)
2025-08-01T05:19:27.900+08:00 DEBUG 44824 --- [jycb-z] [system-task-6] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:27.900+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.DocumentationConfig  : ----------------------------------------------------------
	Ӧ�ó���"jycb-z"����������......
	�ӿ��ĵ����� URL:
	����: 		http://localhost:8081/doc.html
	�ⲿ: 	http://*************:8081/doc.html
	�����ļ�: 	[dev]
----------------------------------------------------------
2025-08-01T05:19:27.900+08:00 DEBUG 44824 --- [jycb-z] [system-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:27.900+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : === �豸ģ�������޸���������ʼִ�� ===
2025-08-01T05:19:27.900+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : ��ʼ���������豸��ػ���...
2025-08-01T05:19:27.957+08:00 DEBUG 44824 --- [jycb-z] [system-task-3] c.jycb.jycbz.config.RedisHealthConfig    : Redis���ӽ������ͨ��
2025-08-01T05:19:27.964+08:00  WARN 44824 --- [jycb-z] [system-task-5] m.f.s.i.CommissionConfigCacheServiceImpl : �ӻ����ȡ�ֳ�����ʧ�ܣ��������commission:config:system������Could not read JSON:Unrecognized field "parentConfigType" (class com.jycb.jycbz.modules.finance.entity.CommissionConfig), not marked as ignorable (25 known properties: "updateBy", "partnerId", "level", "parentConfigId", "partnerRate", "createTime", "name", "shopId", "status", "platformRate", "shopRatio", "shopRate", "relationId", "platformRatio", "id", "configType", "configId", "entityId", "remark", "entityRate", "entityRatio", "useParentConfig", "updateTime", "createBy", "partnerRatio"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 639] (through reference chain: com.jycb.jycbz.modules.finance.entity.CommissionConfig["parentConfigType"]) ����ֱ�Ӳ�ѯ���ݿ�
2025-08-01T05:19:28.020+08:00  INFO 44824 --- [jycb-z] [system-task-1] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ����δ֧��������ʱ����
2025-08-01T05:19:28.021+08:00 DEBUG 44824 --- [jycb-z] [system-task-1] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:28.021+08:00 DEBUG 44824 --- [jycb-z] [system-task-7] c.j.j.m.order.task.OrderScheduledTask    : ��ʼ�����������������
2025-08-01T05:19:28.021+08:00  INFO 44824 --- [jycb-z] [system-task-6] c.j.j.m.order.task.OrderRepairTask       : ��⵽0̨��Ҫ������豸��ά���л���ϣ�
2025-08-01T05:19:28.021+08:00  INFO 44824 --- [jycb-z] [system-task-6] c.j.j.m.order.task.OrderRepairTask       : �����޸�����ִ����ɣ����޸�0������
2025-08-01T05:19:28.022+08:00 DEBUG 44824 --- [jycb-z] [system-task-7] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:28.024+08:00 DEBUG 44824 --- [jycb-z] [system-task-5] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:28.024+08:00  INFO 44824 --- [jycb-z] [system-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0̨ʹ���е��豸
2025-08-01T05:19:28.025+08:00 DEBUG 44824 --- [jycb-z] [system-task-4] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:28.094+08:00 DEBUG 44824 --- [jycb-z] [system-task-7] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ������еĶ���
2025-08-01T05:19:28.096+08:00 DEBUG 44824 --- [jycb-z] [system-task-1] c.j.j.m.order.task.OrderScheduledTask    : û���ҵ���ʱ��δ֧������
2025-08-01T05:19:28.097+08:00  INFO 44824 --- [jycb-z] [system-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : ��⵽0�������ɵĶ�����Ҫ����豸״̬һ����
2025-08-01T05:19:28.097+08:00  INFO 44824 --- [jycb-z] [system-task-4] c.j.j.m.d.task.DeviceStatusCheckTask     : �豸״̬�������ִ�����
2025-08-01T05:19:28.219+08:00 DEBUG 44824 --- [jycb-z] [system-task-5] c.j.j.m.f.t.CommissionCacheRefreshTask   : �ֳ����û��潡��״̬���ͨ��
2025-08-01T05:19:28.388+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ��������豸��ػ���
2025-08-01T05:19:28.557+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : ��ʼ��Redis���������
2025-08-01T05:19:28.564+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.RedisCacheConfig   : Redis�����������ʼ����ɣ�������8���ض�����
2025-08-01T05:19:28.815+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �����豸��ػ���������
2025-08-01T05:19:28.815+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : �豸����������ɣ��ܹ�ɾ�� 0 ��key
2025-08-01T05:19:28.974+08:00  INFO 8040 --- [jycb-z] [restartedMain] c.jycb.jycbz.config.RedisHealthConfig    : Redis���Ӽ��ɹ������湦��������
2025-08-01T05:19:28.980+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ����΢��֧��HttpClient������
2025-08-01T05:19:28.980+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T05:19:28.980+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T05:19:28.981+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T05:19:28.981+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T05:19:28.981+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAConfig...
2025-08-01T05:19:28.982+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T05:19:29.535+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ�
2025-08-01T05:19:29.536+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧��HttpClient�����ɹ�
2025-08-01T05:19:29.544+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ʹ��RSAAutoCertificateConfig����΢��֧������
2025-08-01T05:19:29.544+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����˽Կ...
2025-08-01T05:19:29.544+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ��PEM��ʽ����ȡ���ݲ���
2025-08-01T05:19:29.544+08:00 DEBUG 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ˽Կ���ݴ�����ɣ�׼������
2025-08-01T05:19:29.544+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �ɹ�����˽Կ���㷨: RSA
2025-08-01T05:19:29.544+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ��ʼ����RSAAutoCertificateConfig...
2025-08-01T05:19:29.545+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : �����������: mchId=17****, serialNo=15****, privateKey=�Ѵ���, apiV3Key=5b****
2025-08-01T05:19:29.791+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : RSAAutoCertificateConfig�����ɹ���Bean����: wxPayConfigBean
2025-08-01T05:19:29.791+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxPayConfig        : ΢��֧�����ó�ʼ���ɹ�
2025-08-01T05:19:29.793+08:00  INFO 8040 --- [jycb-z] [restartedMain] c.j.j.m.system.config.SystemTaskConfig   : ϵͳ��ʱ�����������ʼ����ɣ��̳߳ش�С: 0
2025-08-01T05:19:29.817+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : === �豸ģ�������޸���ɣ����л��������� ===
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : �޸����ݣ�
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 1. �޸����豸��ѯ�߼���ǿ��Ҫ�� is_bound=1
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 2. �޸����豸ͳ�Ʋ�ѯ��ֻͳ���Ѱ��豸
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 3. �����������豸��ػ���
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.c.DeviceFixApplicationRunner   : 4. �ŵ�/�����̽��޷��ٿ���δ���豸
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ��ʼ��ʼ���ֳ����û���...
2025-08-01T05:19:29.817+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ������зֳ����û���...
2025-08-01T05:19:29.900+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �û���������������ע��
2025-08-01T05:19:29.900+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WebMvcConfig       : �豸����Ȩ����֤��������ע��
2025-08-01T05:19:29.934+08:00  INFO 44824 --- [jycb-z] [restartedMain] m.f.s.i.CommissionConfigCacheServiceImpl : ������������û��棬�� 1 ��
2025-08-01T05:19:29.936+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.common.context.UserContextHolder   : �����û�������ʧ��: �� web �������޷���ȡ HttpServletRequest
2025-08-01T05:19:29.936+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : ���зֳ����û��������
2025-08-01T05:19:29.936+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : Ԥ��ϵͳ�ֳ����û���...
2025-08-01T05:19:29.936+08:00 DEBUG 44824 --- [jycb-z] [commission-task-1] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ʼִ�У��û�������������: userId=null, username=null, isWebContext=false
2025-08-01T05:19:29.936+08:00  INFO 44824 --- [jycb-z] [commission-task-1] .j.j.m.f.l.CommissionConfigCacheListener : ���������ֳ�������������¼�����������: all
2025-08-01T05:19:29.936+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.common.context.UserContextHolder   : �����û�������ʧ��: �� web �������޷���ȡ HttpServletRequest
2025-08-01T05:19:29.937+08:00 DEBUG 44824 --- [jycb-z] [commission-task-2] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ʼִ�У��û�������������: userId=null, username=null, isWebContext=false
2025-08-01T05:19:29.937+08:00  INFO 44824 --- [jycb-z] [commission-task-2] .j.j.m.f.l.CommissionConfigCacheListener : ��ʼ�ֳ����û���Ԥ�ȣ�ԭ��: Ӧ������Ԥ��
2025-08-01T05:19:29.991+08:00  INFO 44824 --- [jycb-z] [commission-task-1] .j.j.m.f.l.CommissionConfigCacheListener : �����ֳ�������������¼�������ɣ���������л���
2025-08-01T05:19:29.991+08:00 DEBUG 44824 --- [jycb-z] [commission-task-1] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ִ����ɣ��û������������
2025-08-01T05:19:30.058+08:00 ERROR 44824 --- [jycb-z] [commission-task-2] .j.j.m.f.l.CommissionConfigCacheListener : �ֳ����û���Ԥ��ʧ��

org.springframework.data.redis.serializer.SerializationException: Could not read JSON:Unrecognized field "parentConfigType" (class com.jycb.jycbz.modules.finance.entity.CommissionConfig), not marked as ignorable (25 known properties: "updateBy", "partnerId", "level", "parentConfigId", "partnerRate", "createTime", "name", "shopId", "status", "platformRate", "shopRatio", "shopRate", "relationId", "platformRatio", "id", "configType", "configId", "entityId", "remark", "entityRate", "entityRatio", "useParentConfig", "updateTime", "createBy", "partnerRatio"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 650] (through reference chain: com.jycb.jycbz.modules.finance.entity.CommissionConfig["parentConfigType"]) 
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:311) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:281) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.DefaultRedisElementReader.read(DefaultRedisElementReader.java:46) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.RedisSerializationContext$SerializationPair.read(RedisSerializationContext.java:277) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.cache.RedisCache.deserializeCacheValue(RedisCache.java:361) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.cache.RedisCache.lookup(RedisCache.java:187) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.cache.support.AbstractValueAdaptingCache.get(AbstractValueAdaptingCache.java:58) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator.get(TransactionAwareCacheDecorator.java:82) ~[spring-context-support-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doGet(AbstractCacheInvoker.java:78) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.findInCaches(CacheAspectSupport.java:574) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.findCachedValue(CacheAspectSupport.java:523) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:446) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:410) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:65) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728) ~[spring-aop-6.2.8.jar:6.2.8]
	at com.jycb.jycbz.modules.finance.service.impl.CommissionConfigServiceImpl$$SpringCGLIB$$0.getSystemConfig(<generated>) ~[classes/:na]
	at com.jycb.jycbz.modules.finance.service.impl.CommissionConfigCacheServiceImpl.getSystemConfigWithCache(CommissionConfigCacheServiceImpl.java:119) ~[classes/:na]
	at com.jycb.jycbz.modules.finance.listener.CommissionConfigCacheListener.handleCommissionConfigWarmup(CommissionConfigCacheListener.java:197) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:114) ~[spring-aop-6.2.8.jar:6.2.8]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264) ~[na:na]
	at com.jycb.jycbz.common.async.ContextAwareTaskDecorator.lambda$decorate$0(ContextAwareTaskDecorator.java:30) ~[classes/:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136) ~[na:na]
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635) ~[na:na]
	at java.base/java.lang.Thread.run(Thread.java:842) ~[na:na]
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "parentConfigType" (class com.jycb.jycbz.modules.finance.entity.CommissionConfig), not marked as ignorable (25 known properties: "updateBy", "partnerId", "level", "parentConfigId", "partnerRate", "createTime", "name", "shopId", "status", "platformRate", "shopRatio", "shopRate", "relationId", "platformRatio", "id", "configType", "configId", "entityId", "remark", "entityRate", "entityRatio", "useParentConfig", "updateTime", "createBy", "partnerRatio"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 650] (through reference chain: com.jycb.jycbz.modules.finance.entity.CommissionConfig["parentConfigType"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1180) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2244) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1823) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1801) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:308) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeOther(BeanDeserializer.java:207) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:179) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:169) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:135) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeWithType(BeanDeserializerBase.java:1384) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4971) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3989) ~[jackson-databind-2.19.1.jar:2.19.1]
	at org.springframework.data.redis.serializer.JacksonObjectReader.lambda$create$0(JacksonObjectReader.java:54) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:309) ~[spring-data-redis-3.5.1.jar:3.5.1]
	... 31 common frames omitted

2025-08-01T05:19:30.058+08:00 ERROR 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : Ԥ��ϵͳ�ֳ����û���ʧ��

org.springframework.data.redis.serializer.SerializationException: Could not read JSON:Unrecognized field "parentConfigType" (class com.jycb.jycbz.modules.finance.entity.CommissionConfig), not marked as ignorable (25 known properties: "updateBy", "partnerId", "level", "parentConfigId", "partnerRate", "createTime", "name", "shopId", "status", "platformRate", "shopRatio", "shopRate", "relationId", "platformRatio", "id", "configType", "configId", "entityId", "remark", "entityRate", "entityRatio", "useParentConfig", "updateTime", "createBy", "partnerRatio"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 650] (through reference chain: com.jycb.jycbz.modules.finance.entity.CommissionConfig["parentConfigType"]) 
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:311) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:281) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.DefaultRedisElementReader.read(DefaultRedisElementReader.java:46) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.RedisSerializationContext$SerializationPair.read(RedisSerializationContext.java:277) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.cache.RedisCache.deserializeCacheValue(RedisCache.java:361) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.cache.RedisCache.lookup(RedisCache.java:187) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.cache.support.AbstractValueAdaptingCache.get(AbstractValueAdaptingCache.java:58) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.transaction.TransactionAwareCacheDecorator.get(TransactionAwareCacheDecorator.java:82) ~[spring-context-support-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.AbstractCacheInvoker.doGet(AbstractCacheInvoker.java:78) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.findInCaches(CacheAspectSupport.java:574) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.findCachedValue(CacheAspectSupport.java:523) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:446) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheAspectSupport.execute(CacheAspectSupport.java:410) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.cache.interceptor.CacheInterceptor.invoke(CacheInterceptor.java:65) ~[spring-context-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184) ~[spring-aop-6.2.8.jar:6.2.8]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:728) ~[spring-aop-6.2.8.jar:6.2.8]
	at com.jycb.jycbz.modules.finance.service.impl.CommissionConfigServiceImpl$$SpringCGLIB$$0.getSystemConfig(<generated>) ~[classes/:na]
	at com.jycb.jycbz.modules.finance.service.impl.CommissionConfigCacheServiceImpl.getSystemConfigWithCache(CommissionConfigCacheServiceImpl.java:119) ~[classes/:na]
	at com.jycb.jycbz.modules.finance.config.CommissionCacheInitializer.warmupSystemCommissionCache(CommissionCacheInitializer.java:82) ~[classes/:na]
	at com.jycb.jycbz.modules.finance.config.CommissionCacheInitializer.run(CommissionCacheInitializer.java:37) ~[classes/:na]
	at org.springframework.boot.SpringApplication.lambda$callRunner$4(SpringApplication.java:784) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.util.function.ThrowingConsumer$1.acceptWithException(ThrowingConsumer.java:82) ~[spring-core-6.2.8.jar:6.2.8]
	at org.springframework.util.function.ThrowingConsumer.accept(ThrowingConsumer.java:60) ~[spring-core-6.2.8.jar:6.2.8]
	at org.springframework.util.function.ThrowingConsumer$1.accept(ThrowingConsumer.java:86) ~[spring-core-6.2.8.jar:6.2.8]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:796) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:784) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.lambda$callRunners$3(SpringApplication.java:772) ~[spring-boot-3.5.3.jar:3.5.3]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:183) ~[na:na]
	at java.base/java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:357) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:510) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:150) ~[na:na]
	at java.base/java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:173) ~[na:na]
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234) ~[na:na]
	at java.base/java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:596) ~[na:na]
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:772) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:325) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1361) ~[spring-boot-3.5.3.jar:3.5.3]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1350) ~[spring-boot-3.5.3.jar:3.5.3]
	at com.jycb.jycbz.JycbZApplication.main(JycbZApplication.java:18) ~[classes/:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:na]
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77) ~[na:na]
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:na]
	at java.base/java.lang.reflect.Method.invoke(Method.java:568) ~[na:na]
	at org.springframework.boot.devtools.restart.RestartLauncher.run(RestartLauncher.java:50) ~[spring-boot-devtools-3.5.3.jar:3.5.3]
Caused by: com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException: Unrecognized field "parentConfigType" (class com.jycb.jycbz.modules.finance.entity.CommissionConfig), not marked as ignorable (25 known properties: "updateBy", "partnerId", "level", "parentConfigId", "partnerRate", "createTime", "name", "shopId", "status", "platformRate", "shopRatio", "shopRate", "relationId", "platformRatio", "id", "configType", "configId", "entityId", "remark", "entityRate", "entityRatio", "useParentConfig", "updateTime", "createBy", "partnerRatio"])
 at [Source: REDACTED (`StreamReadFeature.INCLUDE_SOURCE_IN_LOCATION` disabled); line: 1, column: 650] (through reference chain: com.jycb.jycbz.modules.finance.entity.CommissionConfig["parentConfigType"])
	at com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException.from(UnrecognizedPropertyException.java:61) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.DeserializationContext.handleUnknownProperty(DeserializationContext.java:1180) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.std.StdDeserializer.handleUnknownProperty(StdDeserializer.java:2244) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownProperty(BeanDeserializerBase.java:1823) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.handleUnknownVanilla(BeanDeserializerBase.java:1801) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.vanillaDeserialize(BeanDeserializer.java:308) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer._deserializeOther(BeanDeserializer.java:207) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializer.deserialize(BeanDeserializer.java:179) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer._deserializeTypedForId(AsPropertyTypeDeserializer.java:169) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.jsontype.impl.AsPropertyTypeDeserializer.deserializeTypedFromObject(AsPropertyTypeDeserializer.java:135) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.BeanDeserializerBase.deserializeWithType(BeanDeserializerBase.java:1384) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.impl.TypeWrappedDeserializer.deserialize(TypeWrappedDeserializer.java:74) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.deser.DefaultDeserializationContext.readRootValue(DefaultDeserializationContext.java:342) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.ObjectMapper._readMapAndClose(ObjectMapper.java:4971) ~[jackson-databind-2.19.1.jar:2.19.1]
	at com.fasterxml.jackson.databind.ObjectMapper.readValue(ObjectMapper.java:3989) ~[jackson-databind-2.19.1.jar:2.19.1]
	at org.springframework.data.redis.serializer.JacksonObjectReader.lambda$create$0(JacksonObjectReader.java:54) ~[spring-data-redis-3.5.1.jar:3.5.1]
	at org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer.deserialize(GenericJackson2JsonRedisSerializer.java:309) ~[spring-data-redis-3.5.1.jar:3.5.1]
	... 44 common frames omitted

2025-08-01T05:19:30.059+08:00 DEBUG 44824 --- [jycb-z] [commission-task-2] c.j.j.c.async.ContextAwareTaskDecorator  : �첽����ִ����ɣ��û������������
2025-08-01T05:19:30.060+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.f.c.CommissionCacheInitializer   : �ֳ����û����ʼ�����
2025-08-01T05:19:30.175+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ʼ���ϵͳ����...
2025-08-01T05:19:30.175+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.234+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ�������ü������
2025-08-01T05:19:30.234+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - system_name: ���˷������޹���ϵͳ
2025-08-01T05:19:30.234+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - system_logo: /static/logo.png
2025-08-01T05:19:30.234+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ�������ü����ɣ�������������Ѵ���
2025-08-01T05:19:30.235+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С�������ü������
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - msgDataFormat: JSON
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - appid: wx23b7c6a78842ee76
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - aesKey: 
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wechat_token: 
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret: 63****d6
2025-08-01T05:19:30.292+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - token: 
2025-08-01T05:19:30.293+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wechat_aesKey: 
2025-08-01T05:19:30.293+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С�������ü����ɣ�����������ȱʧ: [aesKey, token]
2025-08-01T05:19:30.294+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.352+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'aesKey' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wechat', ���������ϼ�...
2025-08-01T05:19:30.353+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.420+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wechat_aesKey' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:30.420+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.469+08:00  WARN 8040 --- [jycb-z] [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : Unable to start LiveReload server
2025-08-01T05:19:30.497+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'token' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wechat', ���������ϼ�...
2025-08-01T05:19:30.498+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.549+08:00  WARN 8040 --- [jycb-z] [restartedMain] ConfigServletWebServerApplicationContext : Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-08-01T05:19:30.556+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wechat_token' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:30.556+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.557+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T05:19:30.559+08:00  INFO 8040 --- [jycb-z] [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T05:19:30.566+08:00  INFO 8040 --- [jycb-z] [restartedMain] .s.b.a.l.ConditionEvaluationReportLogger : 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-01T05:19:30.576+08:00 ERROR 8040 --- [jycb-z] [restartedMain] o.s.b.d.LoggingFailureAnalysisReporter   : 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8081 was already in use.

Action:

Identify and stop the process that's listening on port 8081 or configure this application to listen on another port.

2025-08-01T05:19:30.623+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����(����)���ü������
2025-08-01T05:19:30.623+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_aesKey: 
2025-08-01T05:19:30.623+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_secret: 63****d6
2025-08-01T05:19:30.623+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_token: 
2025-08-01T05:19:30.623+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_msgDataFormat: JSON
2025-08-01T05:19:30.623+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_miniapp_appid: wx23b7c6a78842ee76
2025-08-01T05:19:30.623+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����(����)���ü����ɣ�����������ȱʧ: [msgDataFormat, appid, aesKey, secret, token]
2025-08-01T05:19:30.623+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.695+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'msgDataFormat' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:19:30.695+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.753+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_msgDataFormat' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:30.754+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.815+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'appid' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:19:30.816+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.870+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_appid' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:30.870+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.870+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'aesKey' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:19:30.871+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.932+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_aesKey' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:30.933+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:30.991+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'secret' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:19:30.991+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.056+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_secret' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:31.056+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.056+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'token' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_miniapp', ���������ϼ�...
2025-08-01T05:19:31.057+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.109+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_miniapp_token' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:31.110+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.173+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧�����ü������
2025-08-01T05:19:31.173+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - privateKey: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
2025-08-01T05:19:31.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - wx_pay_appId: wx23b7c6a78842ee76
2025-08-01T05:19:31.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - mchId: 1721424072
2025-08-01T05:19:31.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - apiV3Key: 5bKMsetvmbbsaPCUY8QrtyAEx3aZDuB7
2025-08-01T05:19:31.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - merchantSerialNumber: 15878634CBF0536B67E993E13AFE7C6A6739131E
2025-08-01T05:19:31.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - notifyUrl: https://api.example.com/api/wx/pay/callback/notify
2025-08-01T05:19:31.174+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - key: 5b****B7
2025-08-01T05:19:31.174+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧�����ü����ɣ�����������ȱʧ: [appId]
2025-08-01T05:19:31.175+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.234+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ������key 'appId' �����ݿ����Ѵ��ڣ�������������Ϊ: 'wechat', ��ǰ���������Ϊ: 'wx_pay', ���������ϼ�...
2025-08-01T05:19:31.234+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.300+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��ϼ� 'wx_pay_appId' �Ѵ��ڣ�����Ҫ���´���
2025-08-01T05:19:31.300+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.376+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��Ѷ��COS���ü������
2025-08-01T05:19:31.376+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret_id: yo****id
2025-08-01T05:19:31.376+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - secret_key: yo****ey
2025-08-01T05:19:31.376+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - bucket_name: your-bucket-name
2025-08-01T05:19:31.376+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - region: ap-guangzhou
2025-08-01T05:19:31.376+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ��Ѷ��COS���ü����ɣ�������������Ѵ���
2025-08-01T05:19:31.376+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : �������ü������
2025-08-01T05:19:31.438+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - withdraw_min_amount: 1.00
2025-08-01T05:19:31.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     :   - withdraw_fee_rate: 0.006
2025-08-01T05:19:31.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : �������ü����ɣ�������������Ѵ���
2025-08-01T05:19:31.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ˢ��΢��С��������...
2025-08-01T05:19:31.439+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��ʼ�����ݿ����΢��С��������...
2025-08-01T05:19:31.439+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ���Դ��������� wechat ��������
2025-08-01T05:19:31.440+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.440+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �ɹ��������ã�����������: 7
2025-08-01T05:19:31.440+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ��������: {msgDataFormat=JSON, appid=wx23b7c6a78842ee76, aesKey=, wechat_token=, secret=63374942cd14aa34112354b2999a6ed6, token=, wechat_aesKey=}
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : �����ݿ����΢��С�������óɹ�
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : appid: wx2****e76
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : secret: 633****ed6
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : token�Ƿ�����: false
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : aesKey�Ƿ�����: false
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : msgDataFormat: JSON
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] com.jycb.jycbz.config.WxMiniAppConfig    : ΢��С��������ˢ�����
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��С����������ˢ��
2025-08-01T05:19:31.440+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.a.service.impl.WxPayServiceImpl  : ��WxPayConfig��ȡ���óɹ�: appId=wx****76, mchId=17****72
2025-08-01T05:19:31.441+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ΢��֧��������ˢ��
2025-08-01T05:19:31.441+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : ϵͳ���ü�����
2025-08-01T05:19:31.441+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.config.SystemConfigInitializer     : jycb-z ģ�����ü�����ϣ�������Դ�����ݿ� jy_system ��
2025-08-01T05:19:31.545+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ��ʼִ���豸�������
2025-08-01T05:19:31.545+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.701+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸��״̬ͳ�� - ���豸��: 127, �Ѱ�: 12, δ��: 115, ��һ��: 16
2025-08-01T05:19:31.701+08:00  WARN 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ���� 16 ̨�豸��״̬��һ��: [DEV20250728919223, DEV20250728985125, DEV20250728426128, DEV20250728033370, DEV20250728808073, DEV20250728667449, DEV20250728329772, DEV20250728974982, DEV20250728918848, DEV20250728517748, DEV20250728220366, DEV20250728211309, DEV20250728271493, DEV20250728055783, DEV20250728064937, DEV20250727609878]
2025-08-01T05:19:31.701+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : �豸��״̬��һ�������Ѽ�¼����ͨ����ʱ�����Զ��޸�
2025-08-01T05:19:31.701+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:31.776+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20007
2025-08-01T05:19:31.839+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���黺�棬�豸ID: 20007
2025-08-01T05:19:31.892+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸��״̬���棬�豸ID: 20007
2025-08-01T05:19:32.052+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : �豸���������ɣ��豸ID: 20007
2025-08-01T05:19:32.052+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.config.DeviceCacheInitializer  : ����ʱ�޸����豸 20007 �Ļ��治һ��
2025-08-01T05:19:32.052+08:00 DEBUG 44824 --- [jycb-z] [restartedMain] c.j.j.config.DataPermissionInterceptor   : δ��������Ȩ�������ģ���������Ȩ�޿���
2025-08-01T05:19:32.134+08:00  INFO 44824 --- [jycb-z] [restartedMain] c.j.j.m.d.s.impl.DeviceCacheServiceImpl  : ����豸���棬�豸ID: 20008
