server:
  port: 8090
  servlet:
    context-path: /
  tomcat:
    max-http-form-post-size: 100MB
    connection-timeout: 30000
    max-connections: 10000
    accept-count: 1000
    threads:
      max: 800
      min-spare: 100

spring:
  application:
    name: jycb-z
  profiles:
    active: dev
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************
    username: jycb
    password: QGKnbHE6MCW4z2ca
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
  data:
    redis:
      host: **************
      port: 6379
      database: 0
      password: redis_ZWE7i5
      timeout: 10000
      connect-timeout: 2000
      client-name: jycb-redis-client
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0
      # 设置为false允许系统在Redis连接失败时继续启动
      client-type: lettuce
      # 是否启用Redis
      enabled: true
  # 允许系统在Redis连接失败时继续启动
  redis:
    repositories:
      enabled: false
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
    default-property-inclusion: non_null
    parser:
      allow-single-quotes: true
      allow-unquoted-field-names: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
    static-path-pattern: /**
  web:
    resources:
      add-mappings: true
      static-locations:
        - classpath:/META-INF/resources/
        - classpath:/resources/
        - classpath:/static/
        - classpath:/public/
        - file:${qrcode.storage.path}

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    auto-mapping-behavior: full
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: 
    - classpath*:/mapper/**/*.xml
    - classpath*:/mapper/admin/*.xml
  type-aliases-package: com.jycb.jycbz.modules.*.entity
  cache-enabled: false

# Sa-Token配置
sa-token:
  token-name: Authorization
  timeout: 2592000
  active-timeout: -1
  is-concurrent: true
  is-share: false
  token-style: uuid
  is-log: false
  # 设为false，使用内存模式不使用Redis
  alone-redis: false
  is-read-cookie: false

# Knife4j配置
knife4j:
  enable: true
  setting:
    language: zh_cn
    enable-swagger-models: true
    enable-document-manage: true
    swagger-model-name: 数据模型
    enable-version: true
    enable-after-script: true
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
  production: false
  # 开启Knife4j增强功能
  basic:
    enable: true
    username: wujinb12
    password: 132wuji

# SpringDoc配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    disable-swagger-default-url: true
    display-request-duration: true
    syntax-highlight:
      activated: true
    try-it-out-enabled: true
    filter: true
    doc-expansion: none
  api-docs:
    path: /v3/api-docs
    enabled: true
    groups:
      enabled: true
  default-flat-param-object: true
  packages-to-scan: com.jycb.jycbz
  cache:
    disabled: true
  writer-with-default-pretty-printer: true
  model-converters:
    pageable-converter:
      enabled: true

# 微信公众号配置
weixin:
  appid: wxe53333ac06e97efd
  secret: 8581ecae420f323bcca4558216683938

# 微信小程序配置
wx:
  miniapp:
    appid: wxe53333ac06e97efd
    secret: 8581ecae420f323bcca4558216683938
    token: 
    aesKey: 
    msgDataFormat: JSON

# okhttp配置
okhttp:
  connect-timeout: 10
  read-timeout: 10
  write-timeout: 10
  max-idle-connections: 5
  keep-alive-duration: 5

# 二维码配置
qrcode:
  # 二维码存储路径
  storage:
    path: ./qrcode
  # 二维码访问URL前缀
  access:
    url-prefix: ${server.servlet.context-path}/qrcode

logging:
  level:
    root: info
    com.jycb.jycbz: debug
  file:
    name: ./logs/jycb-z.log

# 应用自定义配置
jycb:
  # 分成配置缓存
  commission:
    cache:
      refresh:
        enabled: true  # 启用定时缓存刷新
        system-interval: 3600000  # 系统配置刷新间隔（1小时）
        health-check-interval: 1800000  # 健康检查间隔（30分钟）

  # 设备缓存配置
  device:
    cache:
      consistency:
        enabled: true  # 启用设备缓存一致性检查
      startup-check:
        enabled: true  # 启用启动时缓存检查