<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jycb.jycbz.modules.system.mapper.MenuMapper">

    <!-- 根据管理员ID查询菜单列表 -->
    <select id="selectMenusByAdminId" resultType="SystemMenu">
        SELECT DISTINCT
            m.id, m.parent_id, m.name, m.title, m.type, m.icon,
            m.path, m.component, m.redirect, m.permission, m.sort,
            m.visible, m.status, m.create_time, m.update_time
        FROM
            jy_admin_role ar
        JOIN
            jy_role r ON ar.role_id = r.id
        JOIN
            jy_role_menu rm ON r.id = rm.role_id
        JOIN
            jy_menu m ON rm.menu_id = m.id
        WHERE
            ar.admin_id = #{adminId}
            AND r.status = 1
            AND m.status = 1
        ORDER BY
            m.sort ASC, m.create_time ASC
    </select>

    <!-- 根据角色ID查询菜单列表 -->
    <select id="selectMenusByRoleId" resultType="SystemMenu">
        SELECT DISTINCT
            m.id, m.parent_id, m.name, m.title, m.type, m.icon,
            m.path, m.component, m.redirect, m.permission, m.sort,
            m.visible, m.status, m.create_time, m.update_time
        FROM
            jy_role_menu rm
        JOIN
            jy_menu m ON rm.menu_id = m.id
        JOIN
            jy_role r ON rm.role_id = r.id
        WHERE
            rm.role_id = #{roleId}
            AND r.status = 1
            AND m.status = 1
        ORDER BY
            m.sort ASC, m.create_time ASC
    </select>

    <!-- 根据角色ID列表查询菜单列表 -->
    <select id="selectMenusByRoleIds" resultType="SystemMenu">
        SELECT DISTINCT
            m.id, m.parent_id, m.name, m.title, m.type, m.icon,
            m.path, m.component, m.redirect, m.permission, m.sort,
            m.visible, m.status, m.create_time, m.update_time
        FROM
            jy_role_menu rm
        JOIN
            jy_menu m ON rm.menu_id = m.id
        JOIN
            jy_role r ON rm.role_id = r.id
        WHERE
            rm.role_id IN
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
            AND r.status = 1
            AND m.status = 1
        ORDER BY
            m.sort ASC, m.create_time ASC
    </select>

    <!-- 查询所有菜单 -->
    <select id="selectAllMenus" resultType="SystemMenu">
        SELECT
            id, parent_id, name, title, type, icon,
            path, component, redirect, permission, sort,
            visible, status, create_time, update_time
        FROM
            jy_menu
        WHERE
            status = 1
        ORDER BY
            sort ASC, create_time ASC
    </select>

    <!-- 根据父菜单ID查询子菜单数量 -->
    <select id="countChildMenus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM jy_menu
        WHERE parent_id = #{parentId}
        AND status = 1
    </select>

    <!-- 检查菜单名称是否存在（同一父级下） -->
    <select id="checkMenuNameExists" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM jy_menu
        WHERE name = #{menuName}
        AND parent_id = #{parentId}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 根据菜单类型查询菜单列表 -->
    <select id="selectMenusByType" resultType="SystemMenu">
        SELECT
            id, parent_id, name, title, type, icon,
            path, component, redirect, permission, sort,
            visible, status, create_time, update_time
        FROM
            jy_menu
        WHERE
            type = #{menuType}
            AND status = 1
        ORDER BY
            sort ASC, create_time ASC
    </select>

    <!-- 根据权限标识查询菜单 -->
    <select id="selectMenusByPermission" resultType="SystemMenu">
        SELECT
            id, parent_id, name, title, type, icon,
            path, component, redirect, permission, sort,
            visible, status, create_time, update_time
        FROM
            jy_menu
        WHERE
            permission = #{permission}
            AND status = 1
        ORDER BY
            sort ASC, create_time ASC
    </select>

</mapper>
