package com.jycb.jycbz.common.api;

import com.jycb.jycbz.common.enums.ResultCode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 通用接口响应结果
 */
@Data
@Schema(description = "通用接口响应结果")
public class CommonResult<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    @Schema(description = "状态码")
    private int code;

    /**
     * 响应消息
     */
    @Schema(description = "响应消息")
    private String message;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据", anyOf = {Object.class})
    private T data;

    /**
     * 是否成功
     */
    @Schema(description = "是否成功")
    private boolean success;

    /**
     * 构造方法
     */
    private CommonResult() {
    }

    /**
     * 构造方法
     *
     * @param code    状态码
     * @param message 响应消息
     * @param data    响应数据
     */
    private CommonResult(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = code == ResultCode.SUCCESS.getCode();
    }

    /**
     * 成功返回结果（无数据）
     *
     * @param <T> 泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> success() {
        return new CommonResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null);
    }

    /**
     * 成功返回结果
     *
     * @param data 响应数据
     * @param <T>  泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> success(T data) {
        return new CommonResult<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }

    /**
     * 成功返回结果
     *
     * @param message 提示信息
     * @param data    响应数据
     * @param <T>     泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> success(String message, T data) {
        return new CommonResult<>(ResultCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败返回结果
     *
     * @param resultCode 错误码
     * @param <T>        泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> failed(IErrorCode resultCode) {
        return new CommonResult<>(resultCode.getCode(), resultCode.getMessage(), null);
    }

    /**
     * 失败返回结果
     *
     * @param resultCode 错误码
     * @param message    错误信息
     * @param <T>        泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> failed(IErrorCode resultCode, String message) {
        return new CommonResult<>(resultCode.getCode(), message, null);
    }

    /**
     * 失败返回结果
     *
     * @param message 提示信息
     * @param <T>     泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<>(ResultCode.FAILED.getCode(), message, null);
    }

    /**
     * 失败返回结果
     *
     * @param resultCode 结果码
     * @param <T>        泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> failed(IResultCode resultCode) {
        return new CommonResult<>(resultCode.getCode(), resultCode.getMessage(), null);
    }

    /**
     * 失败返回结果
     *
     * @param <T> 泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> failed() {
        return failed(ResultCode.FAILED);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param <T> 泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> validateFailed() {
        return failed(ResultCode.VALIDATE_FAILED);
    }

    /**
     * 参数验证失败返回结果
     *
     * @param message 提示信息
     * @param <T>     泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> validateFailed(String message) {
        return new CommonResult<>(ResultCode.VALIDATE_FAILED.getCode(), message, null);
    }

    /**
     * 未登录返回结果
     *
     * @param <T> 泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> unauthorized() {
        return failed(ResultCode.UNAUTHORIZED);
    }

    /**
     * 未授权返回结果
     *
     * @param <T> 泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> forbidden() {
        return failed(ResultCode.FORBIDDEN);
    }
    
    /**
     * 错误返回结果
     *
     * @param code    错误码
     * @param message 错误信息
     * @param <T>     泛型参数
     * @return 响应结果
     */
    public static <T> CommonResult<T> error(int code, String message) {
        return new CommonResult<>(code, message, null);
    }
} 