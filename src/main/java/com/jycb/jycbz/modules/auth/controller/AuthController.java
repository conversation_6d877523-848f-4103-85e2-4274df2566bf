package com.jycb.jycbz.modules.auth.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.jycb.jycbz.common.annotation.Anonymous;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.modules.auth.dto.AdminLoginDTO;
import com.jycb.jycbz.modules.auth.dto.WechatLoginDTO;
import com.jycb.jycbz.modules.auth.service.AuthService;
import com.jycb.jycbz.modules.auth.vo.AdminInfoVO;
import com.jycb.jycbz.modules.auth.vo.LoginResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "认证相关接口")
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;

    /**
     * 管理员登录
     *
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    @PostMapping("/admin/login")
    @Operation(summary = "管理员登录", description = "管理员登录接口")
    @Anonymous
    @Auditable(module = AuditConstants.Module.ADMIN, operation = AuditConstants.Operation.LOGIN, description = "管理员登录")
    public CommonResult<LoginResultVO> adminLogin(@RequestBody AdminLoginDTO loginDTO) {
        LoginResultVO result = authService.adminLogin(loginDTO);
        return CommonResult.success(result);
    }

    /**
     * 微信小程序登录
     *
     * @param wechatLoginDTO 微信登录参数
     * @return 登录结果
     */
    @PostMapping("/wechat/login")
    @Operation(summary = "微信小程序登录", description = "微信小程序登录接口")
    @Anonymous
    public CommonResult<LoginResultVO> wechatLogin(@RequestBody WechatLoginDTO wechatLoginDTO) {
        LoginResultVO result = authService.wechatLogin(wechatLoginDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取管理员信息
     *
     * @return 管理员信息
     */
    @GetMapping("/admin/info")
    @Operation(summary = "获取管理员信息", description = "获取当前登录管理员信息")
    public CommonResult<AdminInfoVO> getAdminInfo() {
        // 获取当前登录ID
        String loginId = StpUtil.getLoginIdAsString();
        AdminInfoVO adminInfo = authService.getAdminInfo(Long.valueOf(loginId));
        return CommonResult.success(adminInfo);
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/profile")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户信息")
    public CommonResult<LoginResultVO> getProfile() {
        // 获取当前登录ID
        String loginId = StpUtil.getLoginIdAsString();
        LoginResultVO profile = authService.getProfile(loginId);
        return CommonResult.success(profile);
    }

    /**
     * 获取权限列表
     *
     * @return 权限列表
     */
    @GetMapping("/permissions")
    @Operation(summary = "获取权限列表", description = "获取当前登录用户的权限列表")
    public CommonResult<String[]> getPermissions() {
        // 获取当前登录ID
        String loginId = StpUtil.getLoginIdAsString();
        String[] permissions = authService.getPermissions(Long.valueOf(loginId));
        return CommonResult.success(permissions);
    }

    /**
     * 获取菜单列表
     *
     * @return 菜单列表
     */
    @GetMapping("/menus")
    @Operation(summary = "获取菜单列表", description = "获取当前登录用户的菜单列表")
    public CommonResult<Object> getMenus() {
        // 获取当前登录ID
        String loginId = StpUtil.getLoginIdAsString();
        Object menus = authService.getMenus(Long.valueOf(loginId));
        return CommonResult.success(menus);
    }

    /**
     * 验证密码
     *
     * @param password 密码
     * @return 验证结果
     */
    @PostMapping("/verify-password")
    @Operation(summary = "验证密码", description = "验证当前登录用户的密码")
    public CommonResult<Boolean> verifyPassword(@RequestParam String password) {
        // 获取当前登录ID
        String loginId = StpUtil.getLoginIdAsString();
        boolean result = authService.verifyPassword(Long.valueOf(loginId), password);
        return CommonResult.success(result);
    }

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 修改结果
     */
    @PostMapping("/change-password")
    @Operation(summary = "修改密码", description = "修改当前登录用户的密码")
    public CommonResult<Boolean> changePassword(@RequestParam String oldPassword, @RequestParam String newPassword) {
        // 获取当前登录ID
        String loginId = StpUtil.getLoginIdAsString();
        boolean result = authService.changePassword(Long.valueOf(loginId), oldPassword, newPassword);
        return CommonResult.success(result);
    }

    /**
     * 登出
     *
     * @return 登出结果
     */
    @PostMapping("/logout")
    @Operation(summary = "登出", description = "登出当前用户")
    public CommonResult<Void> logout() {
        StpUtil.logout();
        return CommonResult.success(null);
    }

    /**
     * 获取登录状态
     *
     * @return 登录状态
     */
    @GetMapping("/is-login")
    @Operation(summary = "获取登录状态", description = "获取当前用户的登录状态")
    @SaIgnore
    public CommonResult<Map<String, Object>> isLogin() {
        Map<String, Object> result = new HashMap<>();
        result.put("isLogin", StpUtil.isLogin());
        if (StpUtil.isLogin()) {
            result.put("loginId", StpUtil.getLoginIdAsString());
            result.put("tokenName", StpUtil.getTokenName());
            result.put("tokenValue", StpUtil.getTokenValue());
        }
        return CommonResult.success(result);
    }


}