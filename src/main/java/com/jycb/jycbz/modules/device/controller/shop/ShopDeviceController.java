package com.jycb.jycbz.modules.device.controller.shop;

import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.annotation.DeviceOwnershipRequired;
import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.common.controller.BaseDeviceController;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.service.DeviceFaultReportService;
import com.jycb.jycbz.modules.device.service.DeviceMaintenanceService;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.device.vo.DeviceVO;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 门店端-设备控制器
 * 注意：门店只有查看设备的权限，没有绑定设备的权限
 */
@Slf4j
@RestController
@RequestMapping("/api/shop/devices")
@Tag(name = "门店端-设备API")
@SaCheckRole(value = {"shop_admin"})
public class ShopDeviceController extends BaseDeviceController {

    @Autowired
    private DeviceFaultReportService deviceFaultReportService;

    @Autowired
    private DeviceMaintenanceService deviceMaintenanceService;

    @Autowired
    private ShopService shopService;

    // Inherit common methods from BaseDeviceController, shops can only view their own devices
    // Override getDeviceList to ensure shops only query their own bound devices

    @Override
    public Result<PageResult<DeviceVO>> getDeviceList(
            @Parameter(description = "Page number") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "Device number") @RequestParam(required = false) String deviceNo,
            @Parameter(description = "Device status") @RequestParam(required = false) Integer status,
            @Parameter(description = "Partner ID") @RequestParam(required = false) Integer partnerId,
            @Parameter(description = "Shop ID") @RequestParam(required = false) Integer shopId,
            @Parameter(description = "Is bound") @RequestParam(required = false) Integer isBound
    ) {
        Integer currentShopId = getCurrentShopId();

        // Force shopId to current shop and isBound to 1 (only bound devices)
        PageResult<DeviceVO> result = deviceService.getDeviceList(
                page, size, deviceNo, status,
                null, // partnerId - shops cannot specify partner
                currentShopId, // Force current shop ID
                1 // Force only bound devices
        );

        return Result.success(result);
    }

    @Override
    protected void validateListPermission(Integer partnerId, Integer shopId) {
        // This method is called by BaseDeviceController but we override getDeviceList
        // so this validation is mainly for other inherited methods
        Integer currentShopId = getCurrentShopId();
        if (shopId != null && !shopId.equals(currentShopId)) {
            throw new BusinessException("No permission to view other shops' devices");
        }
        // Shops cannot specify partner ID for queries
        if (partnerId != null) {
            throw new BusinessException("Shops cannot specify partner for queries");
        }
    }

    @Override
    protected void validateDevicePermission(DeviceVO device) {
        Integer shopId = getCurrentShopId();
        if (device == null || !device.getShopId().equals(shopId)) {
            throw new BusinessException("Device does not exist or does not belong to current shop");
        }
    }

    @Override
    protected Map<String, Object> getFilteredStatistics() {
        Integer shopId = getCurrentShopId();
        return deviceService.getShopDeviceStatistics(shopId);
    }

    @Override
    protected Integer getCurrentShopId() {
        // Get shop admin ID from Token, then find associated shop
        String loginId = StpUtil.getLoginIdAsString();
        Long adminId = Long.valueOf(loginId);
        // Find associated shop ID by admin ID
        Shop shop = shopService.getShopByAdminId(adminId);
        if (shop == null) {
            throw new BusinessException("Current user is not associated with any shop");
        }
        System.out.printf("Current user id: %s\n", loginId);
        return shop.getId().intValue();
    }

    /**
     * 获取设备详情
     */
    @GetMapping("/{id}")
    @Operation(
        summary = "获取设备详情",
        description = "门店只能查看属于本门店的设备详情"
    )
    @DeviceOwnershipRequired(deviceIdParam = "id", message = "设备不存在或不属于当前门店")
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询设备详情",
            targetId = "#id"
    )
    public Result<DeviceVO> getDeviceDetail(@PathVariable Integer id) {
        Integer shopId = getCurrentShopId();
        DeviceVO device = deviceService.getDeviceDetail(id);
        
        // 验证设备是否属于当前门店
        if (device == null || !device.getShopId().equals(shopId)) {
            return Result.failed("设备不存在或不属于当前门店");
        }
        
        return Result.success(device);
    }

    /**
     * 根据MAC地址查询设备
     */
    @GetMapping("/mac/{macAddress}")
    @Operation(
        summary = "根据MAC地址查询设备", 
        description = "门店只能查询属于本门店的设备"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "根据MAC地址查询设备"
    )
    public Result<DeviceVO> getDeviceByMacAddress(@PathVariable String macAddress) {
        Integer shopId = getCurrentShopId();
        Device device = deviceService.getDeviceByMacAddress(macAddress);
        
        if (device == null) {
            return Result.failed("设备不存在");
        }
        DeviceVO deviceVO = deviceService.getDeviceDetail(device.getId());
        
        // 验证设备是否属于当前门店
        if (!deviceVO.getShopId().equals(shopId)) {
            return Result.failed("设备不属于当前门店");
        }
        
        return Result.success(deviceVO);
    }

    /**
     * 更新设备状态
     */
    @PutMapping("/{id}/status")
    @Operation(
        summary = "更新设备状态", 
        description = "门店只能更新属于本门店的设备状态，用于报告设备故障或维护需求"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "更新设备状态",
            targetId = "#id"
    )
    public Result<Void> updateDeviceStatus(
            @PathVariable Integer id,
            @Parameter(description = "设备状态：1-正常 2-维护中 3-故障") @RequestParam Integer status
    ) {
        Integer shopId = getCurrentShopId();
        DeviceVO device = deviceService.getDeviceDetail(id);
        
        // 验证设备是否属于当前门店
        if (device == null || !device.getShopId().equals(shopId)) {
            return Result.failed("设备不存在或不属于当前门店");
        }
        
        deviceService.updateDeviceStatus(id, status);
        return Result.success();
    }

    /**
     * 获取门店设备统计信息
     * 包含设备总数、在线设备数、维护中设备数、故障设备数等统计数据
     */
    @GetMapping("/statistics")
    @Operation(
        summary = "获取门店设备统计信息", 
        description = "获取当前门店的设备统计数据，包括设备总数、在线设备数、维护中设备数、故障设备数等"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "查询门店设备统计信息"
    )
    public Result<Map<String, Object>> getDeviceStatistics() {
        Integer shopId = getCurrentShopId();
        return Result.success(deviceService.getDeviceStatistics(null, null, shopId));
    }

    /**
     * 扫描设备二维码
     */
    @GetMapping("/scan/{macAddress}")
    @Operation(
        summary = "扫描设备二维码", 
        description = "门店扫描设备二维码，验证设备是否属于本门店"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.READ,
            description = "扫描设备二维码"
    )
    public Result<DeviceVO> scanDeviceQrcode(@PathVariable String macAddress) {
        Integer shopId = getCurrentShopId();
        Device device = deviceService.getDeviceByMacAddress(macAddress);
        
        if (device == null) {
            return Result.failed("设备不存在");
        }
        
        DeviceVO deviceVO = deviceService.getDeviceDetail(device.getId());
        
        // 检查设备是否已绑定
        if (deviceVO.getIsBound() == 1) {
            // 检查设备是否属于当前门店
            if (!deviceVO.getShopId().equals(shopId)) {
                return Result.failed("设备已绑定到其他门店");
            }
        }
        
        return Result.success(deviceVO);
    }

    /**
     * 报告设备故障
     */
    @PostMapping("/report-issue")
    @Operation(
        summary = "报告设备故障", 
        description = "门店报告设备故障，提供故障类型和描述"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "报告设备故障"
    )
    public Result<Void> reportDeviceIssue(@RequestBody Map<String, String> issueDetails) {
        Integer shopId = getCurrentShopId();
        
        // 验证设备是否属于当前门店
        Integer deviceId = Integer.valueOf(issueDetails.get("deviceId"));
        DeviceVO device = deviceService.getDeviceDetail(deviceId);
        if (device == null || !device.getShopId().equals(shopId)) {
            return Result.failed("设备不存在或不属于当前门店");
        }
        
        // 实现故障报告逻辑
        try {
            // 从请求中获取故障信息
            Integer faultType = Integer.valueOf(issueDetails.getOrDefault("faultType", "1"));
            String faultDesc = issueDetails.getOrDefault("faultDesc", "");
            String faultImages = issueDetails.getOrDefault("faultImages", "");
            String contactPhone = issueDetails.getOrDefault("contactPhone", "");
            
            // 获取当前登录用户信息
            Integer reporterId = StpUtil.getLoginIdAsInt();
            String reporterName = (String) StpUtil.getSession().get("userName");
            if (reporterName == null) {
                reporterName = "门店管理员";
            }
            
            // 创建故障报告
            Integer reportId = deviceFaultReportService.createFaultReport(
                deviceId,
                device.getDeviceNo(),
                device.getEntityId(),
                device.getPartnerId(),
                shopId,
                faultType,
                faultDesc,
                faultImages,
                reporterId,
                reporterName,
                contactPhone
            );
            
            // 如果设备状态为正常，则更新为故障状态
            if (device.getStatus() == 1) {
                deviceService.updateDeviceStatus(deviceId, 3); // 3-故障
            }
            
            return Result.success();
        } catch (Exception e) {
            return Result.failed("报告设备故障失败：" + e.getMessage());
        }
    }

    /**
     * 申请设备维护
     */
    @PostMapping("/request-maintenance")
    @Operation(
        summary = "申请设备维护", 
        description = "门店申请设备维护，提供维护原因和描述"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "申请设备维护"
    )
    public Result<Void> requestDeviceMaintenance(@RequestBody Map<String, String> maintenanceDetails) {
        Integer shopId = getCurrentShopId();
        
        // 验证设备是否属于当前门店
        Integer deviceId = Integer.valueOf(maintenanceDetails.get("deviceId"));
        DeviceVO device = deviceService.getDeviceDetail(deviceId);
        if (device == null || !device.getShopId().equals(shopId)) {
            return Result.failed("设备不存在或不属于当前门店");
        }
        
        // 实现维护申请逻辑
        try {
            // 从请求中获取维护信息
            Integer maintenanceType = Integer.valueOf(maintenanceDetails.getOrDefault("maintenanceType", "1"));
            String maintenanceReason = maintenanceDetails.getOrDefault("maintenanceReason", "");
            String contactPhone = maintenanceDetails.getOrDefault("contactPhone", "");
            
            // 处理期望维护时间
            LocalDateTime expectedTime = LocalDateTime.now().plusDays(1); // 默认为明天
            String expectedTimeStr = maintenanceDetails.get("expectedTime");
            if (expectedTimeStr != null && !expectedTimeStr.isEmpty()) {
                try {
                    // 尝试解析日期时间字符串，格式为yyyy-MM-dd HH:mm:ss
                    expectedTime = LocalDateTime.parse(expectedTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } catch (Exception e) {
                    log.warn("解析期望维护时间失败: {}", expectedTimeStr);
                }
            }
            
            // 获取当前登录用户信息
            Integer applicantId = StpUtil.getLoginIdAsInt();
            String applicantName = (String) StpUtil.getSession().get("userName");
            if (applicantName == null) {
                applicantName = "门店管理员";
            }
            
            // 创建维护申请
            Integer maintenanceId = deviceMaintenanceService.createMaintenance(
                deviceId,
                device.getDeviceNo(),
                device.getEntityId(),
                device.getPartnerId(),
                shopId,
                maintenanceType,
                maintenanceReason,
                expectedTime,
                applicantId,
                applicantName,
                contactPhone
            );
            
            // 如果设备状态为正常，则更新为维护中状态
            if (device.getStatus() == 1) {
                deviceService.updateDeviceStatus(deviceId, 2); // 2-维护中
            }
            
            return Result.success();
        } catch (Exception e) {
            return Result.failed("申请设备维护失败：" + e.getMessage());
        }
    }

    /**
     * 申请设备清洁
     */
    @PostMapping("/request-cleaning")
    @Operation(
        summary = "申请设备清洁", 
        description = "门店申请设备清洁，提供清洁原因和描述"
    )
    @Auditable(
            module = AuditConstants.Module.DEVICE,
            operation = AuditConstants.Operation.UPDATE,
            description = "申请设备清洁"
    )
    public Result<Void> requestDeviceCleaning(@RequestBody Map<String, String> cleaningDetails) {
        Integer shopId = getCurrentShopId();
        
        // 验证设备是否属于当前门店
        Integer deviceId = Integer.valueOf(cleaningDetails.get("deviceId"));
        DeviceVO device = deviceService.getDeviceDetail(deviceId);
        if (device == null || !device.getShopId().equals(shopId)) {
            return Result.failed("设备不存在或不属于当前门店");
        }
        
        // 调用设备服务申请清洁
        boolean success = deviceService.requestDeviceCleaning(shopId, cleaningDetails);
        
        if (success) {
            return Result.success();
        } else {
            return Result.failed("申请清洁失败");
        }
    }

    // 重复的private方法已删除，使用@Override的protected方法
}