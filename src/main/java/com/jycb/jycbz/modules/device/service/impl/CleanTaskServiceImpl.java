package com.jycb.jycbz.modules.device.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.api.PageResult;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.entity.CleanTask;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.mapper.DeviceCleanTaskMapper;
import com.jycb.jycbz.modules.device.service.CleanTaskService;
import com.jycb.jycbz.modules.device.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 清洁任务服务实现类
 */
@Slf4j
@Service
public class CleanTaskServiceImpl extends ServiceImpl<DeviceCleanTaskMapper, CleanTask> implements CleanTaskService {

    @Autowired
    @Lazy
    private DeviceService deviceService;

    @Override
    public IPage<CleanTask> selectCleanTaskPage(Page<CleanTask> page,
                                              Integer entityId,
                                              Integer partnerId,
                                              Integer shopId,
                                              Integer deviceId,
                                              Integer status,
                                              Integer taskType) {
        return baseMapper.selectCleanTaskPage(page, entityId, partnerId, shopId, deviceId, status, taskType);
    }

    @Override
    public List<CleanTask> getCleanTaskList(Integer deviceId, Integer status, Integer taskType) {
        return baseMapper.selectCleanTaskList(deviceId, status, taskType);
    }

    @Override
    public CleanTask getCleanTaskDetail(Integer id) {
        return getById(id);
    }

    @Override
    public PageResult<CleanTask> getCleanTaskPage(Integer entityId,
                                                Integer partnerId,
                                                Integer shopId,
                                                Integer deviceId,
                                                Integer status,
                                                Integer taskType,
                                                Integer page,
                                                Integer size) {
        Page<CleanTask> pageParam = new Page<>(page, size);
        IPage<CleanTask> pageResult = selectCleanTaskPage(pageParam, entityId, partnerId, shopId, deviceId, status, taskType);
        
        return PageResult.build(pageResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCleanTask(CleanTask cleanTask) {
        cleanTask.setCreateTime(LocalDateTime.now());
        cleanTask.setUpdateTime(LocalDateTime.now());
        cleanTask.setTaskStatus(0); // 默认待处理状态
        cleanTask.setApplyTime(LocalDateTime.now());
        
        return save(cleanTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCleanTaskStatus(Integer id, Integer status, String remark) {
        CleanTask cleanTask = getById(id);
        if (cleanTask == null) {
            throw new BusinessException("清洁任务不存在");
        }
        
        cleanTask.setTaskStatus(status);
        cleanTask.setRemark(remark);
        cleanTask.setUpdateTime(LocalDateTime.now());
        
        return updateById(cleanTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignCleanTask(Integer id, Integer cleanerId, String cleanerName, String planTime) {
        CleanTask cleanTask = getById(id);
        if (cleanTask == null) {
            throw new BusinessException("清洁任务不存在");
        }
        
        cleanTask.setCleanerId(cleanerId);
        cleanTask.setCleanerName(cleanerName);
        cleanTask.setPlanTime(LocalDateTime.parse(planTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        cleanTask.setTaskStatus(1); // 已安排状态
        cleanTask.setUpdateTime(LocalDateTime.now());
        
        return updateById(cleanTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeCleanTask(Integer id, String imagesAfter, String remark) {
        CleanTask cleanTask = getById(id);
        if (cleanTask == null) {
            throw new BusinessException("清洁任务不存在");
        }
        
        cleanTask.setImagesAfter(imagesAfter);
        cleanTask.setRemark(remark);
        cleanTask.setTaskStatus(2); // 已完成状态
        cleanTask.setEndTime(LocalDateTime.now());
        
        // 计算清洁时长
        if (cleanTask.getStartTime() != null) {
            long minutes = java.time.Duration.between(cleanTask.getStartTime(), cleanTask.getEndTime()).toMinutes();
            cleanTask.setDuration((int) minutes);
        }
        
        cleanTask.setUpdateTime(LocalDateTime.now());
        
        return updateById(cleanTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelCleanTask(Integer id, String remark) {
        CleanTask cleanTask = getById(id);
        if (cleanTask == null) {
            throw new BusinessException("清洁任务不存在");
        }
        
        cleanTask.setRemark(remark);
        cleanTask.setTaskStatus(3); // 已取消状态
        cleanTask.setUpdateTime(LocalDateTime.now());
        
        return updateById(cleanTask);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean requestDeviceCleaning(Integer shopId, Map<String, String> cleaningDetails) {
        if (cleaningDetails == null || !cleaningDetails.containsKey("deviceId")) {
            throw new BusinessException("参数错误");
        }
        
        try {
            Integer deviceId = Integer.valueOf(cleaningDetails.get("deviceId"));
            Device device = deviceService.getById(deviceId);
            
            if (device == null || !device.getShopId().equals(shopId)) {
                throw new BusinessException("设备不存在或不属于当前门店");
            }
            
            // 创建清洁任务
            CleanTask cleanTask = new CleanTask();
            cleanTask.setDeviceId(deviceId);
            cleanTask.setDeviceNo(device.getDeviceNo());
            cleanTask.setEntityId(device.getEntityId());
            cleanTask.setPartnerId(device.getPartnerId());
            cleanTask.setShopId(shopId);
            
            // 设置任务类型
            String taskTypeStr = cleaningDetails.getOrDefault("taskType", "1");
            cleanTask.setTaskType(Integer.valueOf(taskTypeStr));
            
            // 设置清洁原因
            cleanTask.setCleanReason(cleaningDetails.getOrDefault("cleanReason", ""));
            
            // 设置申请人信息
            cleanTask.setApplicantId(Integer.valueOf(cleaningDetails.getOrDefault("applicantId", "0")));
            cleanTask.setApplicantName(cleaningDetails.getOrDefault("applicantName", ""));
            cleanTask.setContactPhone(cleaningDetails.getOrDefault("contactPhone", ""));
            
            // 设置图片
            cleanTask.setImagesBefore(cleaningDetails.getOrDefault("imagesBefore", ""));
            
            // 设置备注
            cleanTask.setRemark(cleaningDetails.getOrDefault("remark", ""));
            
            // 创建清洁任务
            return createCleanTask(cleanTask);
        } catch (NumberFormatException e) {
            log.error("请求设备清洁参数错误", e);
            throw new BusinessException("参数格式错误");
        }
    }
} 