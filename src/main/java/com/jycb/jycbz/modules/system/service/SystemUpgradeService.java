package com.jycb.jycbz.modules.system.service;

import com.jycb.jycbz.modules.system.dto.UpgradeConfigDTO;
import com.jycb.jycbz.common.api.PageResult;

import java.util.List;
import java.util.Map;

/**
 * 系统升级服务接口
 */
public interface SystemUpgradeService {

    // ==================== 升级配置管理 ====================

    /**
     * 获取升级配置列表
     */
    PageResult<UpgradeConfigDTO> getUpgradeConfigs(int pageNum, int pageSize, UpgradeConfigDTO queryDTO);

    /**
     * 获取升级配置详情
     */
    UpgradeConfigDTO getUpgradeConfigById(Long configId);

    /**
     * 创建升级配置
     */
    boolean createUpgradeConfig(UpgradeConfigDTO configDTO);

    /**
     * 更新升级配置
     */
    boolean updateUpgradeConfig(UpgradeConfigDTO configDTO);

    /**
     * 删除升级配置
     */
    boolean deleteUpgradeConfig(Long configId);

    /**
     * 批量删除升级配置
     */
    boolean batchDeleteUpgradeConfigs(List<Long> configIds);

    /**
     * 启用/禁用升级配置
     */
    boolean toggleUpgradeConfig(Long configId, Boolean enabled);

    /**
     * 发布升级配置
     */
    boolean releaseUpgradeConfig(Long configId);

    /**
     * 撤回升级配置
     */
    boolean withdrawUpgradeConfig(Long configId);

    // ==================== 版本管理 ====================

    /**
     * 获取当前系统版本
     */
    String getCurrentSystemVersion();

    /**
     * 获取版本历史
     */
    List<Map<String, Object>> getVersionHistory();

    /**
     * 检查版本兼容性
     */
    Map<String, Object> checkVersionCompatibility(String targetVersion);

    /**
     * 获取可用升级版本
     */
    List<UpgradeConfigDTO> getAvailableUpgradeVersions();

    /**
     * 获取最新版本信息
     */
    UpgradeConfigDTO getLatestVersion();

    /**
     * 比较版本差异
     */
    Map<String, Object> compareVersions(String fromVersion, String toVersion);

    // ==================== 升级检查 ====================

    /**
     * 检查系统升级
     */
    Map<String, Object> checkSystemUpgrade();

    /**
     * 检查升级前置条件
     */
    Map<String, Object> checkUpgradePrerequisites(Long configId);

    /**
     * 验证升级包
     */
    Map<String, Object> validateUpgradePackage(Long configId);

    /**
     * 检查磁盘空间
     */
    Map<String, Object> checkDiskSpace(Long configId);

    /**
     * 检查网络连接
     */
    Map<String, Object> checkNetworkConnection();

    /**
     * 检查系统权限
     */
    Map<String, Object> checkSystemPermissions();

    // ==================== 升级执行 ====================

    /**
     * 开始系统升级
     */
    Map<String, Object> startSystemUpgrade(Long configId);

    /**
     * 暂停系统升级
     */
    boolean pauseSystemUpgrade(String upgradeId);

    /**
     * 恢复系统升级
     */
    boolean resumeSystemUpgrade(String upgradeId);

    /**
     * 取消系统升级
     */
    boolean cancelSystemUpgrade(String upgradeId);

    /**
     * 获取升级进度
     */
    Map<String, Object> getUpgradeProgress(String upgradeId);

    /**
     * 获取升级状态
     */
    Map<String, Object> getUpgradeStatus(String upgradeId);

    // ==================== 升级回滚 ====================

    /**
     * 回滚系统升级
     */
    Map<String, Object> rollbackSystemUpgrade(String upgradeId);

    /**
     * 检查回滚条件
     */
    Map<String, Object> checkRollbackConditions(String upgradeId);

    /**
     * 获取回滚进度
     */
    Map<String, Object> getRollbackProgress(String rollbackId);

    /**
     * 获取可回滚版本
     */
    List<Map<String, Object>> getRollbackableVersions();

    // ==================== 升级历史 ====================

    /**
     * 获取升级历史记录
     */
    PageResult<Object> getUpgradeHistory(int pageNum, int pageSize, Map<String, Object> queryParams);

    /**
     * 获取升级详细日志
     */
    List<String> getUpgradeDetailLogs(String upgradeId);

    /**
     * 获取升级统计信息
     */
    Map<String, Object> getUpgradeStatistics();

    /**
     * 清理升级历史
     */
    boolean cleanUpgradeHistory(int retentionDays);

    // ==================== 升级通知 ====================

    /**
     * 发送升级通知
     */
    boolean sendUpgradeNotification(Long configId, String notificationType);

    /**
     * 获取升级通知配置
     */
    Map<String, Object> getUpgradeNotificationConfig();

    /**
     * 更新升级通知配置
     */
    boolean updateUpgradeNotificationConfig(Map<String, Object> config);

    /**
     * 订阅升级通知
     */
    boolean subscribeUpgradeNotification(String email, String notificationType);

    /**
     * 取消订阅升级通知
     */
    boolean unsubscribeUpgradeNotification(String email);

    // ==================== 升级包管理 ====================

    /**
     * 上传升级包
     */
    Map<String, Object> uploadUpgradePackage(byte[] packageData, String fileName);

    /**
     * 下载升级包
     */
    byte[] downloadUpgradePackage(Long configId);

    /**
     * 验证升级包完整性
     */
    Map<String, Object> verifyUpgradePackageIntegrity(Long configId);

    /**
     * 删除升级包
     */
    boolean deleteUpgradePackage(Long configId);

    /**
     * 获取升级包信息
     */
    Map<String, Object> getUpgradePackageInfo(Long configId);

    // ==================== 自动升级 ====================

    /**
     * 启用自动升级
     */
    boolean enableAutoUpgrade();

    /**
     * 禁用自动升级
     */
    boolean disableAutoUpgrade();

    /**
     * 获取自动升级配置
     */
    Map<String, Object> getAutoUpgradeConfig();

    /**
     * 更新自动升级配置
     */
    boolean updateAutoUpgradeConfig(Map<String, Object> config);

    /**
     * 检查自动升级条件
     */
    Map<String, Object> checkAutoUpgradeConditions();

    /**
     * 执行自动升级
     */
    Map<String, Object> executeAutoUpgrade();

    // ==================== 升级测试 ====================

    /**
     * 创建测试升级
     */
    Map<String, Object> createTestUpgrade(Long configId);

    /**
     * 执行升级测试
     */
    Map<String, Object> executeUpgradeTest(String testId);

    /**
     * 获取测试结果
     */
    Map<String, Object> getUpgradeTestResult(String testId);

    /**
     * 清理测试环境
     */
    boolean cleanUpgradeTestEnvironment(String testId);

    // ==================== 升级监控 ====================

    /**
     * 获取升级监控数据
     */
    Map<String, Object> getUpgradeMonitoringData();

    /**
     * 获取升级性能指标
     */
    Map<String, Object> getUpgradePerformanceMetrics(String upgradeId);

    /**
     * 获取升级错误信息
     */
    List<Map<String, Object>> getUpgradeErrors(String upgradeId);

    /**
     * 获取升级警告信息
     */
    List<Map<String, Object>> getUpgradeWarnings(String upgradeId);

    // ==================== 升级报告 ====================

    /**
     * 生成升级报告
     */
    String generateUpgradeReport(String upgradeId);

    /**
     * 导出升级数据
     */
    byte[] exportUpgradeData(String format, Map<String, Object> params);

    /**
     * 获取升级报告模板
     */
    String getUpgradeReportTemplate();

    /**
     * 发送升级报告
     */
    boolean sendUpgradeReport(String upgradeId, List<String> recipients);

    // ==================== 扩展方法 ====================

    /**
     * 获取当前版本
     */
    Map<String, Object> getCurrentVersion();

    /**
     * 检查系统更新
     */
    Map<String, Object> checkSystemUpdate();

    /**
     * 下载系统更新
     */
    boolean downloadSystemUpdate(String version);

    /**
     * 执行系统升级
     */
    boolean executeSystemUpgrade(com.jycb.jycbz.modules.system.dto.UpgradeConfigDTO upgradeConfigDTO);

    /**
     * 获取升级历史（简化版本）
     */
    List<Map<String, Object>> getUpgradeHistory(Integer pageNum, Integer pageSize);

    /**
     * 回滚系统
     */
    boolean rollbackSystem(String version, String reason);

    /**
     * 获取租户列表
     */
    List<Map<String, Object>> getTenantList(String keyword, Integer pageNum, Integer pageSize);

    /**
     * 创建租户
     */
    boolean createTenant(Map<String, Object> tenantInfo);

    /**
     * 更新租户
     */
    boolean updateTenant(String tenantId, Map<String, Object> tenantInfo);

    /**
     * 删除租户
     */
    boolean deleteTenant(String tenantId);

    /**
     * 获取支持的语言
     */
    List<Map<String, Object>> getSupportedLanguages();

    /**
     * 获取语言消息
     */
    Map<String, Object> getLanguageMessages(String language, String module);

    /**
     * 更新语言消息
     */
    boolean updateLanguageMessages(String language, Map<String, String> messages);

    /**
     * 添加语言支持
     */
    boolean addLanguageSupport(Map<String, Object> languageInfo);

    /**
     * 获取系统主题
     */
    List<Map<String, Object>> getSystemThemes();

    /**
     * 创建自定义主题
     */
    boolean createCustomTheme(Map<String, Object> themeInfo);

    /**
     * 设置默认主题
     */
    boolean setDefaultTheme(String themeId);

    /**
     * 获取个性化配置
     */
    Map<String, Object> getPersonalizationConfig();

    /**
     * 更新个性化配置
     */
    boolean updatePersonalizationConfig(Map<String, Object> config);

    /**
     * 启用维护模式
     */
    boolean enableMaintenanceMode(String reason, Integer duration);

    /**
     * 禁用维护模式
     */
    boolean disableMaintenanceMode();

    /**
     * 获取维护状态
     */
    Map<String, Object> getMaintenanceStatus();
}
