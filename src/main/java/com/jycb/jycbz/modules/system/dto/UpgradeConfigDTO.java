package com.jycb.jycbz.modules.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 升级配置DTO
 */
@Data
@Schema(description = "升级配置DTO")
public class UpgradeConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "配置ID")
    private Long configId;

    @Schema(description = "升级版本", required = true)
    @NotBlank(message = "升级版本不能为空")
    private String version;

    @Schema(description = "版本名称", required = true)
    @NotBlank(message = "版本名称不能为空")
    private String versionName;

    @Schema(description = "版本描述")
    private String description;

    @Schema(description = "升级类型：MAJOR-主版本 MINOR-次版本 PATCH-补丁版本 HOTFIX-热修复")
    private String upgradeType;

    @Schema(description = "升级方式：AUTO-自动升级 MANUAL-手动升级 FORCED-强制升级")
    private String upgradeMode;

    @Schema(description = "升级包URL", required = true)
    @NotBlank(message = "升级包URL不能为空")
    private String packageUrl;

    @Schema(description = "升级包大小（字节）")
    private Long packageSize;

    @Schema(description = "升级包MD5")
    private String packageMd5;

    @Schema(description = "升级包SHA256")
    private String packageSha256;

    @Schema(description = "最低兼容版本")
    private String minCompatibleVersion;

    @Schema(description = "最高兼容版本")
    private String maxCompatibleVersion;

    @Schema(description = "升级前检查项")
    private List<String> preUpgradeChecks;

    @Schema(description = "升级后验证项")
    private List<String> postUpgradeValidations;

    @Schema(description = "升级脚本")
    private String upgradeScript;

    @Schema(description = "回滚脚本")
    private String rollbackScript;

    @Schema(description = "升级状态：DRAFT-草稿 TESTING-测试中 APPROVED-已批准 RELEASED-已发布 DEPRECATED-已废弃")
    private String status;

    @Schema(description = "是否启用：1-启用 0-禁用")
    private Integer enabled;

    @Schema(description = "发布时间")
    private LocalDateTime releaseTime;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveTime;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

    @Schema(description = "升级优先级：1-低 2-中 3-高 4-紧急")
    private Integer priority;

    @Schema(description = "目标平台：ALL-全平台 ANDROID-安卓 IOS-苹果 WEB-网页 DESKTOP-桌面")
    private String targetPlatform;

    @Schema(description = "目标环境：ALL-全环境 DEV-开发 TEST-测试 STAGING-预发布 PROD-生产")
    private String targetEnvironment;

    @Schema(description = "升级通知配置")
    private Map<String, Object> notificationConfig;

    @Schema(description = "升级进度回调URL")
    private String progressCallbackUrl;

    @Schema(description = "升级完成回调URL")
    private String completionCallbackUrl;

    @Schema(description = "升级失败回调URL")
    private String failureCallbackUrl;

    @Schema(description = "自动重试次数")
    private Integer autoRetryCount;

    @Schema(description = "重试间隔（秒）")
    private Integer retryInterval;

    @Schema(description = "升级超时时间（秒）")
    private Integer upgradeTimeout;

    @Schema(description = "是否支持断点续传：1-支持 0-不支持")
    private Integer supportResume;

    @Schema(description = "是否支持增量升级：1-支持 0-不支持")
    private Integer supportIncremental;

    @Schema(description = "增量包信息")
    private Map<String, Object> incrementalPackageInfo;

    @Schema(description = "升级日志级别：DEBUG INFO WARN ERROR")
    private String logLevel;

    @Schema(description = "是否记录升级日志：1-记录 0-不记录")
    private Integer enableLogging;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "备注")
    private String remark;

    // ==================== 查询条件字段 ====================

    @Schema(description = "关键词搜索")
    private String keyword;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    // ==================== 业务方法 ====================

    /**
     * 获取升级类型名称
     */
    public String getUpgradeTypeName() {
        if (this.upgradeType == null) return "未知";
        switch (this.upgradeType) {
            case "MAJOR": return "主版本";
            case "MINOR": return "次版本";
            case "PATCH": return "补丁版本";
            case "HOTFIX": return "热修复";
            default: return "未知";
        }
    }

    /**
     * 获取升级方式名称
     */
    public String getUpgradeModeName() {
        if (this.upgradeMode == null) return "手动升级";
        switch (this.upgradeMode) {
            case "AUTO": return "自动升级";
            case "MANUAL": return "手动升级";
            case "FORCED": return "强制升级";
            default: return "手动升级";
        }
    }

    /**
     * 获取状态名称
     */
    public String getStatusName() {
        if (this.status == null) return "草稿";
        switch (this.status) {
            case "DRAFT": return "草稿";
            case "TESTING": return "测试中";
            case "APPROVED": return "已批准";
            case "RELEASED": return "已发布";
            case "DEPRECATED": return "已废弃";
            default: return "草稿";
        }
    }

    /**
     * 获取状态颜色
     */
    public String getStatusColor() {
        if (this.status == null) return "#d9d9d9";
        switch (this.status) {
            case "DRAFT": return "#d9d9d9";
            case "TESTING": return "#1890ff";
            case "APPROVED": return "#52c41a";
            case "RELEASED": return "#722ed1";
            case "DEPRECATED": return "#f5222d";
            default: return "#d9d9d9";
        }
    }

    /**
     * 获取优先级名称
     */
    public String getPriorityName() {
        if (this.priority == null) return "中";
        switch (this.priority) {
            case 1: return "低";
            case 2: return "中";
            case 3: return "高";
            case 4: return "紧急";
            default: return "中";
        }
    }

    /**
     * 获取优先级颜色
     */
    public String getPriorityColor() {
        if (this.priority == null) return "#1890ff";
        switch (this.priority) {
            case 1: return "#52c41a";
            case 2: return "#1890ff";
            case 3: return "#fa8c16";
            case 4: return "#f5222d";
            default: return "#1890ff";
        }
    }

    /**
     * 获取目标平台名称
     */
    public String getTargetPlatformName() {
        if (this.targetPlatform == null) return "全平台";
        switch (this.targetPlatform) {
            case "ALL": return "全平台";
            case "ANDROID": return "安卓";
            case "IOS": return "苹果";
            case "WEB": return "网页";
            case "DESKTOP": return "桌面";
            default: return "全平台";
        }
    }

    /**
     * 获取目标环境名称
     */
    public String getTargetEnvironmentName() {
        if (this.targetEnvironment == null) return "全环境";
        switch (this.targetEnvironment) {
            case "ALL": return "全环境";
            case "DEV": return "开发";
            case "TEST": return "测试";
            case "STAGING": return "预发布";
            case "PROD": return "生产";
            default: return "全环境";
        }
    }

    /**
     * 判断是否启用
     */
    public boolean isEnabled() {
        return Integer.valueOf(1).equals(this.enabled);
    }

    /**
     * 判断是否已发布
     */
    public boolean isReleased() {
        return "RELEASED".equals(this.status);
    }

    /**
     * 判断是否过期
     */
    public boolean isExpired() {
        if (this.expireTime == null) return false;
        return LocalDateTime.now().isAfter(this.expireTime);
    }

    /**
     * 判断是否生效
     */
    public boolean isEffective() {
        if (this.effectiveTime == null) return true;
        return LocalDateTime.now().isAfter(this.effectiveTime);
    }

    /**
     * 判断是否为强制升级
     */
    public boolean isForcedUpgrade() {
        return "FORCED".equals(this.upgradeMode);
    }

    /**
     * 判断是否为自动升级
     */
    public boolean isAutoUpgrade() {
        return "AUTO".equals(this.upgradeMode);
    }

    /**
     * 判断是否支持断点续传
     */
    public boolean isSupportResume() {
        return Integer.valueOf(1).equals(this.supportResume);
    }

    /**
     * 判断是否支持增量升级
     */
    public boolean isSupportIncremental() {
        return Integer.valueOf(1).equals(this.supportIncremental);
    }

    /**
     * 判断是否启用日志
     */
    public boolean isLoggingEnabled() {
        return Integer.valueOf(1).equals(this.enableLogging);
    }

    /**
     * 获取升级包大小格式化
     */
    public String getPackageSizeFormatted() {
        if (this.packageSize == null) return "0 B";
        
        long size = this.packageSize;
        String[] units = {"B", "KB", "MB", "GB"};
        int unitIndex = 0;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", (double) size, units[unitIndex]);
    }

    /**
     * 验证升级配置
     */
    public boolean isValid() {
        if (this.version == null || this.version.trim().isEmpty()) return false;
        if (this.versionName == null || this.versionName.trim().isEmpty()) return false;
        if (this.packageUrl == null || this.packageUrl.trim().isEmpty()) return false;

        return true;
    }

    /**
     * 获取目标版本
     */
    public String getTargetVersion() {
        return this.version;
    }
}
