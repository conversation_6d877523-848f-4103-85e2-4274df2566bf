package com.jycb.jycbz.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.modules.system.dto.MenuCreateDTO;
import com.jycb.jycbz.modules.system.dto.MenuQueryDTO;
import com.jycb.jycbz.modules.system.dto.MenuUpdateDTO;
import com.jycb.jycbz.modules.system.entity.Menu;
import com.jycb.jycbz.modules.system.entity.Role;
import com.jycb.jycbz.modules.system.mapper.MenuMapper;
import com.jycb.jycbz.modules.system.mapper.PermissionMapper;
import com.jycb.jycbz.modules.system.service.MenuService;
import com.jycb.jycbz.modules.system.service.RoleService;
import com.jycb.jycbz.modules.system.vo.MenuVO;
import com.jycb.jycbz.modules.system.constant.SystemConstants;
import com.jycb.jycbz.modules.system.util.MenuTreeBuilder;
import com.jycb.jycbz.modules.system.util.QueryWrapperBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 菜单服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MenuServiceImpl extends ServiceImpl<MenuMapper, Menu> implements MenuService {

    private final PermissionMapper permissionMapper;
    private final RoleService roleService;

    @Override
    public IPage<MenuVO> getMenuPage(MenuQueryDTO queryDTO) {
        try {
            // 创建分页对象
            Page<Menu> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
            
            // 构建查询条件
            LambdaQueryWrapper<Menu> queryWrapper = new LambdaQueryWrapper<>();
            
            // 菜单名称模糊查询
            if (queryDTO.getMenuName() != null && !queryDTO.getMenuName().trim().isEmpty()) {
                queryWrapper.like(Menu::getMenuName, queryDTO.getMenuName().trim());
            }
            
            // 菜单编码查询已移除，因为数据库中没有menu_code字段
            
            // 菜单类型查询
            if (queryDTO.getMenuType() != null && !queryDTO.getMenuType().trim().isEmpty()) {
                queryWrapper.eq(Menu::getMenuType, queryDTO.getMenuType());
            }
            
            // 父菜单ID查询
            if (queryDTO.getParentId() != null) {
                queryWrapper.eq(Menu::getParentId, queryDTO.getParentId());
            }
            
            // 状态查询
            if (queryDTO.getStatus() != null) {
                queryWrapper.eq(Menu::getStatus, queryDTO.getStatus());
            }
            
            // 可见性查询 - MenuQueryDTO中没有visible字段，暂时注释
            // if (queryDTO.getVisible() != null) {
            //     queryWrapper.eq(Menu::getVisible, queryDTO.getVisible());
            // }
            
            // 创建时间范围查询
            if (queryDTO.getCreateTimeStart() != null && !queryDTO.getCreateTimeStart().trim().isEmpty()) {
                queryWrapper.ge(Menu::getCreateTime, queryDTO.getCreateTimeStart());
            }
            if (queryDTO.getCreateTimeEnd() != null && !queryDTO.getCreateTimeEnd().trim().isEmpty()) {
                queryWrapper.le(Menu::getCreateTime, queryDTO.getCreateTimeEnd());
            }
            
            // 排序
            queryWrapper.orderByAsc(Menu::getOrderNum).orderByDesc(Menu::getCreateTime);
            
            // 执行分页查询
            IPage<Menu> menuPage = this.page(page, queryWrapper);
            
            // 转换为VO
            List<MenuVO> menuVOList = menuPage.getRecords().stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            // 创建返回的分页对象
            Page<MenuVO> resultPage = new Page<>(menuPage.getCurrent(), menuPage.getSize(), menuPage.getTotal());
            resultPage.setRecords(menuVOList);
            
            log.info("分页查询菜单列表成功，页码: {}, 页大小: {}, 总数: {}", 
                    queryDTO.getPageNum(), queryDTO.getPageSize(), menuPage.getTotal());
            
            return resultPage;
            
        } catch (Exception e) {
            log.error("分页查询菜单列表失败", e);
            // 返回空的分页结果
            Page<MenuVO> emptyPage = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize(), 0);
            emptyPage.setRecords(new ArrayList<>());
            return emptyPage;
        }
    }

    @Override
    public List<MenuVO> getMenuTree() {
        try {
            // 查询所有启用的菜单
            List<Menu> menus = this.lambdaQuery()
                    .eq(Menu::getStatus, 1)
                    .orderByAsc(Menu::getOrderNum)
                    .orderByAsc(Menu::getCreateTime)
                    .list();
            
            // 转换为VO
            List<MenuVO> menuVOList = menus.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            // 构建树形结构
            List<MenuVO> menuTree = buildMenuTree(menuVOList);
            
            log.info("获取菜单树形结构成功，共{}个菜单", menuVOList.size());
            return menuTree;
            
        } catch (Exception e) {
            log.error("获取菜单树形结构失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public MenuVO getMenuDetail(Long menuId) {
        try {
            Menu menu = this.getById(menuId);
            if (menu == null) {
                log.warn("获取菜单详情失败，菜单不存在，菜单ID: {}", menuId);
                return null;
            }
            
            MenuVO menuVO = convertToVO(menu);
            log.info("获取菜单详情成功，菜单ID: {}, 菜单名称: {}", menuId, menu.getMenuName());
            return menuVO;
            
        } catch (Exception e) {
            log.error("获取菜单详情失败，菜单ID: {}", menuId, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createMenu(MenuCreateDTO createDTO) {
        try {
            // 检查菜单编码是否已存在
            if (checkMenuCodeExists(createDTO.getMenuCode(), null)) {
                log.warn("创建菜单失败，菜单编码已存在: {}", createDTO.getMenuCode());
                return false;
            }
            
            // 检查菜单名称是否已存在
            if (checkMenuNameExists(createDTO.getMenuName(), createDTO.getParentId(), null)) {
                log.warn("创建菜单失败，菜单名称已存在: {}", createDTO.getMenuName());
                return false;
            }
            
            // 创建菜单
            Menu menu = new Menu();
            BeanUtils.copyProperties(createDTO, menu);
            
            boolean success = this.save(menu);
            
            if (success) {
                log.info("创建菜单成功，菜单编码: {}, 菜单名称: {}", createDTO.getMenuCode(), createDTO.getMenuName());
            } else {
                log.warn("创建菜单失败，菜单编码: {}", createDTO.getMenuCode());
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("创建菜单失败，菜单编码: {}", createDTO.getMenuCode(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMenu(MenuUpdateDTO updateDTO) {
        try {
            Menu existingMenu = this.getById(updateDTO.getId());
            if (existingMenu == null) {
                log.warn("更新菜单失败，菜单不存在，菜单ID: {}", updateDTO.getId());
                return false;
            }
            
            // 检查菜单编码是否已存在（排除当前菜单）
            if (checkMenuCodeExists(updateDTO.getMenuCode(), updateDTO.getId())) {
                log.warn("更新菜单失败，菜单编码已存在: {}", updateDTO.getMenuCode());
                return false;
            }
            
            // 检查菜单名称是否已存在（排除当前菜单）
            if (checkMenuNameExists(updateDTO.getMenuName(), updateDTO.getParentId(), updateDTO.getId())) {
                log.warn("更新菜单失败，菜单名称已存在: {}", updateDTO.getMenuName());
                return false;
            }
            
            // 更新菜单信息
            Menu menu = new Menu();
            BeanUtils.copyProperties(updateDTO, menu);
            
            boolean success = this.updateById(menu);
            
            if (success) {
                log.info("更新菜单成功，菜单ID: {}, 菜单名称: {}", updateDTO.getId(), updateDTO.getMenuName());
            } else {
                log.warn("更新菜单失败，菜单ID: {}", updateDTO.getId());
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("更新菜单失败，菜单ID: {}", updateDTO.getId(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMenu(Long menuId) {
        try {
            Menu menu = this.getById(menuId);
            if (menu == null) {
                log.warn("删除菜单失败，菜单不存在，菜单ID: {}", menuId);
                return false;
            }
            
            // 检查是否有子菜单
            if (hasChildMenus(menuId)) {
                log.warn("删除菜单失败，菜单存在子菜单，菜单ID: {}", menuId);
                return false;
            }
            
            // 执行删除
            boolean success = this.removeById(menuId);
            
            if (success) {
                log.info("删除菜单成功，菜单ID: {}, 菜单名称: {}", menuId, menu.getMenuName());
            } else {
                log.warn("删除菜单失败，菜单ID: {}", menuId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("删除菜单失败，菜单ID: {}", menuId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteMenu(List<Long> menuIds) {
        try {
            if (menuIds == null || menuIds.isEmpty()) {
                log.warn("批量删除菜单失败，菜单ID列表为空");
                return false;
            }

            // 检查菜单是否存在
            List<Menu> menus = this.lambdaQuery()
                    .in(Menu::getId, menuIds)
                    .list();

            if (menus.isEmpty()) {
                log.warn("批量删除菜单失败，没有找到任何菜单，菜单ID列表: {}", menuIds);
                return false;
            }

            // 检查是否有子菜单
            for (Long menuId : menuIds) {
                if (hasChildMenus(menuId)) {
                    log.warn("批量删除菜单失败，菜单{}存在子菜单", menuId);
                    return false;
                }
            }

            // 执行批量删除
            boolean success = this.removeByIds(menuIds);

            if (success) {
                log.info("批量删除菜单成功，删除数量: {}, 菜单ID列表: {}", menus.size(), menuIds);
            } else {
                log.warn("批量删除菜单失败，菜单ID列表: {}", menuIds);
            }

            return success;

        } catch (Exception e) {
            log.error("批量删除菜单失败，菜单ID列表: {}", menuIds, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(Long menuId, Integer status) {
        try {
            Menu menu = this.getById(menuId);
            if (menu == null) {
                log.warn("更新菜单状态失败，菜单不存在，菜单ID: {}", menuId);
                return false;
            }

            menu.setStatus(status);
            boolean success = this.updateById(menu);

            if (success) {
                log.info("更新菜单状态成功，菜单ID: {}, 新状态: {}", menuId, status);
            } else {
                log.warn("更新菜单状态失败，菜单ID: {}, 新状态: {}", menuId, status);
            }

            return success;

        } catch (Exception e) {
            log.error("更新菜单状态失败，菜单ID: {}, 新状态: {}", menuId, status, e);
            return false;
        }
    }

    @Override
    public List<MenuVO> getMenusByAdminId(Long adminId) {
        try {
            // 通过PermissionMapper查询管理员菜单
            List<com.jycb.jycbz.modules.system.model.Menu> menus = permissionMapper.findMenusByAdminId(adminId.intValue());

            // 转换为MenuVO
            List<MenuVO> menuVOList = new ArrayList<>();
            for (com.jycb.jycbz.modules.system.model.Menu menu : menus) {
                MenuVO menuVO = new MenuVO();
                menuVO.setId(menu.getId().longValue());
                menuVO.setMenuName(menu.getMenuName());
                menuVO.setMenuCode(menu.getName());
                menuVO.setMenuType(menu.getType() != null ? menu.getType().toString() : "menu");
                menuVO.setParentId(menu.getParentId().longValue());
                menuVO.setPath(menu.getPath());
                menuVO.setComponent(menu.getComponent());
                menuVO.setPermission(menu.getPermission());
                menuVO.setIcon(menu.getIcon());
                menuVO.setOrderNum(menu.getOrderNum());
                menuVO.setIsFrame(menu.getExternal() != null && menu.getExternal() == 1 ? 1 : 0);
                menuVO.setIsCache(menu.getKeepAlive() != null && menu.getKeepAlive() == 1 ? 1 : 0);
                menuVO.setVisible(menu.getVisible() != null && menu.getVisible() == 1 ? 1 : 0);
                menuVO.setStatus(menu.getStatus());
                menuVO.setCreateTime(menu.getCreateTime());
                menuVO.setUpdateTime(menu.getUpdateTime());

                // 设置路由元信息
                MenuVO.Meta meta = new MenuVO.Meta();
                meta.setTitle(menu.getTitle());
                meta.setIcon(menu.getIcon());
                meta.setHidden(menu.getVisible() == null || menu.getVisible() == 0);
                meta.setKeepAlive(menu.getKeepAlive() != null && menu.getKeepAlive() == 1);
                meta.setIsLink(menu.getExternal() != null && menu.getExternal() == 1);
                meta.setPermission(menu.getPermission());
                menuVO.setMeta(meta);

                menuVOList.add(menuVO);
            }

            // 构建树形结构
            List<MenuVO> menuTree = buildMenuTree(menuVOList);

            log.info("根据管理员ID获取菜单列表成功，管理员ID: {}, 共{}个菜单", adminId, menuVOList.size());
            return menuTree;

        } catch (Exception e) {
            log.error("根据管理员ID获取菜单列表失败，管理员ID: {}", adminId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MenuVO> getMenusByRoleId(Long roleId) {
        try {
            // 参数验证
            if (roleId == null || roleId <= 0) {
                log.warn("根据角色ID获取菜单列表失败，角色ID无效: {}", roleId);
                return new ArrayList<>();
            }

            // 检查角色是否存在且有效
            Role role = roleService.getById(roleId);
            if (role == null) {
                log.warn("根据角色ID获取菜单列表失败，角色不存在: {}", roleId);
                return new ArrayList<>();
            }

            if (role.getStatus() == null || role.getStatus() != 1) {
                log.warn("根据角色ID获取菜单列表失败，角色已禁用: {}", roleId);
                return new ArrayList<>();
            }

            // 通过MenuMapper查询角色关联的菜单
            List<Menu> menus = baseMapper.selectMenusByRoleId(roleId);

            if (menus == null || menus.isEmpty()) {
                log.info("根据角色ID获取菜单列表，角色ID: {}，未找到关联菜单", roleId);
                return new ArrayList<>();
            }

            // 转换为VO对象
            List<MenuVO> menuVOList = menus.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            // 构建树形结构
            List<MenuVO> menuTree = buildMenuTree(menuVOList);

            log.info("根据角色ID获取菜单列表成功，角色ID: {}, 共{}个菜单", roleId, menuVOList.size());
            return menuTree;

        } catch (Exception e) {
            log.error("根据角色ID获取菜单列表失败，角色ID: {}", roleId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MenuVO> getMenusByType(String menuType) {
        try {
            List<Menu> menus = this.lambdaQuery()
                    .eq(Menu::getMenuType, menuType)
                    .eq(Menu::getStatus, 1)
                    .orderByAsc(Menu::getOrderNum)
                    .orderByAsc(Menu::getCreateTime)
                    .list();

            List<MenuVO> menuVOList = menus.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            log.info("根据菜单类型获取菜单列表成功，菜单类型: {}, 共{}个菜单", menuType, menuVOList.size());
            return menuVOList;

        } catch (Exception e) {
            log.error("根据菜单类型获取菜单列表失败，菜单类型: {}", menuType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将Menu实体转换为MenuVO
     */
    private MenuVO convertToVO(Menu menu) {
        MenuVO menuVO = new MenuVO();
        BeanUtils.copyProperties(menu, menuVO);

        // 设置路由元信息
        MenuVO.Meta meta = new MenuVO.Meta();
        meta.setTitle(menu.getMenuName());
        meta.setIcon(menu.getIcon());
        meta.setHidden(menu.getVisible() == 0);
        meta.setKeepAlive(menu.getIsCache() != null && menu.getIsCache() == 1);
        meta.setIsLink(menu.getExternal() != null && menu.getExternal() == 1);
        meta.setPermission(menu.getPermission());
        menuVO.setMeta(meta);

        return menuVO;
    }

    @Override
    public boolean checkMenuCodeExists(String menuCode, Long excludeId) {
        // 数据库中没有menu_code字段，直接返回false
        log.warn("checkMenuCodeExists方法已废弃，因为数据库中没有menu_code字段");
        return false;
    }

    @Override
    public boolean checkMenuNameExists(String menuName, Long parentId, Long excludeId) {
        try {
            var query = this.lambdaQuery()
                    .eq(Menu::getMenuName, menuName)
                    .eq(Menu::getParentId, parentId != null ? parentId : 0);
            if (excludeId != null) {
                query.ne(Menu::getId, excludeId);
            }
            long count = query.count();

            log.info("检查菜单名称是否存在，菜单名称: {}, 父菜单ID: {}, 排除ID: {}, 结果: {}",
                    menuName, parentId, excludeId, count > 0);
            return count > 0;

        } catch (Exception e) {
            log.error("检查菜单名称是否存在失败，菜单名称: {}, 父菜单ID: {}, 排除ID: {}",
                    menuName, parentId, excludeId, e);
            return false;
        }
    }

    @Override
    public List<MenuVO> getAllAvailableMenus() {
        try {
            List<Menu> menus = this.lambdaQuery()
                    .eq(Menu::getStatus, 1)
                    .orderByAsc(Menu::getOrderNum)
                    .orderByAsc(Menu::getCreateTime)
                    .list();

            List<MenuVO> menuVOList = menus.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            log.info("获取所有可用菜单成功，共{}个菜单", menuVOList.size());
            return menuVOList;

        } catch (Exception e) {
            log.error("获取所有可用菜单失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MenuVO> getChildMenus(Long parentId) {
        try {
            List<Menu> menus = this.lambdaQuery()
                    .eq(Menu::getParentId, parentId)
                    .eq(Menu::getStatus, 1)
                    .orderByAsc(Menu::getOrderNum)
                    .orderByAsc(Menu::getCreateTime)
                    .list();

            List<MenuVO> menuVOList = menus.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());

            log.info("根据父菜单ID获取子菜单列表成功，父菜单ID: {}, 共{}个子菜单", parentId, menuVOList.size());
            return menuVOList;

        } catch (Exception e) {
            log.error("根据父菜单ID获取子菜单列表失败，父菜单ID: {}", parentId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<MenuVO> buildMenuTree(List<MenuVO> menus) {
        return MenuTreeBuilder.buildTree(menus);
    }

    @Override
    public List<MenuVO> buildMenuTree(List<MenuVO> menus, Long parentId) {
        return MenuTreeBuilder.buildTree(menus, parentId);
    }

    @Override
    public List<MenuVO> getUserRoutes(Long adminId) {
        try {
            // 获取用户菜单
            List<MenuVO> userMenus = getMenusByAdminId(adminId);

            // 过滤出路由菜单（菜单类型为menu且可见）
            List<MenuVO> routes = userMenus.stream()
                    .filter(menu -> "menu".equals(menu.getMenuType()) && menu.getVisible() == 1)
                    .collect(Collectors.toList());

            log.info("获取用户可访问的路由成功，管理员ID: {}, 共{}个路由", adminId, routes.size());
            return routes;

        } catch (Exception e) {
            log.error("获取用户可访问的路由失败，管理员ID: {}", adminId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean hasChildMenus(Long menuId) {
        try {
            long count = this.lambdaQuery()
                    .eq(Menu::getParentId, menuId)
                    .count();

            boolean hasChildren = count > 0;
            log.info("检查菜单是否有子菜单，菜单ID: {}, 结果: {}", menuId, hasChildren);
            return hasChildren;

        } catch (Exception e) {
            log.error("检查菜单是否有子菜单失败，菜单ID: {}", menuId, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSort(Long menuId, Integer sort) {
        try {
            Menu menu = this.getById(menuId);
            if (menu == null) {
                log.warn("更新菜单排序失败，菜单不存在，菜单ID: {}", menuId);
                return false;
            }

            menu.setOrderNum(sort);
            boolean success = this.updateById(menu);

            if (success) {
                log.info("更新菜单排序成功，菜单ID: {}, 新排序: {}", menuId, sort);
            } else {
                log.warn("更新菜单排序失败，菜单ID: {}, 新排序: {}", menuId, sort);
            }

            return success;

        } catch (Exception e) {
            log.error("更新菜单排序失败，菜单ID: {}, 新排序: {}", menuId, sort, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateSort(List<MenuVO> menuSorts) {
        try {
            if (menuSorts == null || menuSorts.isEmpty()) {
                log.warn("批量更新菜单排序失败，菜单列表为空");
                return false;
            }

            for (MenuVO menuVO : menuSorts) {
                if (menuVO.getId() != null && menuVO.getOrderNum() != null) {
                    updateSort(menuVO.getId(), menuVO.getOrderNum());
                }
            }

            log.info("批量更新菜单排序成功，更新数量: {}", menuSorts.size());
            return true;

        } catch (Exception e) {
            log.error("批量更新菜单排序失败", e);
            return false;
        }
    }
}
