package com.jycb.jycbz.modules.system.service;

import java.util.List;
import java.util.Map;

/**
 * 系统监控服务接口
 */
public interface SystemMonitorService {

    // ==================== 系统信息监控 ====================

    /**
     * 获取系统概览信息
     */
    Map<String, Object> getSystemOverview();

    /**
     * 获取系统基本信息
     */
    Map<String, Object> getSystemInfo();

    /**
     * 获取操作系统信息
     */
    Map<String, Object> getOperatingSystemInfo();

    /**
     * 获取硬件信息
     */
    Map<String, Object> getHardwareInfo();

    // ==================== JVM监控 ====================

    /**
     * 获取JVM信息
     */
    Map<String, Object> getJvmInfo();

    /**
     * 获取JVM内存信息
     */
    Map<String, Object> getJvmMemoryInfo();

    /**
     * 获取JVM垃圾回收信息
     */
    Map<String, Object> getJvmGcInfo();

    /**
     * 获取JVM线程信息
     */
    Map<String, Object> getJvmThreadInfo();

    /**
     * 获取JVM类加载信息
     */
    Map<String, Object> getJvmClassLoadingInfo();

    // ==================== 性能监控 ====================

    /**
     * 获取CPU使用率
     */
    Map<String, Object> getCpuUsage();

    /**
     * 获取内存使用情况
     */
    Map<String, Object> getMemoryUsage();

    /**
     * 获取磁盘使用情况
     */
    Map<String, Object> getDiskUsage();

    /**
     * 获取网络使用情况
     */
    Map<String, Object> getNetworkUsage();

    /**
     * 获取系统负载
     */
    Map<String, Object> getSystemLoad();

    // ==================== 应用监控 ====================

    /**
     * 获取应用运行状态
     */
    Map<String, Object> getApplicationStatus();

    /**
     * 获取应用性能指标
     */
    Map<String, Object> getApplicationMetrics();

    /**
     * 获取应用健康检查
     */
    Map<String, Object> getApplicationHealthCheck();

    /**
     * 获取应用配置信息
     */
    Map<String, Object> getApplicationConfig();

    // ==================== 数据库监控 ====================

    /**
     * 获取数据库连接信息
     */
    Map<String, Object> getDatabaseConnectionInfo();

    /**
     * 获取数据库性能指标
     */
    Map<String, Object> getDatabaseMetrics();

    /**
     * 获取数据库连接池状态
     */
    Map<String, Object> getDatabasePoolStatus();

    /**
     * 获取数据库慢查询
     */
    List<Map<String, Object>> getDatabaseSlowQueries();

    // ==================== 缓存监控 ====================

    /**
     * 获取Redis连接信息
     */
    Map<String, Object> getRedisConnectionInfo();

    /**
     * 获取Redis性能指标
     */
    Map<String, Object> getRedisMetrics();

    /**
     * 获取Redis内存使用情况
     */
    Map<String, Object> getRedisMemoryUsage();

    /**
     * 获取Redis键空间信息
     */
    Map<String, Object> getRedisKeyspaceInfo();

    // ==================== 日志监控 ====================

    /**
     * 获取日志统计信息
     */
    Map<String, Object> getLogStatistics();

    /**
     * 获取日志统计信息（带参数）
     */
    Map<String, Object> getLogStatistics(String startTime, String endTime);

    /**
     * 获取错误日志统计
     */
    Map<String, Object> getErrorLogStatistics();

    /**
     * 获取最近的错误日志
     */
    List<Map<String, Object>> getRecentErrorLogs();

    /**
     * 获取日志文件信息
     */
    List<Map<String, Object>> getLogFileInfo();

    // ==================== 接口监控 ====================

    /**
     * 获取API调用统计
     */
    Map<String, Object> getApiCallStatistics();

    /**
     * 获取API响应时间统计
     */
    Map<String, Object> getApiResponseTimeStatistics();

    /**
     * 获取API错误率统计
     */
    Map<String, Object> getApiErrorRateStatistics();

    /**
     * 获取最慢的API接口
     */
    List<Map<String, Object>> getSlowestApis();

    // ==================== 用户监控 ====================

    /**
     * 获取在线用户统计
     */
    Map<String, Object> getOnlineUserStatistics();

    /**
     * 获取用户活跃度统计
     */
    Map<String, Object> getUserActivityStatistics();

    /**
     * 获取用户地域分布
     */
    Map<String, Object> getUserGeographicDistribution();

    /**
     * 获取用户设备统计
     */
    Map<String, Object> getUserDeviceStatistics();

    // ==================== 业务监控 ====================

    /**
     * 获取业务指标概览
     */
    Map<String, Object> getBusinessMetricsOverview();

    /**
     * 获取订单统计
     */
    Map<String, Object> getOrderStatistics();

    /**
     * 获取支付统计
     */
    Map<String, Object> getPaymentStatistics();

    /**
     * 获取设备状态统计
     */
    Map<String, Object> getDeviceStatusStatistics();

    // ==================== 告警监控 ====================

    /**
     * 获取系统告警列表
     */
    List<Map<String, Object>> getSystemAlerts();

    /**
     * 获取系统告警列表（带参数）
     */
    List<Map<String, Object>> getSystemAlerts(String level, Boolean resolved);

    /**
     * 获取告警统计
     */
    Map<String, Object> getAlertStatistics();

    /**
     * 创建系统告警
     */
    boolean createSystemAlert(String alertType, String message, String level);

    /**
     * 处理系统告警
     */
    boolean handleSystemAlert(Long alertId, String action);

    /**
     * 处理告警
     */
    boolean handleAlert(Long alertId, String action);

    // ==================== 历史数据 ====================

    /**
     * 获取历史性能数据
     */
    List<Map<String, Object>> getHistoricalPerformanceData(String metricType, int hours);

    /**
     * 获取历史业务数据
     */
    List<Map<String, Object>> getHistoricalBusinessData(String dataType, int days);

    /**
     * 清理历史监控数据
     */
    boolean cleanHistoricalData(int retentionDays);

    // ==================== 实时监控 ====================

    /**
     * 获取实时系统状态
     */
    Map<String, Object> getRealTimeSystemStatus();

    /**
     * 获取实时性能指标
     */
    Map<String, Object> getRealTimePerformanceMetrics();

    /**
     * 开始实时监控
     */
    boolean startRealTimeMonitoring();

    /**
     * 停止实时监控
     */
    boolean stopRealTimeMonitoring();

    // ==================== 监控配置 ====================

    /**
     * 获取监控配置
     */
    Map<String, Object> getMonitoringConfig();

    /**
     * 更新监控配置
     */
    boolean updateMonitoringConfig(Map<String, Object> config);

    /**
     * 重置监控配置
     */
    boolean resetMonitoringConfig();

    // ==================== 导出报告 ====================

    /**
     * 生成系统监控报告
     */
    String generateSystemMonitoringReport();

    /**
     * 导出监控数据
     */
    byte[] exportMonitoringData(String format, String dataType);

    /**
     * 获取监控报告模板
     */
    String getMonitoringReportTemplate();

    // ==================== 扩展方法 ====================

    /**
     * 获取服务器信息
     */
    Map<String, Object> getServerInfo();

    /**
     * 获取数据库信息
     */
    Map<String, Object> getDatabaseInfo();

    /**
     * 获取Redis信息
     */
    Map<String, Object> getRedisInfo();

    /**
     * 获取性能指标
     */
    Map<String, Object> getPerformanceMetrics();

    /**
     * 获取在线用户
     */
    Map<String, Object> getOnlineUsers();

    /**
     * 获取API统计
     */
    Map<String, Object> getApiStatistics(String startTime, String endTime);

    /**
     * 获取业务统计
     */
    Map<String, Object> getBusinessStatistics(String startTime, String endTime, String businessType);

    /**
     * 获取健康检查
     */
    Map<String, Object> getHealthCheck();

    /**
     * 清理缓存
     */
    boolean clearCache(String cacheType);

    /**
     * 重启服务
     */
    boolean restartService(String serviceName);

    /**
     * 导出系统报告
     */
    Map<String, Object> exportSystemReport(String reportType, String startTime, String endTime);

    /**
     * 获取配置检查
     */
    Map<String, Object> getConfigCheck();

    /**
     * 获取安全检查
     */
    Map<String, Object> getSecurityCheck();
}
