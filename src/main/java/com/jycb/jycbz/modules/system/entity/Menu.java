package com.jycb.jycbz.modules.system.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 菜单实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("jy_menu")
@Schema(description = "菜单实体")
@Alias("SystemMenu")
public class Menu implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "菜单ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "菜单名称/路由名称")
    @TableField("name")
    private String menuName;

    @Schema(description = "菜单标题")
    @TableField("title")
    private String title;

    @Schema(description = "菜单类型：0-目录 1-菜单 2-按钮")
    @TableField("type")
    private Integer menuType;

    @Schema(description = "父菜单ID")
    private Long parentId;

    @Schema(description = "路由地址")
    private String path;

    @Schema(description = "组件路径")
    private String component;

    @Schema(description = "重定向地址")
    private String redirect;

    @Schema(description = "权限标识")
    private String permission;

    @Schema(description = "菜单图标")
    private String icon;

    @Schema(description = "显示顺序")
    @TableField("sort")
    private Integer orderNum;

    @Schema(description = "是否隐藏：0-显示 1-隐藏")
    private Integer hidden;

    @Schema(description = "菜单状态：1-显示 0-隐藏")
    private Integer visible;

    @Schema(description = "是否缓存：0-不缓存 1-缓存")
    @TableField("keep_alive")
    private Integer isCache;

    @Schema(description = "是否外链：0-否 1-是")
    private Integer external;

    @Schema(description = "外链地址")
    @TableField("external_url")
    private String externalUrl;

    @Schema(description = "状态：1-正常 0-禁用")
    private Integer status;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;


}
