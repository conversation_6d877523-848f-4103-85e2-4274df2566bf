package com.jycb.jycbz.modules.order.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.lock.DistributedLock;
import com.jycb.jycbz.common.utils.SnowflakeIdGenerator;
import com.jycb.jycbz.common.utils.SpringUtils;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.device.service.DeviceFeeRealTimeService;
import com.jycb.jycbz.modules.finance.event.OrderCompletedEvent;
import com.jycb.jycbz.modules.order.convert.OrderConverter;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.event.OrderEvent;
import com.jycb.jycbz.modules.order.event.OrderStatusChangeEvent;
import com.jycb.jycbz.modules.order.mapper.OrderMapper;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.order.service.OrderStatusLogService;
import com.jycb.jycbz.modules.order.vo.OrderVO;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 */
@Slf4j
@Service
public class OrderServiceImpl extends ServiceImpl<OrderMapper, Order> implements OrderService {

    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    @Autowired
    @Lazy
    private DeviceService deviceService;
    
    @Autowired
    private SnowflakeIdGenerator idGenerator;
    
    @Autowired
    private DistributedLock distributedLock;
    
    @Autowired
    private OrderConverter orderConverter;
    
    @Autowired
    private OrderStatusLogService orderStatusLogService;

    @Autowired
    private DeviceFeeRealTimeService deviceFeeRealTimeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order createOrder(Integer deviceId, String deviceNo, Integer userId, BigDecimal amount) {
        // 使用分布式锁确保设备操作的原子性
        return distributedLock.executeWithLock("order:device:" + deviceId, () -> {
            // 检查设备是否存在
            if (!deviceService.checkDeviceExists(deviceId)) {
                throw new BusinessException("设备不存在");
            }
            
            // 检查设备是否可用
            if (!deviceService.checkDeviceAvailable(deviceId)) {
                throw new BusinessException("设备当前不可用");
            }
            
            // 获取设备所属关系
            Map<String, Object> deviceInfo = deviceService.getDeviceOwnership(deviceId);
            Integer entityId = (Integer) deviceInfo.get("entityId");
            Integer partnerId = (Integer) deviceInfo.get("partnerId");
            Integer shopId = (Integer) deviceInfo.get("shopId");
            
            // 如果未指定金额，则获取设备价格
            BigDecimal orderAmount = amount;
            if (orderAmount == null || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
                orderAmount = deviceService.getDevicePrice(deviceId);
            }

            // 创建订单
            Order order = new Order();
            order.setOrderNo(generateOrderNo());
            order.setDeviceId(deviceId);
            order.setDeviceNo(deviceNo);
            order.setEntityId(entityId.longValue());
            order.setPartnerId(partnerId.longValue());
            order.setShopId(shopId.longValue());
            order.setUserId(userId.intValue());
            order.setOrderStatus(0); // 待支付
            order.setStartTime(null); // 支付后才设置开始时间
            order.setAmount(orderAmount);
            order.setPayStatus(0); // 未支付
            order.setRefundStatus(0); // 未退款
            order.setCommissionStatus(0); // 未分账
            
            // 保存订单
            save(order);
            log.info("订单创建成功，订单号：{}", order.getOrderNo());
            
            // 发布订单创建事件
            eventPublisher.publishEvent(new OrderStatusChangeEvent(
                    this, order, OrderStatusChangeEvent.OrderStatusType.CREATED));
            
            return order;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payOrder(String orderNo, String payType, String transactionId) {
        log.info("支付订单: orderNo={}, payType={}, transactionId={}", orderNo, payType, transactionId);
        
        // 使用分布式锁确保支付操作的原子性
        return distributedLock.executeWithLock("order:pay:" + orderNo, () -> {
            // 更新订单支付信息
            boolean updated = update(new LambdaUpdateWrapper<Order>()
                    .eq(Order::getOrderNo, orderNo)
                    .eq(Order::getPayStatus, 0) // 确保只更新未支付的订单
                    .set(Order::getPayStatus, 1)
                    .set(Order::getPayType, payType)
                    .set(Order::getTransactionId, transactionId)
                    .set(Order::getPayTime, LocalDateTime.now())
                    .set(Order::getStartTime, LocalDateTime.now()) // 设置开始时间为支付时间
                    .set(Order::getOrderStatus, 1) // 设置订单状态为进行中
            );
            
            if (updated) {
                // 获取更新后的订单信息
                Order order = getOrderByOrderNo(orderNo);
                if (order != null) {
                    log.info("订单支付成功: {}", orderNo);
                    
                    // 发布订单支付事件
                    eventPublisher.publishEvent(new OrderStatusChangeEvent(
                            this, order, OrderStatusChangeEvent.OrderStatusType.PAID));
                    
                    // 发布订单事件
                    eventPublisher.publishEvent(new OrderEvent(
                            this, order, OrderEvent.OrderEventType.PAID));

                    // 注意：分成事件应该在订单完成时触发，而不是支付时
                    log.info("订单支付成功，等待订单完成后触发分成，订单编号：{}", orderNo);
                    
                    // 更新设备状态为使用中
                    deviceService.updateDeviceUseStatus(order.getDeviceId().intValue(), true);
                }
            }
            
            return updated;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeOrder(String orderId, String orderNo, Integer entityId, Integer partnerId, Integer shopId, BigDecimal orderAmount) {
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单编号不能为空");
        }
        
        // 使用分布式锁确保订单完成操作的原子性
        return distributedLock.executeWithLock("order:complete:" + orderNo, () -> {
            // 查询订单
            Order order = getOrderByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在");
            }
            
            // 检查订单状态
            if (order.getOrderStatus() != 1) {
                throw new BusinessException("只有进行中的订单才能完成");
            }
            
            if (order.getPayStatus() != 1) {
                throw new BusinessException("未支付的订单不能完成");
            }
            
            // 更新订单状态
            order.setOrderStatus(2); // 已完成
            order.setEndTime(LocalDateTime.now());
            
            boolean updated = updateById(order);
            if (updated) {
                // 包天计费模式下，完成订单时保持设备状态为"使用中"
                // 不再调用 deviceService.updateDeviceUseStatus(order.getDeviceId(), false);
                
                log.info("订单完成成功，订单号：{}", orderNo);
                
                // 发布订单完成事件
                eventPublisher.publishEvent(new OrderStatusChangeEvent(
                        this, order, OrderStatusChangeEvent.OrderStatusType.COMPLETED));
                
                // 检查是否已经分成，避免重复分成
                if (order.getCommissionStatus() == 0) {
                    // 发布财务分账事件
                    OrderCompletedEvent event = new OrderCompletedEvent(
                            this,
                            order.getId().toString(),
                            orderNo,
                            entityId != null ? entityId : (order.getEntityId() != null ? order.getEntityId().intValue() : null),
                            partnerId != null ? partnerId : (order.getPartnerId() != null ? order.getPartnerId().intValue() : null),
                            shopId != null ? shopId : (order.getShopId() != null ? order.getShopId().intValue() : null),
                            orderAmount != null ? orderAmount : order.getAmount()
                    );
                    eventPublisher.publishEvent(event);
                    log.info("已触发订单分成事件，订单编号：{}", orderNo);
                } else {
                    log.info("订单已分成，跳过分成事件，订单编号：{}", orderNo);
                }
            }
            
            return updated;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String orderNo, String reason) {
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单编号不能为空");
        }
        
        // 使用分布式锁确保订单取消操作的原子性
        return distributedLock.executeWithLock("order:cancel:" + orderNo, () -> {
            // 查询订单
            Order order = getOrderByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在");
            }
            
            // 检查订单状态 - 允许取消待支付(0)和进行中(1)的订单
            if (order.getOrderStatus() != 1 && order.getOrderStatus() != 0) {
                throw new BusinessException("只有待支付或进行中的订单才能取消");
            }
            
            // 如果已支付，需要退款
            if (order.getPayStatus() == 1) {
                throw new BusinessException("已支付的订单需要先退款才能取消");
            }
            
            // 更新订单状态
            order.setOrderStatus(3); // 已取消
            order.setEndTime(LocalDateTime.now());
            order.setRemark(reason);
            
            boolean updated = updateById(order);
            if (updated) {
                // 更新设备使用状态
                deviceService.updateDeviceUseStatus(order.getDeviceId().intValue(), false);
                
                // 发布订单取消事件
                eventPublisher.publishEvent(new OrderStatusChangeEvent(
                        this, order, OrderStatusChangeEvent.OrderStatusType.CANCELED));
                
                log.info("订单取消成功，订单号：{}", orderNo);
            }
            
            return updated;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundOrder(String orderNo, BigDecimal refundAmount, String refundReason) {
        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单编号不能为空");
        }
        
        // 使用分布式锁确保订单退款操作的原子性
        return distributedLock.executeWithLock("order:refund:" + orderNo, () -> {
            // 查询订单
            Order order = getOrderByOrderNo(orderNo);
            if (order == null) {
                throw new BusinessException("订单不存在");
            }
            
            // 检查订单状态
            if (order.getPayStatus() != 1) {
                throw new BusinessException("订单未支付，无法退款");
            }
            
            if (order.getRefundStatus() == 1) {
                throw new BusinessException("订单已退款，请勿重复操作");
            }
            
            // 检查退款金额
            if (refundAmount.compareTo(order.getAmount()) > 0) {
                throw new BusinessException("退款金额不能大于订单金额");
            }
            
            // 更新订单状态
            order.setOrderStatus(4); // 已退款
            order.setRefundStatus(1); // 已退款
            order.setRefundTime(LocalDateTime.now());
            order.setRefundAmount(refundAmount);
            order.setRefundReason(refundReason);
            order.setEndTime(LocalDateTime.now());
            
            boolean updated = updateById(order);
            if (updated) {
                // 更新设备使用状态
                deviceService.updateDeviceUseStatus(order.getDeviceId().intValue(), false);
                
                // 发布订单退款事件
                eventPublisher.publishEvent(new OrderStatusChangeEvent(
                        this, order, OrderStatusChangeEvent.OrderStatusType.REFUNDED));
                
                log.info("订单退款成功，订单号：{}，退款金额：{}", orderNo, refundAmount);
            }
            
            return updated;
        });
    }

    @Override
    public IPage<Order> pageOrders(Page<Order> page, String orderNo, String deviceNo, Integer userId, 
                                 Integer orderStatus, Integer payStatus, Integer entityId, 
                                 Integer partnerId, Integer shopId, LocalDateTime startTime, 
                                 LocalDateTime endTime) {
        return baseMapper.selectOrderPage(page, orderNo, deviceNo, userId, orderStatus, payStatus, 
                                        entityId, partnerId, shopId, startTime, endTime);
    }

    @Override
    public Order getOrderByOrderNo(String orderNo) {
        if (!StringUtils.hasText(orderNo)) {
            return null;
        }
        
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getOrderNo, orderNo)
                   .select(Order::getId, Order::getOrderNo, Order::getDeviceId, Order::getDeviceNo,
                          Order::getEntityId, Order::getPartnerId, Order::getShopId, Order::getUserId,
                          Order::getOrderStatus, Order::getStartTime, Order::getEndTime, Order::getAmount,
                          Order::getActualAmount, Order::getDuration, Order::getActualDuration,
                          Order::getPayStatus, Order::getPayTime, Order::getPayType, Order::getTransactionId,
                          Order::getRefundStatus, Order::getRefundTime, Order::getRefundAmount, Order::getRefundReason,
                          Order::getCommissionStatus, Order::getCommissionTime, Order::getRemark,
                          Order::getCreateTime, Order::getUpdateTime); // 明确指定查询字段
        return getOne(queryWrapper);
    }

    @Override
    public List<Order> getActiveOrdersByDeviceId(Integer deviceId) {
        if (deviceId == null) {
            return null;
        }
        
        return baseMapper.selectActiveOrdersByDeviceId(deviceId);
    }

    @Override
    public Map<String, Object> getUserOrderStats(Integer userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        return baseMapper.selectOrderStatsByUserId(userId);
    }

    @Override
    public Map<String, Object> getShopOrderStats(Integer shopId, LocalDateTime startTime, LocalDateTime endTime) {
        if (shopId == null) {
            throw new BusinessException("门店ID不能为空");
        }
        
        return baseMapper.selectOrderStatsByShopId(shopId, startTime, endTime);
    }
    
    /**
     * 生成订单编号
     * 格式：年月日时分秒 + 4位随机数
     */
    private String generateOrderNo() {
        return String.valueOf(idGenerator.nextId());
    }

    @Override
    public Order getByOrderId(String orderId) {
        return lambdaQuery()
                .eq(Order::getOrderNo, orderId)
                .select(Order::getId, Order::getOrderNo, Order::getDeviceId, Order::getDeviceNo,
                       Order::getEntityId, Order::getPartnerId, Order::getShopId, Order::getUserId,
                       Order::getOrderStatus, Order::getStartTime, Order::getEndTime, Order::getAmount,
                       Order::getActualAmount, Order::getDuration, Order::getActualDuration,
                       Order::getPayStatus, Order::getPayTime, Order::getPayType, Order::getTransactionId,
                       Order::getRefundStatus, Order::getRefundTime, Order::getRefundAmount, Order::getRefundReason,
                       Order::getCommissionStatus, Order::getCommissionTime, Order::getRemark,
                       Order::getCreateTime, Order::getUpdateTime) // 明确指定查询字段
                .one();
    }
    @Override
    public int countOrdersByShopAndTime(Long shopId, LocalDateTime startTime, LocalDateTime endTime) {
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getShopId, shopId.intValue());
        if (startTime != null) {
            queryWrapper.ge(Order::getCreateTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(Order::getCreateTime, endTime);
        }
        
        return (int) count(queryWrapper);
    }

    @Override
    public List<Order> getOrdersByShopId(Long shopId) {
        return lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .select(Order::getId, Order::getOrderNo, Order::getDeviceId, Order::getDeviceNo,
                       Order::getEntityId, Order::getPartnerId, Order::getShopId, Order::getUserId,
                       Order::getOrderStatus, Order::getStartTime, Order::getEndTime, Order::getAmount,
                       Order::getActualAmount, Order::getDuration, Order::getActualDuration,
                       Order::getPayStatus, Order::getPayTime, Order::getPayType, Order::getTransactionId,
                       Order::getRefundStatus, Order::getRefundTime, Order::getRefundAmount, Order::getRefundReason,
                       Order::getCommissionStatus, Order::getCommissionTime, Order::getRemark,
                       Order::getCreateTime, Order::getUpdateTime) // 明确指定查询字段
                .orderByDesc(Order::getCreateTime)
                .list();
    }

    @Override
    public Page<Map<String, Object>> getShopOrders(int pageNum, int pageSize, Long shopId, Integer status, String startTime, String endTime, String roomNumber) {
        return null;
    }

    @Override
    public Map<String, Object> getShopOrderStatistics(Long shopId, String startTime, String endTime) {
        return null;
    }

    @Override
    public Page<Map<String, Object>> getPartnerOrders(int pageNum, int pageSize, Long partnerId, Long shopId, Integer status, String startTime, String endTime) {
        Page<Map<String, Object>> page = new Page<>(pageNum, pageSize);
        
        // 构建查询条件
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getPartnerId, partnerId.intValue());
        
        if (shopId != null) {
            queryWrapper.eq(Order::getShopId, shopId.intValue());
        }
        
        if (status != null) {
            queryWrapper.eq(Order::getOrderStatus, status);
        }
        
        // 处理时间范围
        if (StringUtils.hasText(startTime)) {
            LocalDateTime startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryWrapper.ge(Order::getCreateTime, startDateTime);
        }
        
        if (StringUtils.hasText(endTime)) {
            LocalDateTime endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            queryWrapper.le(Order::getCreateTime, endDateTime);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(Order::getCreateTime);
        
        // 执行分页查询
        Page<Order> orderPage = page(new Page<>(pageNum, pageSize), queryWrapper);
        
        // 转换结果为Map
        List<Map<String, Object>> records = orderPage.getRecords().stream()
                .map(order -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", order.getId());
                    map.put("orderNo", order.getOrderNo());
                    map.put("deviceNo", order.getDeviceNo());
                    map.put("amount", order.getAmount());
                    map.put("orderStatus", order.getOrderStatus());
                    map.put("payStatus", order.getPayStatus());
                    map.put("createTime", order.getCreateTime());
                    map.put("startTime", order.getStartTime());
                    map.put("endTime", order.getEndTime());
                    map.put("shopId", order.getShopId());
                    
                    // 获取门店名称
                    if (order.getShopId() != null) {
                        try {
                            Shop shop = SpringUtils.getBean(ShopService.class).getById(order.getShopId());
                            if (shop != null) {
                                map.put("shopName", shop.getShopName());
                            }
                        } catch (Exception e) {
                            log.error("获取门店信息失败", e);
                        }
                    }
                    
                    return map;
                })
                .collect(Collectors.toList());
        
        // 构建返回结果
        Page<Map<String, Object>> resultPage = new Page<>(pageNum, pageSize, orderPage.getTotal());
        resultPage.setRecords(records);
        
        return resultPage;
    }

    @Override
    public Map<String, Object> getPartnerOrderStatistics(Long partnerId, Long shopId, String startTime, String endTime) {
        Map<String, Object> result = new HashMap<>();
        
        // 解析时间
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;
        
        if (StringUtils.hasText(startTime)) {
            startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        
        if (StringUtils.hasText(endTime)) {
            endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        
        // 统计订单总数
        LambdaQueryWrapper<Order> totalWrapper = new LambdaQueryWrapper<>();
        totalWrapper.eq(Order::getPartnerId, partnerId.intValue());
        
        if (shopId != null) {
            totalWrapper.eq(Order::getShopId, shopId.intValue());
        }
        
        if (startDateTime != null) {
            totalWrapper.ge(Order::getCreateTime, startDateTime);
        }
        
        if (endDateTime != null) {
            totalWrapper.le(Order::getCreateTime, endDateTime);
        }
        
        long totalCount = count(totalWrapper);
        result.put("totalCount", totalCount);
        
        // 统计已完成订单数
        LambdaQueryWrapper<Order> completedWrapper = new LambdaQueryWrapper<>();
        completedWrapper.eq(Order::getPartnerId, partnerId.intValue());
        if (shopId != null) {
            completedWrapper.eq(Order::getShopId, shopId.intValue());
        }
        completedWrapper.eq(Order::getOrderStatus, 2); // 已完成
        
        if (StringUtils.hasText(startTime)) {
            completedWrapper.ge(Order::getCreateTime, startDateTime);
        }
        
        if (StringUtils.hasText(endTime)) {
            completedWrapper.le(Order::getCreateTime, endDateTime);
        }
        
        long completedCount = count(completedWrapper);
        result.put("completedCount", completedCount);
        
        // 统计订单总金额
        BigDecimal totalAmount = lambdaQuery()
                .eq(Order::getPartnerId, partnerId.intValue())
                .eq(shopId != null, Order::getShopId, shopId != null ? shopId.intValue() : null)
                .eq(Order::getPayStatus, 1) // 已支付
                .ge(StringUtils.hasText(startTime), Order::getCreateTime, 
                    StringUtils.hasText(startTime) ? startDateTime : null)
                .le(StringUtils.hasText(endTime), Order::getCreateTime, 
                    StringUtils.hasText(endTime) ? endDateTime : null)
                .list()
                .stream()
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        result.put("totalAmount", totalAmount);
        
        // 统计今日订单数和金额
        LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        
        LambdaQueryWrapper<Order> todayWrapper = new LambdaQueryWrapper<>();
        todayWrapper.eq(Order::getPartnerId, partnerId.intValue());
        if (shopId != null) {
            todayWrapper.eq(Order::getShopId, shopId.intValue());
        }
        todayWrapper.between(Order::getCreateTime, todayStart, todayEnd);
        
        long todayCount = count(todayWrapper);
        
        LambdaQueryWrapper<Order> todayAmountWrapper = new LambdaQueryWrapper<>();
        todayAmountWrapper.eq(Order::getPartnerId, partnerId.intValue());
        if (shopId != null) {
            todayAmountWrapper.eq(Order::getShopId, shopId.intValue());
        }
        todayAmountWrapper.eq(Order::getPayStatus, 1); // 已支付
        todayAmountWrapper.between(Order::getCreateTime, todayStart, todayEnd);
        
        BigDecimal todayAmount = list(todayAmountWrapper)
                .stream()
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        result.put("todayCount", todayCount);
        result.put("todayAmount", todayAmount);
        
        return result;
    }

    @Override
    public BigDecimal getPartnerOrderAmount(Long partnerId) {
        if (partnerId == null) {
            return BigDecimal.ZERO;
        }
        
        // 查询合作商下所有已完成且已支付的订单
        return lambdaQuery()
                .eq(Order::getPartnerId, partnerId.intValue())
                .eq(Order::getOrderStatus, 2) // 已完成
                .eq(Order::getPayStatus, 1) // 已支付
                .list()
                .stream()
                .map(Order::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    @Override
    public Long countOrdersByEntityId(Long entityId) {
        if (entityId == null) {
            return 0L;
        }
        
        return lambdaQuery()
                .eq(Order::getEntityId, entityId.intValue())
                .count();
    }

    @Override
    public Map<String, Object> getAdminOrderStatistics(Long entityId, Long partnerId, Long shopId, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天开始
                startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天结束
                endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            }
            
            // 构建查询条件
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(Order::getCreateTime, startDateTime)
                        .le(Order::getCreateTime, endDateTime);
            
            // 根据不同级别筛选数据
            if (shopId != null) {
                queryWrapper.eq(Order::getShopId, shopId);
            } else if (partnerId != null) {
                queryWrapper.eq(Order::getPartnerId, partnerId);
            } else if (entityId != null) {
                queryWrapper.eq(Order::getEntityId, entityId);
            }
            
            // 查询订单总数
            long totalOrders = count(queryWrapper);
            statistics.put("totalOrders", totalOrders);
            
            // 查询各状态订单数量
            Map<Integer, Long> statusCounts = new HashMap<>();
            for (int status = 1; status <= 4; status++) {
                LambdaQueryWrapper<Order> statusQuery = new LambdaQueryWrapper<>();
                statusQuery.ge(Order::getCreateTime, startDateTime)
                        .le(Order::getCreateTime, endDateTime);
                
                // 根据不同级别筛选数据
                if (shopId != null) {
                    statusQuery.eq(Order::getShopId, shopId);
                } else if (partnerId != null) {
                    statusQuery.eq(Order::getPartnerId, partnerId);
                } else if (entityId != null) {
                    statusQuery.eq(Order::getEntityId, entityId);
                }
                
                statusQuery.eq(Order::getOrderStatus, status);
                long count = count(statusQuery);
                statusCounts.put(status, count);
            }
            statistics.put("statusCounts", statusCounts);
            
            // 查询总金额
            LambdaQueryWrapper<Order> paidQuery = new LambdaQueryWrapper<>();
            paidQuery.ge(Order::getCreateTime, startDateTime)
                    .le(Order::getCreateTime, endDateTime);
            
            // 根据不同级别筛选数据
            if (shopId != null) {
                paidQuery.eq(Order::getShopId, shopId);
            } else if (partnerId != null) {
                paidQuery.eq(Order::getPartnerId, partnerId);
            } else if (entityId != null) {
                paidQuery.eq(Order::getEntityId, entityId);
            }
            
            paidQuery.eq(Order::getPayStatus, 1);
            List<Order> paidOrders = list(paidQuery);
            
            BigDecimal totalAmount = paidOrders.stream()
                    .map(Order::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("totalAmount", totalAmount);
            
            // 查询退款金额
            LambdaQueryWrapper<Order> refundQuery = new LambdaQueryWrapper<>();
            refundQuery.ge(Order::getCreateTime, startDateTime)
                    .le(Order::getCreateTime, endDateTime);
            
            // 根据不同级别筛选数据
            if (shopId != null) {
                refundQuery.eq(Order::getShopId, shopId);
            } else if (partnerId != null) {
                refundQuery.eq(Order::getPartnerId, partnerId);
            } else if (entityId != null) {
                refundQuery.eq(Order::getEntityId, entityId);
            }
            
            refundQuery.eq(Order::getRefundStatus, 1);
            List<Order> refundedOrders = list(refundQuery);
            
            BigDecimal totalRefundAmount = refundedOrders.stream()
                    .map(Order::getRefundAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("totalRefundAmount", totalRefundAmount);
            
            // 计算净收入
            BigDecimal netIncome = totalAmount.subtract(totalRefundAmount);
            statistics.put("netIncome", netIncome);
            
            // 添加时间范围
            statistics.put("startTime", startDateTime.toString());
            statistics.put("endTime", endDateTime.toString());
            
        } catch (Exception e) {
            log.error("获取管理员订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public String exportAdminOrders(String orderNo, String deviceNo, Integer userId, Integer orderStatus, Integer payStatus, Integer entityId, Integer partnerId, Integer shopId, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            
            // 添加查询条件
            if (StringUtils.hasText(orderNo)) {
                queryWrapper.eq(Order::getOrderNo, orderNo);
            }
            
            if (StringUtils.hasText(deviceNo)) {
                queryWrapper.eq(Order::getDeviceNo, deviceNo);
            }
            
            if (userId != null) {
                queryWrapper.eq(Order::getUserId, userId);
            }
            
            if (orderStatus != null) {
                queryWrapper.eq(Order::getOrderStatus, orderStatus);
            }
            
            if (payStatus != null) {
                queryWrapper.eq(Order::getPayStatus, payStatus);
            }
            
            if (entityId != null) {
                queryWrapper.eq(Order::getEntityId, entityId);
            }
            
            if (partnerId != null) {
                queryWrapper.eq(Order::getPartnerId, partnerId);
            }
            
            if (shopId != null) {
                queryWrapper.eq(Order::getShopId, shopId);
            }
            
            if (startTime != null) {
                queryWrapper.ge(Order::getCreateTime, startTime);
            }
            
            if (endTime != null) {
                queryWrapper.le(Order::getCreateTime, endTime);
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(Order::getCreateTime);
            
            // 查询订单列表
            List<Order> orders = list(queryWrapper);
            
            // 生成导出文件
            String fileName = "orders_" + System.currentTimeMillis() + ".xlsx";
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = exportDir + "/" + fileName;
            
            // 创建工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // 创建工作表
                XSSFSheet sheet = workbook.createSheet("订单数据");
                
                // 创建表头行
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("订单编号");
                headerRow.createCell(1).setCellValue("设备编号");
                headerRow.createCell(2).setCellValue("订单金额");
                headerRow.createCell(3).setCellValue("订单状态");
                headerRow.createCell(4).setCellValue("支付状态");
                headerRow.createCell(5).setCellValue("支付方式");
                headerRow.createCell(6).setCellValue("创建时间");
                headerRow.createCell(7).setCellValue("支付时间");
                headerRow.createCell(8).setCellValue("完成时间");
                headerRow.createCell(9).setCellValue("业务主体ID");
                headerRow.createCell(10).setCellValue("合作商ID");
                headerRow.createCell(11).setCellValue("门店ID");
                headerRow.createCell(12).setCellValue("用户ID");
                headerRow.createCell(13).setCellValue("备注");
                
                // 填充数据行
                int rowNum = 1;
                for (Order order : orders) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(order.getOrderNo());
                    row.createCell(1).setCellValue(order.getDeviceNo() != null ? order.getDeviceNo() : "");
                    row.createCell(2).setCellValue(order.getAmount() != null ? order.getAmount().doubleValue() : 0);
                    row.createCell(3).setCellValue(getOrderStatusName(order.getOrderStatus()));
                    row.createCell(4).setCellValue(order.getPayStatus() != null && order.getPayStatus() == 1 ? "已支付" : "未支付");
                    row.createCell(5).setCellValue(getPayTypeName(order.getPayType()));
                    row.createCell(6).setCellValue(order.getCreateTime() != null ? order.getCreateTime().toString() : "");
                    row.createCell(7).setCellValue(order.getPayTime() != null ? order.getPayTime().toString() : "");
                    row.createCell(8).setCellValue(order.getEndTime() != null ? order.getEndTime().toString() : "");
                    row.createCell(9).setCellValue(order.getEntityId() != null ? order.getEntityId().toString() : "");
                    row.createCell(10).setCellValue(order.getPartnerId() != null ? order.getPartnerId().toString() : "");
                    row.createCell(11).setCellValue(order.getShopId() != null ? order.getShopId().toString() : "");
                    row.createCell(12).setCellValue(order.getUserId() != null ? order.getUserId().toString() : "");
                    row.createCell(13).setCellValue(order.getRemark() != null ? order.getRemark() : "");
                }
                
                // 调整列宽
                for (int i = 0; i < 14; i++) {
                    sheet.autoSizeColumn(i);
                }
                
                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }
            
            log.info("导出订单数据：共{}条记录，文件路径：{}", orders.size(), filePath);
            
            // 返回文件下载链接
            return "/api/download/" + fileName;
            
        } catch (Exception e) {
            log.error("导出管理员订单数据失败", e);
            throw new BusinessException("导出订单数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getEntityOrderStatistics(Long entityId, Long partnerId, Long shopId, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天开始
                startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天结束
                endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            }
            
            // 构建查询条件
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(Order::getCreateTime, startDateTime)
                        .le(Order::getCreateTime, endDateTime);
            
            // 根据不同级别筛选数据
            if (shopId != null) {
                queryWrapper.eq(Order::getShopId, shopId);
            } else if (partnerId != null) {
                queryWrapper.eq(Order::getPartnerId, partnerId);
            } else if (entityId != null) {
                queryWrapper.eq(Order::getEntityId, entityId);
            }
            
            // 查询订单总数
            long totalOrders = count(queryWrapper);
            statistics.put("totalOrders", totalOrders);
            
            // 查询各状态订单数量
            Map<Integer, Long> statusCounts = new HashMap<>();
            for (int status = 1; status <= 4; status++) {
                LambdaQueryWrapper<Order> statusQuery = new LambdaQueryWrapper<>();
                statusQuery.ge(Order::getCreateTime, startDateTime)
                        .le(Order::getCreateTime, endDateTime);
                
                // 根据不同级别筛选数据
                if (shopId != null) {
                    statusQuery.eq(Order::getShopId, shopId);
                } else if (partnerId != null) {
                    statusQuery.eq(Order::getPartnerId, partnerId);
                } else if (entityId != null) {
                    statusQuery.eq(Order::getEntityId, entityId);
                }
                
                statusQuery.eq(Order::getOrderStatus, status);
                long count = count(statusQuery);
                statusCounts.put(status, count);
            }
            statistics.put("statusCounts", statusCounts);
            
            // 查询总金额
            LambdaQueryWrapper<Order> paidQuery = new LambdaQueryWrapper<>();
            paidQuery.ge(Order::getCreateTime, startDateTime)
                    .le(Order::getCreateTime, endDateTime);
            
            // 根据不同级别筛选数据
            if (shopId != null) {
                paidQuery.eq(Order::getShopId, shopId);
            } else if (partnerId != null) {
                paidQuery.eq(Order::getPartnerId, partnerId);
            } else if (entityId != null) {
                paidQuery.eq(Order::getEntityId, entityId);
            }
            
            paidQuery.eq(Order::getPayStatus, 1);
            List<Order> paidOrders = list(paidQuery);
            
            BigDecimal totalAmount = paidOrders.stream()
                    .map(Order::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("totalAmount", totalAmount);
            
            // 查询退款金额
            LambdaQueryWrapper<Order> refundQuery = new LambdaQueryWrapper<>();
            refundQuery.ge(Order::getCreateTime, startDateTime)
                    .le(Order::getCreateTime, endDateTime);
            
            // 根据不同级别筛选数据
            if (shopId != null) {
                refundQuery.eq(Order::getShopId, shopId);
            } else if (partnerId != null) {
                refundQuery.eq(Order::getPartnerId, partnerId);
            } else if (entityId != null) {
                refundQuery.eq(Order::getEntityId, entityId);
            }
            
            refundQuery.eq(Order::getRefundStatus, 1);
            List<Order> refundedOrders = list(refundQuery);
            
            BigDecimal totalRefundAmount = refundedOrders.stream()
                    .map(Order::getRefundAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("totalRefundAmount", totalRefundAmount);
            
            // 计算净收入
            BigDecimal netIncome = totalAmount.subtract(totalRefundAmount);
            statistics.put("netIncome", netIncome);
            
            // 添加时间范围
            statistics.put("startTime", startDateTime.toString());
            statistics.put("endTime", endDateTime.toString());
            
        } catch (Exception e) {
            log.error("获取业务主体订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public Map<String, Object> getEntityPartnerOrderStatistics(Long entityId, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天开始
                startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天结束
                endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            }
            
            // 基本统计信息
            statistics.put("entityId", entityId);
            statistics.put("period", startTime + " 至 " + endTime);
            
            // 查询该业务主体下所有合作商的订单统计
            // 这里需要根据实际业务逻辑实现，可能需要连表查询或多次查询
            // 简化实现：直接查询订单表中的合作商ID分组统计
            List<Map<String, Object>> partnerStats = baseMapper.getEntityPartnerOrderStats(
                entityId, startDateTime, endDateTime);
            
            statistics.put("partnerStats", partnerStats);
            
        } catch (Exception e) {
            log.error("获取业务主体合作商订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }

    @Override
    public String exportEntityOrders(String orderNo, String deviceNo, Integer orderStatus, Integer payStatus, Integer entityId, Integer partnerId, Integer shopId, String startTime, String endTime) {
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 构建查询条件
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            
            // 添加查询条件
            if (StringUtils.hasText(orderNo)) {
                queryWrapper.eq(Order::getOrderNo, orderNo);
            }
            
            if (StringUtils.hasText(deviceNo)) {
                queryWrapper.eq(Order::getDeviceNo, deviceNo);
            }
            
            if (orderStatus != null) {
                queryWrapper.eq(Order::getOrderStatus, orderStatus);
            }
            
            if (payStatus != null) {
                queryWrapper.eq(Order::getPayStatus, payStatus);
            }
            
            if (entityId != null) {
                queryWrapper.eq(Order::getEntityId, entityId);
            }
            
            if (partnerId != null) {
                queryWrapper.eq(Order::getPartnerId, partnerId);
            }
            
            if (shopId != null) {
                queryWrapper.eq(Order::getShopId, shopId);
            }
            
            if (startDateTime != null) {
                queryWrapper.ge(Order::getCreateTime, startDateTime);
            }
            
            if (endDateTime != null) {
                queryWrapper.le(Order::getCreateTime, endDateTime);
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(Order::getCreateTime);
            
            // 查询订单列表
            List<Order> orders = list(queryWrapper);
            
            // 生成导出文件
            String fileName = "entity_orders_" + System.currentTimeMillis() + ".xlsx";
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = exportDir + "/" + fileName;
            
            // 创建工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // 创建工作表
                XSSFSheet sheet = workbook.createSheet("业务主体订单数据");
                
                // 创建表头行
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("订单编号");
                headerRow.createCell(1).setCellValue("设备编号");
                headerRow.createCell(2).setCellValue("订单金额");
                headerRow.createCell(3).setCellValue("订单状态");
                headerRow.createCell(4).setCellValue("支付状态");
                headerRow.createCell(5).setCellValue("支付方式");
                headerRow.createCell(6).setCellValue("创建时间");
                headerRow.createCell(7).setCellValue("支付时间");
                headerRow.createCell(8).setCellValue("完成时间");
                headerRow.createCell(9).setCellValue("业务主体ID");
                headerRow.createCell(10).setCellValue("合作商ID");
                headerRow.createCell(11).setCellValue("门店ID");
                headerRow.createCell(12).setCellValue("用户ID");
                headerRow.createCell(13).setCellValue("备注");
                
                // 填充数据行
                int rowNum = 1;
                for (Order order : orders) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(order.getOrderNo());
                    row.createCell(1).setCellValue(order.getDeviceNo() != null ? order.getDeviceNo() : "");
                    row.createCell(2).setCellValue(order.getAmount() != null ? order.getAmount().doubleValue() : 0);
                    row.createCell(3).setCellValue(getOrderStatusName(order.getOrderStatus()));
                    row.createCell(4).setCellValue(order.getPayStatus() != null && order.getPayStatus() == 1 ? "已支付" : "未支付");
                    row.createCell(5).setCellValue(getPayTypeName(order.getPayType()));
                    row.createCell(6).setCellValue(order.getCreateTime() != null ? order.getCreateTime().toString() : "");
                    row.createCell(7).setCellValue(order.getPayTime() != null ? order.getPayTime().toString() : "");
                    row.createCell(8).setCellValue(order.getEndTime() != null ? order.getEndTime().toString() : "");
                    row.createCell(9).setCellValue(order.getEntityId() != null ? order.getEntityId().toString() : "");
                    row.createCell(10).setCellValue(order.getPartnerId() != null ? order.getPartnerId().toString() : "");
                    row.createCell(11).setCellValue(order.getShopId() != null ? order.getShopId().toString() : "");
                    row.createCell(12).setCellValue(order.getUserId() != null ? order.getUserId().toString() : "");
                    row.createCell(13).setCellValue(order.getRemark() != null ? order.getRemark() : "");
                }
                
                // 调整列宽
                for (int i = 0; i < 14; i++) {
                    sheet.autoSizeColumn(i);
                }
                
                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }
            
            log.info("导出业务主体订单数据：共{}条记录，文件路径：{}", orders.size(), filePath);
            
            // 返回文件下载链接
            return "/api/download/" + fileName;
            
        } catch (Exception e) {
            log.error("导出业务主体订单数据失败", e);
            throw new BusinessException("导出订单数据失败: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getPartnerShopOrderStatistics(Long partnerId, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天开始
                startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天结束
                endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            }
            
            // 基本统计信息
            statistics.put("partnerId", partnerId);
            statistics.put("period", startTime + " 至 " + endTime);
            
            // 获取合作商下的所有门店
            List<Shop> shops = SpringUtils.getBean(ShopService.class)
                    .lambdaQuery()
                    .eq(Shop::getPartnerId, partnerId)
                    .eq(Shop::getStatus, 1) // 只统计启用状态的门店
                    .list();
            
            // 门店统计数据列表
            List<Map<String, Object>> shopStats = new ArrayList<>();
            
            // 遍历门店，获取每个门店的订单统计
            for (Shop shop : shops) {
                Map<String, Object> shopStat = new HashMap<>();
                shopStat.put("shopId", shop.getId());
                shopStat.put("shopName", shop.getShopName());
                
                // 构建查询条件
                LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Order::getShopId, shop.getId())
                            .ge(Order::getCreateTime, startDateTime)
                            .le(Order::getCreateTime, endDateTime);
                
                // 统计订单数量
                long orderCount = count(queryWrapper);
                shopStat.put("orderCount", orderCount);
                
                // 统计订单金额
                BigDecimal orderAmount = lambdaQuery()
                        .eq(Order::getShopId, shop.getId())
                        .eq(Order::getPayStatus, 1) // 已支付
                        .between(Order::getCreateTime, startDateTime, endDateTime)
                        .list()
                        .stream()
                        .map(Order::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                shopStat.put("orderAmount", orderAmount);
                
                shopStats.add(shopStat);
            }
            
            statistics.put("shopStats", shopStats);
            
        } catch (Exception e) {
            log.error("获取合作商门店订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getPartnerDailyOrderStatistics(Long partnerId, Long shopId, String startDate, String endDate) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDate start = LocalDate.parse(startDate.substring(0, 10));
            LocalDate end = LocalDate.parse(endDate.substring(0, 10));
            
            // 基本统计信息
            statistics.put("partnerId", partnerId);
            statistics.put("shopId", shopId);
            statistics.put("period", startDate + " 至 " + endDate);
            
            // 日期范围内的每日统计
            List<Map<String, Object>> dailyStats = new ArrayList<>();
            
            // 遍历日期范围
            LocalDate currentDate = start;
            while (!currentDate.isAfter(end)) {
                Map<String, Object> dailyStat = new HashMap<>();
                
                // 设置日期
                String dateStr = currentDate.toString();
                dailyStat.put("date", dateStr);
                
                // 当天开始和结束时间
                LocalDateTime dayStart = LocalDateTime.of(currentDate, LocalTime.MIN);
                LocalDateTime dayEnd = LocalDateTime.of(currentDate, LocalTime.MAX);
                
                // 构建查询条件
                LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Order::getPartnerId, partnerId);
                if (shopId != null) {
                    queryWrapper.eq(Order::getShopId, shopId);
                }
                queryWrapper.between(Order::getCreateTime, dayStart, dayEnd);
                
                // 统计订单数量
                long orderCount = count(queryWrapper);
                dailyStat.put("orderCount", orderCount);
                
                // 统计订单金额
                LambdaQueryWrapper<Order> amountWrapper = new LambdaQueryWrapper<>();
                amountWrapper.eq(Order::getPartnerId, partnerId);
                if (shopId != null) {
                    amountWrapper.eq(Order::getShopId, shopId);
                }
                amountWrapper.eq(Order::getPayStatus, 1) // 已支付
                        .between(Order::getCreateTime, dayStart, dayEnd);
                
                BigDecimal orderAmount = list(amountWrapper)
                        .stream()
                        .map(Order::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                dailyStat.put("orderAmount", orderAmount);
                
                // 统计各状态订单数量
                Map<Integer, Long> statusCounts = new HashMap<>();
                for (int status = 1; status <= 4; status++) {
                    LambdaQueryWrapper<Order> statusQuery = new LambdaQueryWrapper<>();
                    statusQuery.eq(Order::getPartnerId, partnerId);
                    if (shopId != null) {
                        statusQuery.eq(Order::getShopId, shopId);
                    }
                    statusQuery.eq(Order::getOrderStatus, status)
                               .between(Order::getCreateTime, dayStart, dayEnd);
                    
                    long count = count(statusQuery);
                    statusCounts.put(status, count);
                }
                
                dailyStat.put("statusCounts", statusCounts);
                
                dailyStats.add(dailyStat);
                
                // 移动到下一天
                currentDate = currentDate.plusDays(1);
            }
            
            statistics.put("dailyStats", dailyStats);
            
        } catch (Exception e) {
            log.error("获取合作商每日订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }
    
    @Override
    public String exportPartnerOrders(String orderNo, String deviceNo, Integer orderStatus, Integer payStatus, Integer partnerId, Integer shopId, String startTime, String endTime) {
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 构建查询条件
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            
            // 添加查询条件
            if (StringUtils.hasText(orderNo)) {
                queryWrapper.eq(Order::getOrderNo, orderNo);
            }
            
            if (StringUtils.hasText(deviceNo)) {
                queryWrapper.eq(Order::getDeviceNo, deviceNo);
            }
            
            if (orderStatus != null) {
                queryWrapper.eq(Order::getOrderStatus, orderStatus);
            }
            
            if (payStatus != null) {
                queryWrapper.eq(Order::getPayStatus, payStatus);
            }
            
            // 合作商必须指定
            queryWrapper.eq(Order::getPartnerId, partnerId);
            
            if (shopId != null) {
                queryWrapper.eq(Order::getShopId, shopId);
            }
            
            if (startDateTime != null) {
                queryWrapper.ge(Order::getCreateTime, startDateTime);
            }
            
            if (endDateTime != null) {
                queryWrapper.le(Order::getCreateTime, endDateTime);
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(Order::getCreateTime);
            
            // 查询订单列表
            List<Order> orders = list(queryWrapper);
            
            // 生成导出文件
            String fileName = "partner_orders_" + System.currentTimeMillis() + ".xlsx";
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = exportDir + "/" + fileName;
            
            // 创建工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // 创建工作表
                XSSFSheet sheet = workbook.createSheet("合作商订单数据");
                
                // 创建表头行
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("订单编号");
                headerRow.createCell(1).setCellValue("设备编号");
                headerRow.createCell(2).setCellValue("订单金额");
                headerRow.createCell(3).setCellValue("订单状态");
                headerRow.createCell(4).setCellValue("支付状态");
                headerRow.createCell(5).setCellValue("支付方式");
                headerRow.createCell(6).setCellValue("创建时间");
                headerRow.createCell(7).setCellValue("支付时间");
                headerRow.createCell(8).setCellValue("完成时间");
                headerRow.createCell(9).setCellValue("门店ID");
                headerRow.createCell(10).setCellValue("用户ID");
                headerRow.createCell(11).setCellValue("备注");
                
                // 填充数据行
                int rowNum = 1;
                for (Order order : orders) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(order.getOrderNo());
                    row.createCell(1).setCellValue(order.getDeviceNo() != null ? order.getDeviceNo() : "");
                    row.createCell(2).setCellValue(order.getAmount() != null ? order.getAmount().doubleValue() : 0);
                    row.createCell(3).setCellValue(getOrderStatusName(order.getOrderStatus()));
                    row.createCell(4).setCellValue(order.getPayStatus() != null && order.getPayStatus() == 1 ? "已支付" : "未支付");
                    row.createCell(5).setCellValue(getPayTypeName(order.getPayType()));
                    row.createCell(6).setCellValue(order.getCreateTime() != null ? order.getCreateTime().toString() : "");
                    row.createCell(7).setCellValue(order.getPayTime() != null ? order.getPayTime().toString() : "");
                    row.createCell(8).setCellValue(order.getEndTime() != null ? order.getEndTime().toString() : "");
                    row.createCell(9).setCellValue(order.getShopId() != null ? order.getShopId().toString() : "");
                    row.createCell(10).setCellValue(order.getUserId() != null ? order.getUserId().toString() : "");
                    row.createCell(11).setCellValue(order.getRemark() != null ? order.getRemark() : "");
                }
                
                // 调整列宽
                for (int i = 0; i < 12; i++) {
                    sheet.autoSizeColumn(i);
                }
                
                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }
            
            log.info("导出合作商订单数据：共{}条记录，文件路径：{}", orders.size(), filePath);
            
            // 返回文件下载链接
            return "/api/download/" + fileName;
            
        } catch (Exception e) {
            log.error("导出合作商订单数据失败", e);
            throw new BusinessException("导出订单数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取门店每日营业额统计数据
     *
     * @param shopId    门店ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getShopDailyRevenueStatistics(Long shopId, String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 解析日期
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            // 检查时间范围
            if (start.isAfter(end)) {
                throw new BusinessException("开始日期不能晚于结束日期");
            }
            
            // 存储每日营业额数据
            List<Map<String, Object>> dailyData = new ArrayList<>();
            Map<String, Object> summary = new HashMap<>();
            BigDecimal totalAmount = BigDecimal.ZERO;
            long totalCount = 0;
            
            // 遍历每一天
            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                LocalDateTime dayStart = date.atStartOfDay();
                LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
                
                // 查询当天的订单数据
                LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Order::getShopId, shopId)
                        .eq(Order::getPayStatus, 1) // 已支付
                        .ge(Order::getCreateTime, dayStart)
                        .lt(Order::getCreateTime, dayEnd);
                
                // 获取订单列表
                List<Order> orders = list(queryWrapper);
                
                // 计算订单总金额和数量
                BigDecimal dayAmount = orders.stream()
                        .map(Order::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                int dayCount = orders.size();
                
                // 累加到总计
                totalAmount = totalAmount.add(dayAmount);
                totalCount += dayCount;
                
                // 构建每日数据
                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", date.toString());
                dayData.put("amount", dayAmount);
                dayData.put("count", dayCount);
                dayData.put("avgAmount", dayCount > 0 ? dayAmount.divide(new BigDecimal(dayCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                
                dailyData.add(dayData);
            }
            
            // 计算总结数据
            summary.put("totalAmount", totalAmount);
            summary.put("totalCount", totalCount);
            summary.put("avgDailyAmount", totalAmount.divide(new BigDecimal(ChronoUnit.DAYS.between(start, end) + 1), 2, RoundingMode.HALF_UP));
            summary.put("avgOrderAmount", totalCount > 0 ? totalAmount.divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            
            // 构建结果
            result.put("dailyData", dailyData);
            result.put("summary", summary);
            
        } catch (DateTimeParseException e) {
            log.error("解析日期失败", e);
            throw new BusinessException("日期格式不正确，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("获取门店日营业额统计失败", e);
            throw new BusinessException("获取门店日营业额统计失败: " + e.getMessage());
        }
        
        return result;
    }

    @Override
    public Map<String, Object> getShopMonthlyRevenueStatistics(Long shopId, Integer year, Integer month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            LocalDate startDate;
            LocalDate endDate;
            
            if (month != null) {
                // 查询特定月份
                startDate = LocalDate.of(year, month, 1);
                endDate = startDate.plusMonths(1).minusDays(1);
                
                // 每日统计
                List<Map<String, Object>> dailyStats = getDailyRevenueData(shopId, startDate, endDate);
                result.put("dailyStats", dailyStats);
            } else {
                // 查询整年
                startDate = LocalDate.of(year, 1, 1);
                endDate = LocalDate.of(year, 12, 31);
                
                // 每月统计
                List<Map<String, Object>> monthlyStats = new ArrayList<>();
                
                for (int m = 1; m <= 12; m++) {
                    LocalDate monthStart = LocalDate.of(year, m, 1);
                    LocalDate monthEnd = monthStart.plusMonths(1).minusDays(1);
                    
                    // 查询当月订单数据
                    LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(Order::getShopId, shopId)
                            .eq(Order::getPayStatus, 1) // 已支付
                            .ge(Order::getCreateTime, monthStart.atStartOfDay())
                            .lt(Order::getCreateTime, monthEnd.plusDays(1).atStartOfDay());
                    
                    List<Order> orders = list(queryWrapper);
                    
                    // 计算统计数据
                    BigDecimal monthlyAmount = orders.stream()
                            .map(Order::getAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
                    int orderCount = orders.size();
                    
                    // 构建月度数据
                    Map<String, Object> monthData = new HashMap<>();
                    monthData.put("month", m);
                    monthData.put("amount", monthlyAmount);
                    monthData.put("count", orderCount);
                    monthData.put("avgAmount", orderCount > 0 ? monthlyAmount.divide(new BigDecimal(orderCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                    
                    monthlyStats.add(monthData);
                }
                
                result.put("monthlyStats", monthlyStats);
            }
            
            // 查询整体统计数据
            LambdaQueryWrapper<Order> totalQueryWrapper = new LambdaQueryWrapper<>();
            totalQueryWrapper.eq(Order::getShopId, shopId)
                    .eq(Order::getPayStatus, 1) // 已支付
                    .ge(Order::getCreateTime, startDate.atStartOfDay())
                    .lt(Order::getCreateTime, endDate.plusDays(1).atStartOfDay());
            
            List<Order> orders = list(totalQueryWrapper);
            
            BigDecimal totalAmount = orders.stream()
                    .map(Order::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            int totalCount = orders.size();
            
            // 生成统计摘要
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalAmount", totalAmount);
            summary.put("totalCount", totalCount);
            summary.put("startDate", startDate);
            summary.put("endDate", endDate);
            summary.put("avgOrderAmount", totalCount > 0 ? totalAmount.divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            
            result.put("summary", summary);
            
        } catch (Exception e) {
            log.error("获取门店月营业额统计失败", e);
            throw new BusinessException("获取门店月营业额统计失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> getShopDeviceRevenueStatistics(Long shopId, String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 解析日期
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            LocalDateTime startDateTime = start.atStartOfDay();
            LocalDateTime endDateTime = end.plusDays(1).atStartOfDay();
            
            // 获取门店下的所有已绑定设备
            List<Device> devices = deviceService.lambdaQuery()
                    .eq(Device::getShopId, shopId)
                    .eq(Device::getIsBound, 1) // 只查询已绑定设备
                    .list();
            
            if (devices.isEmpty()) {
                result.put("deviceStats", Collections.emptyList());
                result.put("summary", Map.of(
                    "totalAmount", BigDecimal.ZERO,
                    "totalCount", 0
                ));
                return result;
            }
            
            // 设备营收统计
            List<Map<String, Object>> deviceStats = new ArrayList<>();
            BigDecimal totalAmount = BigDecimal.ZERO;
            long totalCount = 0;
            
            // 遍历每个设备
            for (Device device : devices) {
                // 查询设备订单数据
                LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Order::getDeviceId, device.getId())
                        .eq(Order::getShopId, shopId)
                        .eq(Order::getPayStatus, 1) // 已支付
                        .ge(Order::getCreateTime, startDateTime)
                        .lt(Order::getCreateTime, endDateTime);
                
                List<Order> orders = list(queryWrapper);
                
                // 计算设备营收
                BigDecimal deviceAmount = orders.stream()
                        .map(Order::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                int deviceCount = orders.size();
                
                // 累加到总计
                totalAmount = totalAmount.add(deviceAmount);
                totalCount += deviceCount;
                
                // 构建设备数据
                Map<String, Object> deviceStat = new HashMap<>();
                deviceStat.put("deviceId", device.getId());
                deviceStat.put("deviceNo", device.getDeviceNo());
                deviceStat.put("deviceName", device.getDeviceName());
                deviceStat.put("amount", deviceAmount);
                deviceStat.put("count", deviceCount);
                deviceStat.put("avgAmount", deviceCount > 0 ? deviceAmount.divide(new BigDecimal(deviceCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
                
                deviceStats.add(deviceStat);
            }
            
            // 按营收降序排序
            deviceStats.sort((a, b) -> ((BigDecimal) b.get("amount")).compareTo((BigDecimal) a.get("amount")));
            
            // 生成统计摘要
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalAmount", totalAmount);
            summary.put("totalCount", totalCount);
            summary.put("deviceCount", devices.size());
            summary.put("avgDeviceAmount", devices.size() > 0 ? totalAmount.divide(new BigDecimal(devices.size()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            summary.put("avgOrderAmount", totalCount > 0 ? totalAmount.divide(new BigDecimal(totalCount), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            
            result.put("deviceStats", deviceStats);
            result.put("summary", summary);
            
        } catch (DateTimeParseException e) {
            log.error("解析日期失败", e);
            throw new BusinessException("日期格式不正确，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("获取门店设备营收统计失败", e);
            throw new BusinessException("获取门店设备营收统计失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public Map<String, Object> getShopIncomeAnalysis(Long shopId, String startDate, String endDate) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 解析日期
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            LocalDateTime startDateTime = start.atStartOfDay();
            LocalDateTime endDateTime = end.plusDays(1).atStartOfDay();
            
            // 查询时间范围内的订单
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getShopId, shopId)
                    .eq(Order::getPayStatus, 1) // 已支付
                    .ge(Order::getCreateTime, startDateTime)
                    .lt(Order::getCreateTime, endDateTime);
            
            List<Order> orders = list(queryWrapper);
            
            // 统计总收入
            BigDecimal totalAmount = orders.stream()
                    .map(Order::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 按日期分组统计
            Map<LocalDate, List<Order>> ordersByDate = orders.stream()
                    .collect(Collectors.groupingBy(order -> 
                        order.getCreateTime().toLocalDate()));
            
            // 日统计数据
            List<Map<String, Object>> dailyStats = new ArrayList<>();
            
            // 遍历日期范围
            for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                List<Order> dayOrders = ordersByDate.getOrDefault(date, Collections.emptyList());
                
                BigDecimal dayAmount = dayOrders.stream()
                        .map(Order::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                Map<String, Object> dayStat = new HashMap<>();
                dayStat.put("date", date.toString());
                dayStat.put("amount", dayAmount);
                dayStat.put("count", dayOrders.size());
                
                dailyStats.add(dayStat);
            }
            
            // 按时段分组统计
            Map<Integer, BigDecimal> hourlyStats = new HashMap<>();
            for (int hour = 0; hour < 24; hour++) {
                hourlyStats.put(hour, BigDecimal.ZERO);
            }
            
            // 计算每个小时的收入
            for (Order order : orders) {
                int hour = order.getCreateTime().getHour();
                BigDecimal amount = order.getAmount() != null ? order.getAmount() : BigDecimal.ZERO;
                hourlyStats.put(hour, hourlyStats.get(hour).add(amount));
            }
            
            // 转换为列表格式
            List<Map<String, Object>> hourlyStatsList = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Object> hourStat = new HashMap<>();
                hourStat.put("hour", hour);
                hourStat.put("amount", hourlyStats.get(hour));
                hourlyStatsList.add(hourStat);
            }
            
            // 构建结果
            result.put("totalAmount", totalAmount);
            result.put("totalCount", orders.size());
            result.put("dailyStats", dailyStats);
            result.put("hourlyStats", hourlyStatsList);
            result.put("startDate", startDate);
            result.put("endDate", endDate);
            
        } catch (DateTimeParseException e) {
            log.error("解析日期失败", e);
            throw new BusinessException("日期格式不正确，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("获取门店收入分析失败", e);
            throw new BusinessException("获取门店收入分析失败: " + e.getMessage());
        }
        
        return result;
    }
    
    @Override
    public String exportShopStatisticsReport(Long shopId, String reportType, String startDate, String endDate) {
        try {
            // 解析日期
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);
            
            LocalDateTime startDateTime = start.atStartOfDay();
            LocalDateTime endDateTime = end.plusDays(1).atStartOfDay();
            
            // 根据报表类型获取相应的数据
            Map<String, Object> reportData = new HashMap<>();
            String sheetName = "";
            
            switch (reportType) {
                case "daily":
                    reportData = getShopDailyRevenueStatistics(shopId, startDate, endDate);
                    sheetName = "日营业额统计";
                    break;
                case "monthly":
                    // 获取年份和月份
                    Year year = Year.from(start);
                    Month month = null;
                    if (start.getMonth() == end.getMonth() && start.getYear() == end.getYear()) {
                        month = start.getMonth();
                    }
                    reportData = getShopMonthlyRevenueStatistics(shopId, year.getValue(), month != null ? month.getValue() : null);
                    sheetName = "月营业额统计";
                    break;
                case "yearly":
                    reportData = getShopMonthlyRevenueStatistics(shopId, start.getYear(), null);
                    sheetName = "年营业额统计";
                    break;
                default:
                    throw new BusinessException("不支持的报表类型: " + reportType);
            }
            
            // 生成报表文件名
            String fileName = "shop_" + shopId + "_" + reportType + "_" + start.toString() + "_to_" + end.toString() + "_" + System.currentTimeMillis() + ".xlsx";
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = exportDir + "/" + fileName;
            
            // 创建工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // 创建工作表
                XSSFSheet sheet = workbook.createSheet(sheetName);
                
                // 创建表头
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("门店ID");
                headerRow.createCell(1).setCellValue(shopId);
                
                Row periodRow = sheet.createRow(1);
                periodRow.createCell(0).setCellValue("统计周期");
                periodRow.createCell(1).setCellValue(startDate + " 至 " + endDate);
                
                // 根据报表类型创建不同的内容
                int rowIndex = 3;
                
                if ("daily".equals(reportType)) {
                    // 日报表表头
                    Row dailyHeaderRow = sheet.createRow(rowIndex++);
                    dailyHeaderRow.createCell(0).setCellValue("日期");
                    dailyHeaderRow.createCell(1).setCellValue("订单数量");
                    dailyHeaderRow.createCell(2).setCellValue("订单金额");
                    dailyHeaderRow.createCell(3).setCellValue("平均订单金额");
                    
                    // 填充日报表数据
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> dailyData = (List<Map<String, Object>>) reportData.get("dailyData");
                    if (dailyData != null) {
                        for (Map<String, Object> dayData : dailyData) {
                            Row dataRow = sheet.createRow(rowIndex++);
                            dataRow.createCell(0).setCellValue(String.valueOf(dayData.get("date")));
                            dataRow.createCell(1).setCellValue(((Number) dayData.get("count")).intValue());
                            dataRow.createCell(2).setCellValue(((BigDecimal) dayData.get("amount")).doubleValue());
                            dataRow.createCell(3).setCellValue(((BigDecimal) dayData.get("avgAmount")).doubleValue());
                        }
                    }
                    
                    // 添加汇总信息
                    @SuppressWarnings("unchecked")
                    Map<String, Object> summary = (Map<String, Object>) reportData.get("summary");
                    if (summary != null) {
                        rowIndex += 2;
                        Row summaryHeaderRow = sheet.createRow(rowIndex++);
                        summaryHeaderRow.createCell(0).setCellValue("统计汇总");
                        
                        Row totalAmountRow = sheet.createRow(rowIndex++);
                        totalAmountRow.createCell(0).setCellValue("总营业额");
                        totalAmountRow.createCell(1).setCellValue(((BigDecimal) summary.get("totalAmount")).doubleValue());
                        
                        Row totalCountRow = sheet.createRow(rowIndex++);
                        totalCountRow.createCell(0).setCellValue("总订单数");
                        totalCountRow.createCell(1).setCellValue(((Number) summary.get("totalCount")).intValue());
                        
                        Row avgDailyRow = sheet.createRow(rowIndex++);
                        avgDailyRow.createCell(0).setCellValue("日均营业额");
                        avgDailyRow.createCell(1).setCellValue(((BigDecimal) summary.get("avgDailyAmount")).doubleValue());
                        
                        Row avgOrderRow = sheet.createRow(rowIndex++);
                        avgOrderRow.createCell(0).setCellValue("平均订单金额");
                        avgOrderRow.createCell(1).setCellValue(((BigDecimal) summary.get("avgOrderAmount")).doubleValue());
                    }
                } else if ("monthly".equals(reportType) || "yearly".equals(reportType)) {
                    // 月报表或年报表表头
                    Row monthlyHeaderRow = sheet.createRow(rowIndex++);
                    if ("monthly".equals(reportType)) {
                        monthlyHeaderRow.createCell(0).setCellValue("日期");
                    } else {
                        monthlyHeaderRow.createCell(0).setCellValue("月份");
                    }
                    monthlyHeaderRow.createCell(1).setCellValue("订单数量");
                    monthlyHeaderRow.createCell(2).setCellValue("订单金额");
                    monthlyHeaderRow.createCell(3).setCellValue("平均订单金额");
                    
                    // 填充月报表或年报表数据
                    String dataKey = "monthly".equals(reportType) ? "dailyStats" : "monthlyStats";
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> statsData = (List<Map<String, Object>>) reportData.get(dataKey);
                    if (statsData != null) {
                        for (Map<String, Object> stat : statsData) {
                            Row dataRow = sheet.createRow(rowIndex++);
                            if ("monthly".equals(reportType)) {
                                dataRow.createCell(0).setCellValue(String.valueOf(stat.get("date")));
                            } else {
                                dataRow.createCell(0).setCellValue(((Number) stat.get("month")).intValue() + "月");
                            }
                            dataRow.createCell(1).setCellValue(((Number) stat.get("count")).intValue());
                            dataRow.createCell(2).setCellValue(((BigDecimal) stat.get("amount")).doubleValue());
                            dataRow.createCell(3).setCellValue(((BigDecimal) stat.get("avgAmount")).doubleValue());
                        }
                    }
                    
                    // 添加汇总信息
                    @SuppressWarnings("unchecked")
                    Map<String, Object> summary = (Map<String, Object>) reportData.get("summary");
                    if (summary != null) {
                        rowIndex += 2;
                        Row summaryHeaderRow = sheet.createRow(rowIndex++);
                        summaryHeaderRow.createCell(0).setCellValue("统计汇总");
                        
                        Row totalAmountRow = sheet.createRow(rowIndex++);
                        totalAmountRow.createCell(0).setCellValue("总营业额");
                        totalAmountRow.createCell(1).setCellValue(((BigDecimal) summary.get("totalAmount")).doubleValue());
                        
                        Row totalCountRow = sheet.createRow(rowIndex++);
                        totalCountRow.createCell(0).setCellValue("总订单数");
                        totalCountRow.createCell(1).setCellValue(((Number) summary.get("totalCount")).intValue());
                        
                        Row avgOrderRow = sheet.createRow(rowIndex++);
                        avgOrderRow.createCell(0).setCellValue("平均订单金额");
                        avgOrderRow.createCell(1).setCellValue(((BigDecimal) summary.get("avgOrderAmount")).doubleValue());
                    }
                }
                
                // 调整列宽
                for (int i = 0; i < 4; i++) {
                    sheet.autoSizeColumn(i);
                }
                
                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }
            
            log.info("导出门店统计报表：类型={}，时间范围={}~{}，文件路径={}", reportType, startDate, endDate, filePath);
            
            // 返回文件下载链接
            return "/api/download/" + fileName;
            
        } catch (DateTimeParseException e) {
            log.error("解析日期失败", e);
            throw new BusinessException("日期格式不正确，请使用yyyy-MM-dd格式");
        } catch (Exception e) {
            log.error("导出门店统计报表失败", e);
            throw new BusinessException("导出门店统计报表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取特定日期范围内的每日营收数据
     */
    private List<Map<String, Object>> getDailyRevenueData(Long shopId, LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> result = new ArrayList<>();
        
        // 遍历每一天
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            LocalDateTime dayStart = date.atStartOfDay();
            LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();
            
            // 查询当天的订单
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Order::getShopId, shopId)
                    .eq(Order::getPayStatus, 1) // 已支付
                    .ge(Order::getCreateTime, dayStart)
                    .lt(Order::getCreateTime, dayEnd);
            
            List<Order> orders = list(queryWrapper);
            
            // 计算当天收入
            BigDecimal dayAmount = orders.stream()
                    .map(Order::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            // 构建当天数据
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", date.toString());
            dayData.put("amount", dayAmount);
            dayData.put("count", orders.size());
            
            result.add(dayData);
        }
        
        return result;
    }

    /**
     * 获取门店每日订单统计信息
     *
     * @param shopId    门店ID
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 每日订单统计数据
     */
    @Override
    public Map<String, Object> getShopDailyOrderStatistics(Long shopId, String startDate, String endDate) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDate start = LocalDate.parse(startDate.substring(0, 10));
            LocalDate end = LocalDate.parse(endDate.substring(0, 10));
            
            // 基本统计信息
            statistics.put("shopId", shopId);
            statistics.put("period", startDate + " 至 " + endDate);
            
            // 日期范围内的每日统计
            List<Map<String, Object>> dailyStats = new ArrayList<>();
            
            // 遍历日期范围
            LocalDate currentDate = start;
            while (!currentDate.isAfter(end)) {
                Map<String, Object> dailyStat = new HashMap<>();
                
                // 设置日期
                String dateStr = currentDate.toString();
                dailyStat.put("date", dateStr);
                
                // 当天开始和结束时间
                LocalDateTime dayStart = LocalDateTime.of(currentDate, LocalTime.MIN);
                LocalDateTime dayEnd = LocalDateTime.of(currentDate, LocalTime.MAX);
                
                // 构建查询条件
                LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Order::getShopId, shopId)
                           .between(Order::getCreateTime, dayStart, dayEnd);
                
                // 统计订单数量
                long orderCount = count(queryWrapper);
                dailyStat.put("orderCount", orderCount);
                
                // 统计订单金额
                LambdaQueryWrapper<Order> amountWrapper = new LambdaQueryWrapper<>();
                amountWrapper.eq(Order::getShopId, shopId)
                        .eq(Order::getPayStatus, 1) // 已支付
                        .between(Order::getCreateTime, dayStart, dayEnd);
                
                BigDecimal orderAmount = list(amountWrapper)
                        .stream()
                        .map(Order::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                dailyStat.put("orderAmount", orderAmount);
                
                // 统计各状态订单数量
                Map<Integer, Long> statusCounts = new HashMap<>();
                for (int status = 1; status <= 4; status++) {
                    LambdaQueryWrapper<Order> statusQuery = new LambdaQueryWrapper<>();
                    statusQuery.eq(Order::getShopId, shopId)
                               .eq(Order::getOrderStatus, status)
                               .between(Order::getCreateTime, dayStart, dayEnd);
                    
                    long count = count(statusQuery);
                    statusCounts.put(status, count);
                }
                
                dailyStat.put("statusCounts", statusCounts);
                
                dailyStats.add(dailyStat);
                
                // 移动到下一天
                currentDate = currentDate.plusDays(1);
            }
            
            statistics.put("dailyStats", dailyStats);
            
        } catch (Exception e) {
            log.error("获取门店每日订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }

    /**
     * 获取门店设备订单统计信息
     *
     * @param shopId    门店ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 各设备订单统计列表
     */
    @Override
    public Map<String, Object> getShopDeviceOrderStatistics(Long shopId, String startTime, String endTime) {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天开始
                startDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } else {
                // 默认为今天结束
                endDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
            }
            
            // 基本统计信息
            statistics.put("shopId", shopId);
            statistics.put("period", startTime + " 至 " + endTime);
            
            // 查询门店下所有已绑定设备
            List<Device> devices = SpringUtils.getBean(DeviceService.class)
                    .lambdaQuery()
                    .eq(Device::getShopId, shopId)
                    .eq(Device::getIsBound, 1) // 只统计已绑定设备
                    .eq(Device::getStatus, 1) // 只统计启用状态的设备
                    .list();
            
            // 设备统计数据列表
            List<Map<String, Object>> deviceStats = new ArrayList<>();
            
            // 遍历设备，获取每个设备的订单统计
            for (Device device : devices) {
                Map<String, Object> deviceStat = new HashMap<>();
                deviceStat.put("deviceId", device.getId());
                deviceStat.put("deviceNo", device.getDeviceNo());
                deviceStat.put("deviceName", device.getDeviceName());
                
                // 构建查询条件
                LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Order::getDeviceId, device.getId())
                           .ge(Order::getCreateTime, startDateTime)
                           .le(Order::getCreateTime, endDateTime);
                
                // 统计订单数量
                long orderCount = count(queryWrapper);
                deviceStat.put("orderCount", orderCount);
                
                // 统计订单金额
                LambdaQueryWrapper<Order> amountWrapper = new LambdaQueryWrapper<>();
                amountWrapper.eq(Order::getDeviceId, device.getId())
                        .eq(Order::getPayStatus, 1) // 已支付
                        .between(Order::getCreateTime, startDateTime, endDateTime);
                
                BigDecimal orderAmount = list(amountWrapper)
                        .stream()
                        .map(Order::getAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                
                deviceStat.put("orderAmount", orderAmount);
                
                deviceStats.add(deviceStat);
            }
            
            statistics.put("deviceStats", deviceStats);
            
        } catch (Exception e) {
            log.error("获取门店设备订单统计数据失败", e);
            statistics.put("error", "获取统计数据失败: " + e.getMessage());
        }
        
        return statistics;
    }

    /**
     * 导出门店订单数据
     *
     * @param orderNo     订单编号
     * @param deviceNo    设备编号
     * @param orderStatus 订单状态
     * @param payStatus   支付状态
     * @param shopId      门店ID
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 导出文件下载链接
     */
    @Override
    public String exportShopOrders(String orderNo, String deviceNo, Integer orderStatus, Integer payStatus, Integer shopId, String startTime, String endTime) {
        try {
            // 解析时间
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            
            if (StringUtils.hasText(startTime)) {
                startDateTime = LocalDateTime.parse(startTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            if (StringUtils.hasText(endTime)) {
                endDateTime = LocalDateTime.parse(endTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            
            // 构建查询条件
            LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
            
            // 添加查询条件
            if (StringUtils.hasText(orderNo)) {
                queryWrapper.eq(Order::getOrderNo, orderNo);
            }
            
            if (StringUtils.hasText(deviceNo)) {
                queryWrapper.eq(Order::getDeviceNo, deviceNo);
            }
            
            if (orderStatus != null) {
                queryWrapper.eq(Order::getOrderStatus, orderStatus);
            }
            
            if (payStatus != null) {
                queryWrapper.eq(Order::getPayStatus, payStatus);
            }
            
            // 门店必须指定
            queryWrapper.eq(Order::getShopId, shopId);
            
            if (startDateTime != null) {
                queryWrapper.ge(Order::getCreateTime, startDateTime);
            }
            
            if (endDateTime != null) {
                queryWrapper.le(Order::getCreateTime, endDateTime);
            }
            
            // 按创建时间降序排序
            queryWrapper.orderByDesc(Order::getCreateTime);
            
            // 查询订单列表
            List<Order> orders = list(queryWrapper);
            
            // 生成导出文件
            String fileName = "shop_orders_" + System.currentTimeMillis() + ".xlsx";
            String exportDir = "./export";
            File dir = new File(exportDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            String filePath = exportDir + "/" + fileName;
            
            // 创建工作簿
            try (XSSFWorkbook workbook = new XSSFWorkbook()) {
                // 创建工作表
                XSSFSheet sheet = workbook.createSheet("门店订单数据");
                
                // 创建表头行
                Row headerRow = sheet.createRow(0);
                headerRow.createCell(0).setCellValue("订单编号");
                headerRow.createCell(1).setCellValue("设备编号");
                headerRow.createCell(2).setCellValue("订单金额");
                headerRow.createCell(3).setCellValue("订单状态");
                headerRow.createCell(4).setCellValue("支付状态");
                headerRow.createCell(5).setCellValue("支付方式");
                headerRow.createCell(6).setCellValue("创建时间");
                headerRow.createCell(7).setCellValue("支付时间");
                headerRow.createCell(8).setCellValue("完成时间");
                headerRow.createCell(9).setCellValue("用户ID");
                headerRow.createCell(10).setCellValue("备注");
                
                // 填充数据行
                int rowNum = 1;
                for (Order order : orders) {
                    Row row = sheet.createRow(rowNum++);
                    row.createCell(0).setCellValue(order.getOrderNo());
                    row.createCell(1).setCellValue(order.getDeviceNo() != null ? order.getDeviceNo() : "");
                    row.createCell(2).setCellValue(order.getAmount() != null ? order.getAmount().doubleValue() : 0);
                    row.createCell(3).setCellValue(getOrderStatusName(order.getOrderStatus()));
                    row.createCell(4).setCellValue(order.getPayStatus() != null && order.getPayStatus() == 1 ? "已支付" : "未支付");
                    row.createCell(5).setCellValue(getPayTypeName(order.getPayType()));
                    row.createCell(6).setCellValue(order.getCreateTime() != null ? order.getCreateTime().toString() : "");
                    row.createCell(7).setCellValue(order.getPayTime() != null ? order.getPayTime().toString() : "");
                    row.createCell(8).setCellValue(order.getEndTime() != null ? order.getEndTime().toString() : "");
                    row.createCell(9).setCellValue(order.getUserId() != null ? order.getUserId().toString() : "");
                    row.createCell(10).setCellValue(order.getRemark() != null ? order.getRemark() : "");
                }
                
                // 调整列宽
                for (int i = 0; i < 11; i++) {
                    sheet.autoSizeColumn(i);
                }
                
                // 写入文件
                try (FileOutputStream outputStream = new FileOutputStream(filePath)) {
                    workbook.write(outputStream);
                }
            }
            
            log.info("导出门店订单数据：共{}条记录，文件路径：{}", orders.size(), filePath);
            
            // 返回文件下载链接
            return "/api/download/" + fileName;
            
        } catch (Exception e) {
            log.error("导出门店订单数据失败", e);
            throw new BusinessException("导出订单数据失败: " + e.getMessage());
        }
    }

    /**
     * 统计指定时间范围内的订单数量
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单数量
     */
    @Override
    public Long countOrdersByTimeRange(LocalDateTime startTime, LocalDateTime endTime) {
        return lambdaQuery()
                .ge(startTime != null, Order::getCreateTime, startTime)
                .le(endTime != null, Order::getCreateTime, endTime)
                .count();
    }

    /**
     * 统计指定状态的订单数量
     *
     * @param status 订单状态
     * @return 订单数量
     */
    @Override
    public Long countOrdersByStatus(int status) {
        return lambdaQuery()
                .eq(Order::getOrderStatus, status)
                .count();
    }

    /**
     * 获取支付方式分布统计数据
     *
     * @return 支付方式分布数据
     */
    @Override
    public Map<String, Object> getPaymentMethodDistribution() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 查询已支付订单的支付方式分布
            List<Map<String, Object>> distribution = baseMapper.countGroupByPayType();
            
            // 计算总数
            long total = 0;
            for (Map<String, Object> item : distribution) {
                total += ((Number) item.getOrDefault("count", 0)).longValue();
            }
            
            // 转换为比例
            List<Map<String, Object>> ratioList = new ArrayList<>();
            for (Map<String, Object> item : distribution) {
                Map<String, Object> ratioItem = new HashMap<>();
                String payType = (String) item.get("payType");
                long count = ((Number) item.getOrDefault("count", 0)).longValue();
                
                ratioItem.put("payType", payType);
                ratioItem.put("count", count);
                ratioItem.put("ratio", total > 0 ? (double) count / total : 0);
                
                // 添加支付方式名称
                String payTypeName = getPayTypeName(payType);
                ratioItem.put("payTypeName", payTypeName);
                
                ratioList.add(ratioItem);
            }
            
            result.put("total", total);
            result.put("distribution", ratioList);
            
        } catch (Exception e) {
            log.error("获取支付方式分布数据失败", e);
            result.put("error", "获取支付方式分布数据失败");
        }
        
        return result;
    }
    
    /**
     * 获取支付方式名称
     *
     * @param payType 支付方式代码
     * @return 支付方式名称
     */
    private String getPayTypeName(String payType) {
        switch (payType) {
            case "alipay":
                return "支付宝";
            case "wechat":
                return "微信支付";
            case "unionpay":
                return "银联支付";
            case "balance":
                return "余额支付";
            default:
                return payType;
        }
    }

    /**
     * 获取订单时间分布统计数据
     *
     * @return 订单时间分布数据
     */
    @Override
    public Map<String, Object> getOrderTimeDistribution() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取每小时订单分布
            List<Map<String, Object>> hourDistribution = baseMapper.countGroupByHour();
            result.put("hourDistribution", hourDistribution);
            
            // 获取每天订单分布
            List<Map<String, Object>> dayDistribution = baseMapper.countGroupByDay();
            result.put("dayDistribution", dayDistribution);
            
            // 获取每周订单分布
            List<Map<String, Object>> weekDistribution = baseMapper.countGroupByWeekday();
            result.put("weekDistribution", weekDistribution);
            
            // 获取每月订单分布
            List<Map<String, Object>> monthDistribution = baseMapper.countGroupByMonth();
            result.put("monthDistribution", monthDistribution);
            
        } catch (Exception e) {
            log.error("获取订单时间分布数据失败", e);
            result.put("error", "获取订单时间分布数据失败");
        }
        
        return result;
    }

    @Override
    public OrderVO getOrderById(Long id) {
        if (id == null) {
            return null;
        }
        
        Order order = getById(id);
        if (order == null) {
            return null;
        }
        
        // 转换为VO对象
        return orderConverter.toVO(order);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateOrderStatus(Long id, Integer status) {
        if (id == null || status == null) {
            return false;
        }
        
        Order order = getById(id);
        if (order == null) {
            return false;
        }
        
        // 使用分布式锁确保操作的原子性
        return distributedLock.executeWithLock("order:status:" + id, () -> {
            // 记录原状态
            Integer oldStatus = order.getOrderStatus();
            
            // 更新订单状态
            order.setOrderStatus(status);
            order.setUpdateTime(LocalDateTime.now());
            
            boolean updated = updateById(order);
            if (updated) {
                log.info("订单状态更新成功，订单ID：{}，旧状态：{}，新状态：{}", id, oldStatus, status);
                
                // 记录订单状态变更日志
                try {
                    orderStatusLogService.logStatusChange(
                        order.getId().intValue(),
                        order.getOrderNo(),
                        oldStatus,
                        status,
                        "系统自动更新",
                        2, // 2-系统
                        0, // 系统操作无操作人ID
                        "通过updateOrderStatus方法更新"
                    );
                } catch (Exception e) {
                    log.error("记录订单状态变更日志异常，订单ID：{}，旧状态：{}，新状态：{}", 
                            id, oldStatus, status, e);
                }
                
                // 发布订单状态变更事件
                OrderStatusChangeEvent.OrderStatusType statusType;
                switch (status) {
                    case 2:
                        statusType = OrderStatusChangeEvent.OrderStatusType.COMPLETED;
                        break;
                    case 3:
                        statusType = OrderStatusChangeEvent.OrderStatusType.CANCELED;
                        break;
                    case 4:
                        statusType = OrderStatusChangeEvent.OrderStatusType.REFUNDED;
                        break;
                    default:
                        statusType = OrderStatusChangeEvent.OrderStatusType.UPDATED;
                }
                
                eventPublisher.publishEvent(new OrderStatusChangeEvent(this, order, statusType));
            }
            
            return updated;
        });
    }

    /**
     * 更新订单状态
     * 
     * @param orderId 订单ID
     * @param status 订单状态：1-进行中 2-已完成 3-已取消 4-已退款
     * @param remark 备注
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(Integer orderId, int status, String remark) {
        if (orderId == null) {
            log.warn("更新订单状态失败：订单ID不能为空");
            return;
        }
        
        // 获取订单信息
        Order order = getById(orderId);
        if (order == null) {
            log.warn("更新订单状态失败：订单[{}]不存在", orderId);
            return;
        }
        
        // 记录原状态
        Integer oldStatus = order.getOrderStatus();
        
        // 更新订单状态
        order.setOrderStatus(status);
        
        // 根据状态设置其他字段
        if (status == 1) { // 进行中
            if (order.getStartTime() == null) {
                order.setStartTime(LocalDateTime.now());
            }
        } else if (status == 2) { // 已完成
            if (order.getEndTime() == null) {
                order.setEndTime(LocalDateTime.now());
            }
        } else if (status == 3) { // 已取消
            if (order.getEndTime() == null) {
                order.setEndTime(LocalDateTime.now());
            }
        } else if (status == 4) { // 已退款
            if (order.getRefundStatus() == null || order.getRefundStatus() != 1) {
                order.setRefundStatus(1); // 已退款
            }
            if (order.getRefundTime() == null) {
                order.setRefundTime(LocalDateTime.now());
            }
        }
        
        // 设置备注
        if (StringUtils.hasText(remark)) {
            order.setRemark(remark);
        }
        
        // 更新时间
        order.setUpdateTime(LocalDateTime.now());
        
        // 更新订单
        boolean result = updateById(order);
        if (result) {
            log.info("订单[{}]状态已从[{}]更新为[{}]", orderId, getOrderStatusName(oldStatus), getOrderStatusName(status));
            
            // 记录订单状态变更日志
            try {
                orderStatusLogService.logStatusChange(
                    order.getId().intValue(),
                    order.getOrderNo(),
                    oldStatus,
                    status,
                    "系统更新",
                    2, // 2-系统
                    0, // 系统操作无操作人ID
                    remark
                );
            } catch (Exception e) {
                log.error("记录订单状态变更日志异常，订单ID：{}，旧状态：{}，新状态：{}", 
                        orderId, oldStatus, status, e);
            }
            
            // 发布订单状态变更事件
            try {
                OrderStatusChangeEvent.OrderStatusType statusType;
                switch (status) {
                    case 2:
                        statusType = OrderStatusChangeEvent.OrderStatusType.COMPLETED;
                        break;
                    case 3:
                        statusType = OrderStatusChangeEvent.OrderStatusType.CANCELED;
                        break;
                    case 4:
                        statusType = OrderStatusChangeEvent.OrderStatusType.REFUNDED;
                        break;
                    default:
                        statusType = OrderStatusChangeEvent.OrderStatusType.UPDATED;
                }
                
                eventPublisher.publishEvent(new OrderStatusChangeEvent(this, order, statusType));
            } catch (Exception e) {
                log.error("发布订单状态变更事件异常，订单ID：{}", orderId, e);
            }
        } else {
            log.warn("订单[{}]状态更新失败", orderId);
        }
    }
    
    /**
     * 获取订单状态名称
     * 
     * @param status 状态码
     * @return 状态名称
     */
    private String getOrderStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0: return "待支付";
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "已取消";
            case 4: return "已退款";
            default: return "未知状态(" + status + ")";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean repairOrder(Long orderId, Integer deviceStatus) {
        if (orderId == null || deviceStatus == null) {
            log.warn("修复订单状态失败：参数不完整");
            return false;
        }
        
        // 获取订单信息
        Order order = getById(orderId);
        if (order == null) {
            log.warn("修复订单状态失败：订单[{}]不存在", orderId);
            return false;
        }
        
        // 只处理进行中的订单
        if (order.getOrderStatus() != 1) {
            log.info("订单[{}]状态不是进行中，无需修复", orderId);
            return true;
        }
        
        // 根据设备状态处理订单
        boolean result = false;
        switch (deviceStatus) {
            case 1: // 设备正常
                // 设备正常，不做处理
                log.info("设备状态正常，无需修复订单[{}]", orderId);
                return true;
                
            case 2: // 设备维护中
                // 将订单状态改为已完成
                log.info("设备处于维护状态，将订单[{}]状态修改为已完成", orderId);
                order.setOrderStatus(2); // 已完成
                order.setEndTime(LocalDateTime.now());
                order.setRemark("因设备维护自动完成订单");
                result = updateById(order);
                
                if (result) {
                    // 发布订单完成事件
                    eventPublisher.publishEvent(new OrderStatusChangeEvent(
                            this, order, OrderStatusChangeEvent.OrderStatusType.COMPLETED));
                }
                break;
                
            case 3: // 设备故障
                // 将订单状态改为已取消
                log.info("设备处于故障状态，将订单[{}]状态修改为已取消", orderId);
                order.setOrderStatus(3); // 已取消
                order.setEndTime(LocalDateTime.now());
                order.setRemark("因设备故障自动取消订单");
                result = updateById(order);
                
                if (result) {
                    // 发布订单取消事件
                    eventPublisher.publishEvent(new OrderStatusChangeEvent(
                            this, order, OrderStatusChangeEvent.OrderStatusType.CANCELED));
                }
                break;
                
            default:
                log.warn("未知的设备状态[{}]，无法修复订单[{}]", deviceStatus, orderId);
                return false;
        }
        
        if (result) {
            log.info("订单[{}]修复成功，新状态：{}", orderId, order.getOrderStatus());
        } else {
            log.warn("订单[{}]修复失败", orderId);
        }
        
        return result;
    }

    @Override
    public int countUserOrders(Integer userId) {
        if (userId == null) {
            log.warn("统计用户订单数量失败：用户ID为空");
            return 0;
        }
        
        // 创建查询条件
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getUserId, userId);
        
        // 执行计数查询并转换为int类型
        return Math.toIntExact(count(queryWrapper));
    }

    @Override
    public int countShopOrdersByDate(Long shopId, LocalDateTime startTime, LocalDateTime endTime) {
        if (shopId == null) {
            return 0;
        }
        
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getShopId, shopId.intValue())
                   .ge(startTime != null, Order::getCreateTime, startTime)
                   .le(endTime != null, Order::getCreateTime, endTime)
                   .eq(Order::getOrderStatus, 2); // 2-已完成状态
        
        return Math.toIntExact(count(queryWrapper));
    }
} 