package com.jycb.jycbz.modules.entity.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.annotation.DataPermission;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.modules.entity.dto.EntityAnalysisDTO;
import com.jycb.jycbz.modules.entity.dto.EntityConfigDTO;
import com.jycb.jycbz.modules.entity.dto.EntityHierarchyDTO;
import com.jycb.jycbz.modules.entity.service.EntityAdvancedService;
import com.jycb.jycbz.modules.entity.vo.EntityAnalysisVO;
import com.jycb.jycbz.modules.entity.vo.EntityHierarchyVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统管理员-业务主体高级管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/entity/advanced")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统管理员-业务主体高级管理", description = "业务主体的高级管理和分析功能")
@SaCheckLogin
public class AdminEntityAdvancedController {

    private final EntityAdvancedService entityAdvancedService;

    /**
     * 获取业务主体层级结构
     */
    @Operation(summary = "获取业务主体层级结构")
    @GetMapping("/hierarchy")
    @SaCheckPermission("admin:entity:hierarchy")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.READ,
        description = "查看业务主体层级结构"
    )
    public CommonResult<List<EntityHierarchyVO>> getEntityHierarchy() {
        log.info("系统管理员查看业务主体层级结构");
        List<EntityHierarchyVO> result = entityAdvancedService.getEntityHierarchy();
        return CommonResult.success(result);
    }

    /**
     * 调整业务主体层级关系
     */
    @Operation(summary = "调整业务主体层级关系")
    @PostMapping("/hierarchy/adjust")
    @SaCheckPermission("admin:entity:hierarchy:adjust")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.UPDATE,
        description = "调整业务主体层级关系"
    )
    public CommonResult<String> adjustEntityHierarchy(@Valid @RequestBody EntityHierarchyDTO hierarchyDTO) {
        log.info("系统管理员调整业务主体层级关系，实体ID: {}", hierarchyDTO.getEntityId());
        boolean result = entityAdvancedService.adjustEntityHierarchy(hierarchyDTO);
        return result ? CommonResult.success("层级关系调整成功") : CommonResult.failed("层级关系调整失败");
    }

    /**
     * 获取业务主体数据分析
     */
    @Operation(summary = "获取业务主体数据分析")
    @GetMapping("/analysis")
    @SaCheckPermission("admin:entity:analysis")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getEntityAnalysis(@Valid EntityAnalysisDTO analysisDTO) {
        log.info("系统管理员查看业务主体数据分析");
        Map<String, Object> result = entityAdvancedService.getEntityAnalysis(analysisDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取业务主体配置
     */
    @Operation(summary = "获取业务主体配置")
    @GetMapping("/{id}/config")
    @SaCheckPermission("admin:entity:config")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getEntityConfig(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id) {
        log.info("系统管理员查看业务主体配置，ID: {}", id);
        Map<String, Object> result = entityAdvancedService.getEntityConfig(id);
        return CommonResult.success(result);
    }

    /**
     * 更新业务主体配置
     */
    @Operation(summary = "更新业务主体配置")
    @PutMapping("/{id}/config")
    @SaCheckPermission("admin:entity:config:update")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.UPDATE,
        description = "更新业务主体配置"
    )
    public CommonResult<String> updateEntityConfig(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody EntityConfigDTO configDTO) {
        log.info("系统管理员更新业务主体配置，ID: {}", id);
        boolean result = entityAdvancedService.updateEntityConfig(id, configDTO);
        return result ? CommonResult.success("配置更新成功") : CommonResult.failed("配置更新失败");
    }

    /**
     * 业务主体数据同步
     */
    @Operation(summary = "业务主体数据同步")
    @PostMapping("/{id}/sync")
    @SaCheckPermission("admin:entity:sync")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.UPDATE,
        description = "业务主体数据同步"
    )
    public CommonResult<String> syncEntityData(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id,
            @Parameter(description = "同步类型") @RequestParam String syncType) {
        log.info("系统管理员同步业务主体数据，ID: {}, 类型: {}", id, syncType);
        boolean result = entityAdvancedService.syncEntityData(id, syncType);
        return result ? CommonResult.success("数据同步成功") : CommonResult.failed("数据同步失败");
    }

    /**
     * 业务主体合规检查
     */
    @Operation(summary = "业务主体合规检查")
    @PostMapping("/{id}/compliance-check")
    @SaCheckPermission("admin:entity:compliance")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.READ,
        description = "业务主体合规检查"
    )
    public CommonResult<Map<String, Object>> complianceCheck(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id) {
        log.info("系统管理员执行业务主体合规检查，ID: {}", id);
        Map<String, Object> result = entityAdvancedService.complianceCheck(id);
        return CommonResult.success(result);
    }

    /**
     * 业务主体风险评估
     */
    @Operation(summary = "业务主体风险评估")
    @PostMapping("/{id}/risk-assessment")
    @SaCheckPermission("admin:entity:risk")
    public CommonResult<Map<String, Object>> riskAssessment(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id) {
        log.info("系统管理员执行业务主体风险评估，ID: {}", id);
        Map<String, Object> result = entityAdvancedService.riskAssessment(id);
        return CommonResult.success(result);
    }

    /**
     * 业务主体绩效评估
     */
    @Operation(summary = "业务主体绩效评估")
    @GetMapping("/{id}/performance")
    @SaCheckPermission("admin:entity:performance")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getEntityPerformance(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id,
            @Parameter(description = "评估周期") @RequestParam(defaultValue = "month") String period) {
        log.info("系统管理员查看业务主体绩效评估，ID: {}, 周期: {}", id, period);
        Map<String, Object> result = entityAdvancedService.getEntityPerformance(id, period);
        return CommonResult.success(result);
    }

    /**
     * 业务主体对比分析
     */
    @Operation(summary = "业务主体对比分析")
    @PostMapping("/comparison")
    @SaCheckPermission("admin:entity:comparison")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> entityComparison(
            @Parameter(description = "对比的业务主体ID列表") @RequestBody List<Long> entityIds,
            @Parameter(description = "对比维度") @RequestParam String dimension) {
        log.info("系统管理员执行业务主体对比分析，实体IDs: {}, 维度: {}", entityIds, dimension);
        Map<String, Object> result = entityAdvancedService.entityComparison(entityIds, dimension);
        return CommonResult.success(result);
    }

    /**
     * 业务主体预警管理
     */
    @Operation(summary = "获取业务主体预警信息")
    @GetMapping("/alerts")
    @SaCheckPermission("admin:entity:alerts")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<List<Map<String, Object>>> getEntityAlerts(
            @Parameter(description = "预警级别") @RequestParam(required = false) String level,
            @Parameter(description = "业务主体ID") @RequestParam(required = false) Long entityId) {
        log.info("系统管理员查看业务主体预警信息");
        List<Map<String, Object>> result = entityAdvancedService.getEntityAlerts(entityId, level, null);
        return CommonResult.success(result);
    }

    /**
     * 处理业务主体预警
     */
    @Operation(summary = "处理业务主体预警")
    @PostMapping("/alerts/{alertId}/handle")
    @SaCheckPermission("admin:entity:alerts:handle")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.UPDATE,
        description = "处理业务主体预警"
    )
    public CommonResult<String> handleEntityAlert(
            @Parameter(description = "预警ID") @PathVariable @NotNull Long alertId,
            @Parameter(description = "处理说明") @RequestParam String handleNote) {
        log.info("系统管理员处理业务主体预警，预警ID: {}", alertId);
        boolean result = entityAdvancedService.handleEntityAlert(alertId, "HANDLE", handleNote);
        return result ? CommonResult.success("预警处理成功") : CommonResult.failed("预警处理失败");
    }

    /**
     * 业务主体数据导入导出
     */
    @Operation(summary = "导出业务主体数据")
    @GetMapping("/export")
    @SaCheckPermission("admin:entity:export")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.EXPORT,
        description = "导出业务主体数据"
    )
    public CommonResult<String> exportEntityData(
            @Parameter(description = "导出类型") @RequestParam String exportType,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "excel") String format) {
        log.info("系统管理员导出业务主体数据，类型: {}, 格式: {}", exportType, format);
        String result = entityAdvancedService.exportEntityData(null, format, null);
        return CommonResult.success(result);
    }

    /**
     * 批量导入业务主体
     */
    @Operation(summary = "批量导入业务主体")
    @PostMapping("/import")
    @SaCheckPermission("admin:entity:import")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.IMPORT,
        description = "批量导入业务主体"
    )
    public CommonResult<Map<String, Object>> importEntityData(
            @Parameter(description = "导入文件路径") @RequestParam String filePath,
            @Parameter(description = "导入模式") @RequestParam(defaultValue = "insert") String mode) {
        log.info("系统管理员批量导入业务主体，文件: {}, 模式: {}", filePath, mode);
        Map<String, Object> result = entityAdvancedService.importEntityData(filePath, mode, false);
        return CommonResult.success(result);
    }

    /**
     * 业务主体模板管理
     */
    @Operation(summary = "获取业务主体模板")
    @GetMapping("/templates")
    @SaCheckPermission("admin:entity:template")
    public CommonResult<List<Map<String, Object>>> getEntityTemplates() {
        log.info("系统管理员查看业务主体模板");
        List<Map<String, Object>> result = entityAdvancedService.getEntityTemplates();
        return CommonResult.success(result);
    }

    /**
     * 应用业务主体模板
     */
    @Operation(summary = "应用业务主体模板")
    @PostMapping("/{id}/apply-template")
    @SaCheckPermission("admin:entity:template:apply")
    @Auditable(
        module = AuditConstants.Module.ENTITY,
        operation = AuditConstants.Operation.UPDATE,
        description = "应用业务主体模板"
    )
    public CommonResult<String> applyEntityTemplate(
            @Parameter(description = "业务主体ID") @PathVariable @NotNull Long id,
            @Parameter(description = "模板ID") @RequestParam @NotNull Long templateId) {
        log.info("系统管理员应用业务主体模板，实体ID: {}, 模板ID: {}", id, templateId);
        boolean result = entityAdvancedService.applyEntityTemplate(id, templateId);
        return result ? CommonResult.success("模板应用成功") : CommonResult.failed("模板应用失败");
    }
}
