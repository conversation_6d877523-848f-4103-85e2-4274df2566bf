package com.jycb.jycbz.modules.statistics.service;

import com.jycb.jycbz.modules.statistics.dto.StatisticsQueryDTO;

import java.util.List;
import java.util.Map;

/**
 * 统计服务接口
 */
public interface StatisticsService {

    // ==================== 概览统计 ====================

    /**
     * 获取概览
     */
    Map<String, Object> getOverview();

    /**
     * 获取统计概览
     */
    Map<String, Object> getStatisticsOverview();

    /**
     * 获取实时统计数据
     */
    Map<String, Object> getRealTimeStatistics();

    /**
     * 获取今日统计数据
     */
    Map<String, Object> getTodayStatistics();

    // ==================== 收入统计 ====================

    /**
     * 获取收入统计
     */
    Map<String, Object> getRevenueStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取收入趋势
     */
    List<Map<String, Object>> getRevenueTrend(StatisticsQueryDTO queryDTO);

    /**
     * 获取收入排行
     */
    List<Map<String, Object>> getRevenueRanking(StatisticsQueryDTO queryDTO);

    // ==================== 财务统计 ====================

    /**
     * 获取财务统计
     */
    Map<String, Object> getFinanceStatistics(StatisticsQueryDTO queryDTO);

    // ==================== 商店统计 ====================

    /**
     * 获取商店统计
     */
    Map<String, Object> getShopStatistics(StatisticsQueryDTO queryDTO);

    // ==================== 合作伙伴统计 ====================

    /**
     * 获取合作伙伴统计
     */
    Map<String, Object> getPartnerStatistics(StatisticsQueryDTO queryDTO);

    // ==================== 实体统计 ====================

    /**
     * 获取实体统计
     */
    Map<String, Object> getEntityStatistics(StatisticsQueryDTO queryDTO);

    // ==================== 订单统计 ====================

    /**
     * 获取订单统计
     */
    Map<String, Object> getOrderStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取订单趋势
     */
    List<Map<String, Object>> getOrderTrend(StatisticsQueryDTO queryDTO);

    /**
     * 获取订单状态分布
     */
    Map<String, Object> getOrderStatusDistribution(StatisticsQueryDTO queryDTO);

    // ==================== 用户统计 ====================

    /**
     * 获取用户统计
     */
    Map<String, Object> getUserStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取用户增长趋势
     */
    List<Map<String, Object>> getUserGrowthTrend(StatisticsQueryDTO queryDTO);

    /**
     * 获取用户活跃度统计
     */
    Map<String, Object> getUserActivityStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取用户行为分析
     */
    Map<String, Object> getUserBehaviorAnalysis(StatisticsQueryDTO queryDTO);

    // ==================== 设备统计 ====================

    /**
     * 获取设备统计
     */
    Map<String, Object> getDeviceStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取设备使用分析
     */
    Map<String, Object> getDeviceUsageAnalysis(StatisticsQueryDTO queryDTO);

    /**
     * 获取设备利用率统计
     */
    Map<String, Object> getDeviceUtilizationStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取设备状态分布
     */
    Map<String, Object> getDeviceStatusDistribution(StatisticsQueryDTO queryDTO);

    // ==================== 地域统计 ====================

    /**
     * 获取地域分布统计
     */
    Map<String, Object> getGeographicDistribution(StatisticsQueryDTO queryDTO);

    /**
     * 获取省份统计
     */
    List<Map<String, Object>> getProvinceStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取城市统计
     */
    List<Map<String, Object>> getCityStatistics(StatisticsQueryDTO queryDTO);

    /**
     * 获取地域分析
     */
    Map<String, Object> getGeographicAnalysis(StatisticsQueryDTO queryDTO);

    // ==================== 时间统计 ====================

    /**
     * 获取时间分布统计
     */
    Map<String, Object> getTimeDistribution(StatisticsQueryDTO queryDTO);

    /**
     * 获取小时分布统计
     */
    List<Map<String, Object>> getHourlyDistribution(StatisticsQueryDTO queryDTO);

    /**
     * 获取时间段分析
     */
    Map<String, Object> getTimePeriodAnalysis(StatisticsQueryDTO queryDTO);

    /**
     * 获取星期分布统计
     */
    List<Map<String, Object>> getWeeklyDistribution(StatisticsQueryDTO queryDTO);

    // ==================== 对比分析 ====================

    /**
     * 获取同期对比数据
     */
    Map<String, Object> getPeriodComparison(StatisticsQueryDTO queryDTO);

    /**
     * 获取环比数据
     */
    Map<String, Object> getSequentialComparison(StatisticsQueryDTO queryDTO);

    /**
     * 获取同比数据
     */
    Map<String, Object> getYearOverYearComparison(StatisticsQueryDTO queryDTO);

    // ==================== 排行榜 ====================

    /**
     * 获取业务主体排行
     */
    List<Map<String, Object>> getEntityRanking(StatisticsQueryDTO queryDTO);

    /**
     * 获取合作商排行
     */
    List<Map<String, Object>> getPartnerRanking(StatisticsQueryDTO queryDTO);

    /**
     * 获取门店排行
     */
    List<Map<String, Object>> getShopRanking(StatisticsQueryDTO queryDTO);

    /**
     * 获取设备排行
     */
    List<Map<String, Object>> getDeviceRanking(StatisticsQueryDTO queryDTO);

    /**
     * 获取排行榜
     */
    List<Map<String, Object>> getRankings(String rankingType, Integer limit, StatisticsQueryDTO queryDTO);

    // ==================== 比较分析 ====================

    /**
     * 获取比较分析
     */
    Map<String, Object> getComparisonAnalysis(String comparisonType, String period, StatisticsQueryDTO queryDTO);

    // ==================== 预测分析 ====================

    /**
     * 获取预测分析
     */
    Map<String, Object> getForecastAnalysis(String forecastType, Integer period, StatisticsQueryDTO queryDTO);

    // ==================== 异常检测 ====================

    /**
     * 获取异常检测
     */
    List<Map<String, Object>> getAnomalyDetection(StatisticsQueryDTO queryDTO);

    // ==================== 数据导出 ====================

    /**
     * 导出统计数据
     */
    String exportStatisticsData(StatisticsQueryDTO queryDTO, String format);

    /**
     * 生成统计报告
     */
    String generateStatisticsReport(StatisticsQueryDTO queryDTO);

    /**
     * 获取导出模板
     */
    String getExportTemplate(String statisticsType);

    // ==================== 缓存管理 ====================

    /**
     * 刷新统计缓存
     */
    boolean refreshStatisticsCache();

    /**
     * 清理统计缓存
     */
    boolean clearStatisticsCache(String cacheKey);

    /**
     * 预热统计缓存
     */
    boolean warmupStatisticsCache();

    // ==================== 扩展方法 ====================

    /**
     * 生成统计报告
     */
    Map<String, Object> generateReport(String reportType, String format, StatisticsQueryDTO queryDTO);

    /**
     * 导出统计数据
     */
    Map<String, Object> exportStatistics(String exportType, String format, StatisticsQueryDTO queryDTO);

    /**
     * 获取实时数据
     */
    Map<String, Object> getRealtimeData(String dataType);

    /**
     * 获取自定义统计
     */
    Map<String, Object> getCustomStatistics(Map<String, Object> customQuery);
}
