package com.jycb.jycbz.modules.statistics.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.jycb.jycbz.common.annotation.Auditable;
import com.jycb.jycbz.common.annotation.DataPermission;
import com.jycb.jycbz.common.api.CommonResult;
import com.jycb.jycbz.common.constant.AuditConstants;
import com.jycb.jycbz.modules.statistics.dto.StatisticsQueryDTO;
import com.jycb.jycbz.modules.statistics.service.StatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 系统管理员-统计分析控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin/statistics")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统管理员-统计分析", description = "全面的数据统计和分析功能")
@SaCheckLogin
public class AdminStatisticsController {

    private final StatisticsService statisticsService;

    /**
     * 获取总体数据概览
     */
    @Operation(summary = "获取总体数据概览")
    @GetMapping("/overview")
    @SaCheckPermission("admin:statistics:overview")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    @Auditable(
        module = AuditConstants.Module.STATISTICS,
        operation = AuditConstants.Operation.READ,
        description = "查看总体数据概览"
    )
    public CommonResult<Map<String, Object>> getOverview() {
        log.info("系统管理员查看总体数据概览");
        Map<String, Object> result = statisticsService.getOverview();
        return CommonResult.success(result);
    }

    /**
     * 获取用户统计
     */
    @Operation(summary = "获取用户统计")
    @GetMapping("/users")
    @SaCheckPermission("admin:statistics:users")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getUserStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看用户统计");
        Map<String, Object> result = statisticsService.getUserStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取设备统计
     */
    @Operation(summary = "获取设备统计")
    @GetMapping("/devices")
    @SaCheckPermission("admin:statistics:devices")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getDeviceStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看设备统计");
        Map<String, Object> result = statisticsService.getDeviceStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取订单统计
     */
    @Operation(summary = "获取订单统计")
    @GetMapping("/orders")
    @SaCheckPermission("admin:statistics:orders")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getOrderStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看订单统计");
        Map<String, Object> result = statisticsService.getOrderStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取财务统计
     */
    @Operation(summary = "获取财务统计")
    @GetMapping("/finance")
    @SaCheckPermission("admin:statistics:finance")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getFinanceStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看财务统计");
        Map<String, Object> result = statisticsService.getFinanceStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取门店统计
     */
    @Operation(summary = "获取门店统计")
    @GetMapping("/shops")
    @SaCheckPermission("admin:statistics:shops")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getShopStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看门店统计");
        Map<String, Object> result = statisticsService.getShopStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取合作商统计
     */
    @Operation(summary = "获取合作商统计")
    @GetMapping("/partners")
    @SaCheckPermission("admin:statistics:partners")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getPartnerStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看合作商统计");
        Map<String, Object> result = statisticsService.getPartnerStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取业务主体统计
     */
    @Operation(summary = "获取业务主体统计")
    @GetMapping("/entities")
    @SaCheckPermission("admin:statistics:entities")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getEntityStatistics(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看业务主体统计");
        Map<String, Object> result = statisticsService.getEntityStatistics(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取收入趋势分析
     */
    @Operation(summary = "获取收入趋势分析")
    @GetMapping("/revenue-trend")
    @SaCheckPermission("admin:statistics:revenue")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<List<Map<String, Object>>> getRevenueTrend(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看收入趋势分析");
        List<Map<String, Object>> result = statisticsService.getRevenueTrend(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取用户行为分析
     */
    @Operation(summary = "获取用户行为分析")
    @GetMapping("/user-behavior")
    @SaCheckPermission("admin:statistics:behavior")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getUserBehaviorAnalysis(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看用户行为分析");
        Map<String, Object> result = statisticsService.getUserBehaviorAnalysis(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取设备使用率分析
     */
    @Operation(summary = "获取设备使用率分析")
    @GetMapping("/device-usage")
    @SaCheckPermission("admin:statistics:usage")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getDeviceUsageAnalysis(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看设备使用率分析");
        Map<String, Object> result = statisticsService.getDeviceUsageAnalysis(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取地域分布分析
     */
    @Operation(summary = "获取地域分布分析")
    @GetMapping("/geographic")
    @SaCheckPermission("admin:statistics:geographic")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getGeographicAnalysis(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看地域分布分析");
        Map<String, Object> result = statisticsService.getGeographicAnalysis(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取时间段分析
     */
    @Operation(summary = "获取时间段分析")
    @GetMapping("/time-period")
    @SaCheckPermission("admin:statistics:time")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getTimePeriodAnalysis(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看时间段分析");
        Map<String, Object> result = statisticsService.getTimePeriodAnalysis(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取排行榜数据
     */
    @Operation(summary = "获取排行榜数据")
    @GetMapping("/rankings")
    @SaCheckPermission("admin:statistics:rankings")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<List<Map<String, Object>>> getRankings(
            @Parameter(description = "排行榜类型") @RequestParam String type,
            @Parameter(description = "排行数量") @RequestParam(defaultValue = "10") Integer limit,
            @Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看排行榜数据，类型: {}", type);
        List<Map<String, Object>> result = statisticsService.getRankings(type, limit, queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取对比分析
     */
    @Operation(summary = "获取对比分析")
    @GetMapping("/comparison")
    @SaCheckPermission("admin:statistics:comparison")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getComparisonAnalysis(
            @Parameter(description = "对比类型") @RequestParam String type,
            @Parameter(description = "对比维度") @RequestParam String dimension,
            @Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看对比分析，类型: {}, 维度: {}", type, dimension);
        Map<String, Object> result = statisticsService.getComparisonAnalysis(type, dimension, queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取预测分析
     */
    @Operation(summary = "获取预测分析")
    @GetMapping("/forecast")
    @SaCheckPermission("admin:statistics:forecast")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getForecastAnalysis(
            @Parameter(description = "预测类型") @RequestParam String type,
            @Parameter(description = "预测周期") @RequestParam(defaultValue = "30") Integer days,
            @Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看预测分析，类型: {}, 周期: {}天", type, days);
        Map<String, Object> result = statisticsService.getForecastAnalysis(type, days, queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取异常检测
     */
    @Operation(summary = "获取异常检测")
    @GetMapping("/anomaly")
    @SaCheckPermission("admin:statistics:anomaly")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<List<Map<String, Object>>> getAnomalyDetection(@Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员查看异常检测");
        List<Map<String, Object>> result = statisticsService.getAnomalyDetection(queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 生成统计报告
     */
    @Operation(summary = "生成统计报告")
    @PostMapping("/report/generate")
    @SaCheckPermission("admin:statistics:report")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    @Auditable(
        module = AuditConstants.Module.STATISTICS,
        operation = AuditConstants.Operation.CREATE,
        description = "生成统计报告"
    )
    public CommonResult<Map<String, Object>> generateReport(
            @Parameter(description = "报告类型") @RequestParam String reportType,
            @Parameter(description = "报告格式") @RequestParam(defaultValue = "pdf") String format,
            @Valid @RequestBody StatisticsQueryDTO queryDTO) {
        log.info("系统管理员生成统计报告，类型: {}, 格式: {}", reportType, format);
        Map<String, Object> result = statisticsService.generateReport(reportType, format, queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 导出统计数据
     */
    @Operation(summary = "导出统计数据")
    @GetMapping("/export")
    @SaCheckPermission("admin:statistics:export")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    @Auditable(
        module = AuditConstants.Module.STATISTICS,
        operation = AuditConstants.Operation.EXPORT,
        description = "导出统计数据"
    )
    public CommonResult<Map<String, Object>> exportStatistics(
            @Parameter(description = "导出类型") @RequestParam String exportType,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "excel") String format,
            @Valid StatisticsQueryDTO queryDTO) {
        log.info("系统管理员导出统计数据，类型: {}, 格式: {}", exportType, format);
        Map<String, Object> result = statisticsService.exportStatistics(exportType, format, queryDTO);
        return CommonResult.success(result);
    }

    /**
     * 获取实时数据
     */
    @Operation(summary = "获取实时数据")
    @GetMapping("/realtime")
    @SaCheckPermission("admin:statistics:realtime")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    public CommonResult<Map<String, Object>> getRealtimeData(
            @Parameter(description = "数据类型") @RequestParam(required = false) String dataType) {
        log.info("系统管理员查看实时数据，类型: {}", dataType);
        Map<String, Object> result = statisticsService.getRealtimeData(dataType);
        return CommonResult.success(result);
    }

    /**
     * 获取自定义统计
     */
    @Operation(summary = "获取自定义统计")
    @PostMapping("/custom")
    @SaCheckPermission("admin:statistics:custom")
    @DataPermission(type = DataPermission.PermissionType.AUTO)
    @Auditable(
        module = AuditConstants.Module.STATISTICS,
        operation = AuditConstants.Operation.READ,
        description = "查看自定义统计"
    )
    public CommonResult<Map<String, Object>> getCustomStatistics(
            @Parameter(description = "自定义查询配置") @RequestBody Map<String, Object> customQuery) {
        log.info("系统管理员查看自定义统计");
        Map<String, Object> result = statisticsService.getCustomStatistics(customQuery);
        return CommonResult.success(result);
    }
}
