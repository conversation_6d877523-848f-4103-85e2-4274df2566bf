package com.jycb.jycbz.modules.cleaner.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.cleaner.dto.CleanerCreateDTO;
import com.jycb.jycbz.modules.cleaner.dto.CleanerQueryDTO;
import com.jycb.jycbz.modules.cleaner.dto.CleanerUpdateDTO;
import com.jycb.jycbz.modules.cleaner.entity.Cleaner;
import com.jycb.jycbz.modules.cleaner.mapper.CleanerMapper;
import com.jycb.jycbz.modules.cleaner.service.CleanerService;
import com.jycb.jycbz.modules.cleaner.vo.CleanerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 清洁人员服务实现类
 */
@Slf4j
@Service
public class CleanerServiceImpl extends ServiceImpl<CleanerMapper, Cleaner> implements CleanerService {

    @Override
    public IPage<CleanerVO> getCleanerPage(CleanerQueryDTO queryDTO) {
        Page<Cleaner> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        LambdaQueryWrapper<Cleaner> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(queryDTO.getName())) {
            wrapper.like(Cleaner::getName, queryDTO.getName());
        }
        if (StringUtils.hasText(queryDTO.getPhone())) {
            wrapper.like(Cleaner::getPhone, queryDTO.getPhone());
        }
        if (queryDTO.getStatus() != null) {
            wrapper.eq(Cleaner::getStatus, queryDTO.getStatus());
        }
        if (queryDTO.getShopId() != null) {
            wrapper.eq(Cleaner::getShopId, queryDTO.getShopId());
        }
        
        wrapper.orderByDesc(Cleaner::getCreateTime);
        
        IPage<Cleaner> cleanerPage = page(page, wrapper);
        
        // 转换为VO
        IPage<CleanerVO> voPage = new Page<>();
        BeanUtils.copyProperties(cleanerPage, voPage);
        
        List<CleanerVO> voList = cleanerPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        
        return voPage;
    }

    @Override
    public CleanerVO getCleanerDetail(Long id) {
        Cleaner cleaner = getById(id);
        if (cleaner == null) {
            throw new BusinessException("清洁人员不存在");
        }
        return convertToVO(cleaner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createCleaner(CleanerCreateDTO createDTO) {
        // 检查手机号是否已存在
        if (StringUtils.hasText(createDTO.getPhone())) {
            long count = lambdaQuery()
                    .eq(Cleaner::getPhone, createDTO.getPhone())
                    .count();
            if (count > 0) {
                throw new BusinessException("手机号已存在");
            }
        }
        
        Cleaner cleaner = new Cleaner();
        BeanUtils.copyProperties(createDTO, cleaner);
        cleaner.setCreateTime(LocalDateTime.now());
        cleaner.setUpdateTime(LocalDateTime.now());
        
        return save(cleaner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCleaner(CleanerUpdateDTO updateDTO) {
        Cleaner existingCleaner = getById(updateDTO.getId());
        if (existingCleaner == null) {
            throw new BusinessException("清洁人员不存在");
        }
        
        // 检查手机号是否已被其他人使用
        if (StringUtils.hasText(updateDTO.getPhone())) {
            long count = lambdaQuery()
                    .eq(Cleaner::getPhone, updateDTO.getPhone())
                    .ne(Cleaner::getId, updateDTO.getId())
                    .count();
            if (count > 0) {
                throw new BusinessException("手机号已被其他人使用");
            }
        }
        
        BeanUtils.copyProperties(updateDTO, existingCleaner);
        existingCleaner.setUpdateTime(LocalDateTime.now());
        
        return updateById(existingCleaner);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCleaner(Long id) {
        if (!canDeleteCleaner(id)) {
            throw new BusinessException("该清洁人员有未完成的任务，无法删除");
        }
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCleanerStatus(Long id, Integer status) {
        Cleaner cleaner = getById(id);
        if (cleaner == null) {
            throw new BusinessException("清洁人员不存在");
        }
        
        cleaner.setStatus(status);
        cleaner.setUpdateTime(LocalDateTime.now());
        
        return updateById(cleaner);
    }

    @Override
    public Map<String, Object> getCleanerStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 总人数
        long totalCount = count();
        statistics.put("totalCount", totalCount);
        
        // 在职人数
        long activeCount = lambdaQuery()
                .eq(Cleaner::getStatus, 1)
                .count();
        statistics.put("activeCount", activeCount);
        
        // 离职人数
        long inactiveCount = totalCount - activeCount;
        statistics.put("inactiveCount", inactiveCount);
        
        return statistics;
    }

    @Override
    public List<CleanerVO> getAvailableCleaners() {
        List<Cleaner> cleaners = lambdaQuery()
                .eq(Cleaner::getStatus, 1)
                .orderBy(true, true, Cleaner::getName)
                .list();
        
        return cleaners.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CleanerVO> getCleanersByShopId(Long shopId) {
        List<Cleaner> cleaners = lambdaQuery()
                .eq(Cleaner::getShopId, shopId)
                .eq(Cleaner::getStatus, 1)
                .orderBy(true, true, Cleaner::getName)
                .list();
        
        return cleaners.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getCleanerWorkStatistics(Long cleanerId, String startTime, String endTime) {
        // TODO: 实现清洁人员工作统计
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("cleanerId", cleanerId);
        statistics.put("taskCount", 0);
        statistics.put("completedCount", 0);
        statistics.put("workHours", 0);
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> batchImportCleaners(List<CleanerCreateDTO> cleaners) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failCount = 0;
        List<String> errors = new ArrayList<>();
        
        for (CleanerCreateDTO createDTO : cleaners) {
            try {
                createCleaner(createDTO);
                successCount++;
            } catch (Exception e) {
                failCount++;
                errors.add("导入失败：" + createDTO.getName() + " - " + e.getMessage());
            }
        }
        
        result.put("successCount", successCount);
        result.put("failCount", failCount);
        result.put("errors", errors);
        
        return result;
    }

    @Override
    public List<CleanerVO> exportCleaners(CleanerQueryDTO queryDTO) {
        LambdaQueryWrapper<Cleaner> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(queryDTO.getName())) {
            wrapper.like(Cleaner::getName, queryDTO.getName());
        }
        if (StringUtils.hasText(queryDTO.getPhone())) {
            wrapper.like(Cleaner::getPhone, queryDTO.getPhone());
        }
        if (queryDTO.getStatus() != null) {
            wrapper.eq(Cleaner::getStatus, queryDTO.getStatus());
        }
        if (queryDTO.getShopId() != null) {
            wrapper.eq(Cleaner::getShopId, queryDTO.getShopId());
        }
        
        wrapper.orderByDesc(Cleaner::getCreateTime);
        
        List<Cleaner> cleaners = list(wrapper);
        
        return cleaners.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public boolean canDeleteCleaner(Long id) {
        // TODO: 检查是否有未完成的清洁任务
        return true;
    }

    @Override
    public Map<String, Object> getCleanerTaskStatistics(Long cleanerId) {
        // TODO: 实现清洁人员任务统计
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalTasks", 0);
        statistics.put("completedTasks", 0);
        statistics.put("pendingTasks", 0);
        statistics.put("averageRating", 0.0);
        return statistics;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCleanerRating(Long cleanerId, Double rating, String comment) {
        // TODO: 实现清洁人员评分更新
        log.info("更新清洁人员评分: cleanerId={}, rating={}, comment={}", cleanerId, rating, comment);
        return true;
    }

    @Override
    public List<Map<String, Object>> getCleanerRatings(Long cleanerId) {
        // TODO: 实现获取清洁人员评价列表
        return new ArrayList<>();
    }

    /**
     * 转换为VO对象
     */
    private CleanerVO convertToVO(Cleaner cleaner) {
        CleanerVO vo = new CleanerVO();
        BeanUtils.copyProperties(cleaner, vo);
        return vo;
    }
}
