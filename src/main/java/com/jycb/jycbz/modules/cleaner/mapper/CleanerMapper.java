package com.jycb.jycbz.modules.cleaner.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jycb.jycbz.modules.cleaner.entity.Cleaner;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 清洁人员Mapper接口
 */
@Mapper
public interface CleanerMapper extends BaseMapper<Cleaner> {

    /**
     * 根据门店ID查询清洁人员列表
     *
     * @param shopId 门店ID
     * @return 清洁人员列表
     */
    @Select("SELECT * FROM jy_cleaner WHERE shop_id = #{shopId} AND status = 1 ORDER BY name")
    List<Cleaner> selectByShopId(@Param("shopId") Long shopId);

    /**
     * 查询可用的清洁人员
     *
     * @return 清洁人员列表
     */
    @Select("SELECT * FROM jy_cleaner WHERE status = 1 ORDER BY name")
    List<Cleaner> selectAvailableCleaners();

    /**
     * 统计清洁人员数量
     *
     * @return 统计结果
     */
    @Select("SELECT " +
            "COUNT(*) as totalCount, " +
            "COUNT(CASE WHEN status = 1 THEN 1 END) as activeCount, " +
            "COUNT(CASE WHEN status = 0 THEN 1 END) as inactiveCount " +
            "FROM jy_cleaner")
    Map<String, Object> selectStatistics();

    /**
     * 根据手机号查询清洁人员
     *
     * @param phone 手机号
     * @return 清洁人员
     */
    @Select("SELECT * FROM jy_cleaner WHERE phone = #{phone} LIMIT 1")
    Cleaner selectByPhone(@Param("phone") String phone);

    /**
     * 查询清洁人员工作统计
     *
     * @param cleanerId 清洁人员ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 工作统计
     */
    @Select("SELECT " +
            "COUNT(*) as taskCount, " +
            "COUNT(CASE WHEN status = 2 THEN 1 END) as completedCount, " +
            "SUM(CASE WHEN status = 2 AND duration IS NOT NULL THEN duration ELSE 0 END) as totalDuration " +
            "FROM jy_clean_task " +
            "WHERE cleaner_id = #{cleanerId} " +
            "AND create_time BETWEEN #{startTime} AND #{endTime}")
    Map<String, Object> selectWorkStatistics(@Param("cleanerId") Long cleanerId, 
                                           @Param("startTime") String startTime, 
                                           @Param("endTime") String endTime);

    /**
     * 查询清洁人员任务统计
     *
     * @param cleanerId 清洁人员ID
     * @return 任务统计
     */
    @Select("SELECT " +
            "COUNT(*) as totalTasks, " +
            "COUNT(CASE WHEN status = 2 THEN 1 END) as completedTasks, " +
            "COUNT(CASE WHEN status IN (0, 1) THEN 1 END) as pendingTasks, " +
            "AVG(CASE WHEN rating IS NOT NULL THEN rating END) as averageRating " +
            "FROM jy_clean_task " +
            "WHERE cleaner_id = #{cleanerId}")
    Map<String, Object> selectTaskStatistics(@Param("cleanerId") Long cleanerId);

    /**
     * 查询清洁人员评价列表
     *
     * @param cleanerId 清洁人员ID
     * @return 评价列表
     */
    @Select("SELECT " +
            "rating, " +
            "comment, " +
            "create_time " +
            "FROM jy_clean_task " +
            "WHERE cleaner_id = #{cleanerId} " +
            "AND rating IS NOT NULL " +
            "ORDER BY create_time DESC")
    List<Map<String, Object>> selectRatings(@Param("cleanerId") Long cleanerId);

    /**
     * 检查清洁人员是否可以删除
     *
     * @param cleanerId 清洁人员ID
     * @return 未完成任务数量
     */
    @Select("SELECT COUNT(*) FROM jy_clean_task WHERE cleaner_id = #{cleanerId} AND status IN (0, 1)")
    int selectPendingTaskCount(@Param("cleanerId") Long cleanerId);
}
