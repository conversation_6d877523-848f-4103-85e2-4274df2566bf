package com.jycb.jycbz.modules.finance.controller;

import com.jycb.jycbz.common.api.Result;
import com.jycb.jycbz.modules.finance.service.FinanceDataValidationService;
import com.jycb.jycbz.modules.finance.util.FinanceDataRepairUtil;
import com.jycb.jycbz.modules.finance.vo.ValidationResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 财务数据修复控制器
 * 提供财务数据一致性检查和修复的API接口
 */
@Slf4j
@RestController
@RequestMapping("/finance/repair")
@Tag(name = "财务数据修复", description = "财务数据一致性检查和修复相关接口")
public class FinanceDataRepairController {

    @Autowired
    private FinanceDataValidationService financeDataValidationService;

    @Autowired
    private FinanceDataRepairUtil financeDataRepairUtil;

    /**
     * 检查账户余额一致性
     */
    @GetMapping("/check-balance")
    @Operation(summary = "检查账户余额一致性", description = "检查所有账户的余额是否与财务流水记录一致")
    public Result<ValidationResultVO> checkBalanceConsistency() {
        try {
            log.info("开始执行账户余额一致性检查");
            ValidationResultVO result = financeDataValidationService.validateAccountBalanceConsistency();
            
            log.info("账户余额一致性检查完成 - 准确率: {}%, 级别: {}", 
                    result.getAccuracyRate(), result.getLevel());
            
            return Result.ok(result);
        } catch (Exception e) {
            log.error("账户余额一致性检查失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 执行账户余额修复
     */
    @PostMapping("/fix-balance")
    @Operation(summary = "修复账户余额", description = "根据财务流水记录修复账户余额不一致问题")
    public Result<Map<String, Object>> fixAccountBalance() {
        try {
            log.info("开始执行账户余额修复");
            Map<String, Object> result = financeDataValidationService.repairAccountBalance();
            
            log.info("账户余额修复完成: {}", result);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("账户余额修复失败", e);
            return Result.error("修复失败: " + e.getMessage());
        }
    }

    /**
     * 执行完整修复流程
     */
    @PostMapping("/full-repair")
    @Operation(summary = "执行完整修复流程", description = "执行完整的财务数据检查和修复流程")
    public Result<Map<String, Object>> executeFullRepair() {
        try {
            log.info("开始执行完整修复流程");
            Map<String, Object> result = financeDataRepairUtil.executeFullRepair();
            
            log.info("完整修复流程执行完成: {}", result.get("message"));
            return Result.ok(result);
        } catch (Exception e) {
            log.error("完整修复流程执行失败", e);
            return Result.error("修复失败: " + e.getMessage());
        }
    }

    /**
     * 快速诊断
     */
    @GetMapping("/diagnosis")
    @Operation(summary = "快速诊断", description = "快速诊断财务数据问题并提供修复建议")
    public Result<String> quickDiagnosis() {
        try {
            log.info("开始快速诊断");
            financeDataRepairUtil.quickDiagnosis();
            return Result.ok("诊断完成，请查看日志获取详细信息");
        } catch (Exception e) {
            log.error("快速诊断失败", e);
            return Result.error("诊断失败: " + e.getMessage());
        }
    }

    /**
     * 验证修复效果
     */
    @GetMapping("/validate")
    @Operation(summary = "验证修复效果", description = "验证财务数据修复是否成功")
    public Result<Map<String, Object>> validateRepairEffect() {
        try {
            log.info("开始验证修复效果");
            boolean isValid = financeDataRepairUtil.validateRepairEffect();
            
            return Result.ok(Map.of(
                "isValid", isValid,
                "message", isValid ? "修复效果良好" : "仍存在问题，需要进一步修复"
            ));
        } catch (Exception e) {
            log.error("验证修复效果失败", e);
            return Result.error("验证失败: " + e.getMessage());
        }
    }

    /**
     * 实时一致性检查
     */
    @GetMapping("/realtime-check")
    @Operation(summary = "实时一致性检查", description = "执行实时财务数据一致性检查")
    public Result<Map<String, Object>> realtimeConsistencyCheck() {
        try {
            log.info("开始实时一致性检查");
            Map<String, Object> result = financeDataValidationService.realTimeConsistencyCheck();
            
            log.info("实时一致性检查完成 - 整体一致性: {}", result.get("overallConsistent"));
            return Result.ok(result);
        } catch (Exception e) {
            log.error("实时一致性检查失败", e);
            return Result.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取修复建议
     */
    @GetMapping("/suggestions")
    @Operation(summary = "获取修复建议", description = "根据当前数据状态提供修复建议")
    public Result<Map<String, Object>> getRepairSuggestions() {
        try {
            log.info("开始获取修复建议");
            ValidationResultVO result = financeDataValidationService.validateAccountBalanceConsistency();
            
            String suggestion;
            String priority;
            
            if (result.getAccuracyRate() >= 99.0) {
                suggestion = "数据状态优秀，建议定期监控即可";
                priority = "LOW";
            } else if (result.getAccuracyRate() >= 95.0) {
                suggestion = "数据状态良好，建议进行细微调整";
                priority = "MEDIUM";
            } else if (result.getAccuracyRate() >= 80.0) {
                suggestion = "存在数据不一致问题，建议执行完整修复流程";
                priority = "HIGH";
            } else {
                suggestion = "数据问题严重，需要立即执行修复并进行人工检查";
                priority = "URGENT";
            }
            
            return Result.ok(Map.of(
                "accuracyRate", result.getAccuracyRate(),
                "suggestion", suggestion,
                "priority", priority,
                "invalidRecords", result.getInvalidRecords(),
                "totalRecords", result.getTotalRecords(),
                "needsRepair", result.getAccuracyRate() < 95.0
            ));
        } catch (Exception e) {
            log.error("获取修复建议失败", e);
            return Result.error("获取建议失败: " + e.getMessage());
        }
    }
}
