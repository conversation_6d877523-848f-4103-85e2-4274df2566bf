package com.jycb.jycbz.modules.finance.util;

import com.jycb.jycbz.modules.finance.service.FinanceDataValidationService;
import com.jycb.jycbz.modules.finance.vo.ValidationResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 财务数据修复工具类
 * 提供手动修复财务数据不一致问题的工具方法
 */
@Slf4j
@Component
public class FinanceDataRepairUtil {

    @Autowired
    private FinanceDataValidationService financeDataValidationService;

    /**
     * 执行完整的财务数据修复流程
     * 
     * @return 修复结果
     */
    public Map<String, Object> executeFullRepair() {
        log.info("开始执行完整的财务数据修复流程");
        
        try {
            // 1. 检查修复前状态
            log.info("步骤1: 检查修复前状态");
            ValidationResultVO beforeResult = financeDataValidationService.validateAccountBalanceConsistency();
            log.info("修复前状态 - 总记录: {}, 有效: {}, 无效: {}, 准确率: {}%", 
                    beforeResult.getTotalRecords(), beforeResult.getValidRecords(), 
                    beforeResult.getInvalidRecords(), beforeResult.getAccuracyRate());
            
            if (beforeResult.getWarnings() != null && !beforeResult.getWarnings().isEmpty()) {
                log.info("发现的问题:");
                for (String warning : beforeResult.getWarnings()) {
                    log.info("  - {}", warning);
                }
            }
            
            // 2. 执行账户余额修复
            if (beforeResult.getInvalidRecords() > 0) {
                log.info("步骤2: 执行账户余额修复");

                // 获取所有需要修复的账户ID
                List<Long> accountIds = extractInconsistentAccountIds(beforeResult);

                Map<String, Object> repairResult = financeDataValidationService.repairAccountBalanceInconsistency(accountIds);
                log.info("修复执行结果: {}", repairResult);
                
                // 3. 检查修复后状态
                log.info("步骤3: 检查修复后状态");
                ValidationResultVO afterResult = financeDataValidationService.validateAccountBalanceConsistency();
                log.info("修复后状态 - 总记录: {}, 有效: {}, 无效: {}, 准确率: {}%", 
                        afterResult.getTotalRecords(), afterResult.getValidRecords(), 
                        afterResult.getInvalidRecords(), afterResult.getAccuracyRate());
                
                // 4. 生成修复报告
                return generateRepairReport(beforeResult, afterResult, repairResult);
            } else {
                log.info("当前数据状态良好，无需修复");
                return Map.of(
                    "success", true,
                    "message", "数据状态良好，无需修复",
                    "beforeAccuracy", beforeResult.getAccuracyRate(),
                    "afterAccuracy", beforeResult.getAccuracyRate(),
                    "improvement", 0.0
                );
            }
            
        } catch (Exception e) {
            log.error("财务数据修复失败", e);
            return Map.of(
                "success", false,
                "message", "修复失败: " + e.getMessage(),
                "error", e.getClass().getSimpleName()
            );
        }
    }

    /**
     * 生成修复报告
     */
    private Map<String, Object> generateRepairReport(ValidationResultVO before, 
                                                   ValidationResultVO after, 
                                                   Map<String, Object> repairResult) {
        double improvement = after.getAccuracyRate() - before.getAccuracyRate();
        boolean isSuccessful = improvement > 0 && after.getAccuracyRate() >= 95.0;
        
        String message;
        if (isSuccessful) {
            message = String.format("修复成功！准确率从 %.2f%% 提升到 %.2f%%，提升了 %.2f%%", 
                    before.getAccuracyRate(), after.getAccuracyRate(), improvement);
        } else if (improvement > 0) {
            message = String.format("部分修复成功。准确率从 %.2f%% 提升到 %.2f%%，但仍需进一步优化", 
                    before.getAccuracyRate(), after.getAccuracyRate());
        } else {
            message = String.format("修复效果不明显。准确率从 %.2f%% 变为 %.2f%%", 
                    before.getAccuracyRate(), after.getAccuracyRate());
        }
        
        return Map.of(
            "success", isSuccessful,
            "message", message,
            "beforeAccuracy", before.getAccuracyRate(),
            "afterAccuracy", after.getAccuracyRate(),
            "improvement", improvement,
            "beforeInvalidRecords", before.getInvalidRecords(),
            "afterInvalidRecords", after.getInvalidRecords(),
            "repairDetails", repairResult
        );
    }

    /**
     * 快速诊断财务数据问题
     */
    public void quickDiagnosis() {
        log.info("开始快速诊断财务数据问题");
        
        try {
            // 1. 账户余额一致性检查
            ValidationResultVO balanceResult = financeDataValidationService.validateAccountBalanceConsistency();
            log.info("账户余额一致性 - 准确率: {}%, 级别: {}", 
                    balanceResult.getAccuracyRate(), balanceResult.getLevel());
            
            // 2. 实时一致性检查
            Map<String, Object> realtimeResult = financeDataValidationService.realTimeConsistencyCheck();
            log.info("实时一致性检查 - 整体一致性: {}", realtimeResult.get("overallConsistent"));
            
            // 3. 提供诊断建议
            if (balanceResult.getAccuracyRate() < 95.0) {
                log.warn("⚠️ 发现余额一致性问题，建议执行修复");
                log.info("建议执行: financeDataRepairUtil.executeFullRepair()");
            } else {
                log.info("✅ 财务数据状态良好");
            }
            
        } catch (Exception e) {
            log.error("诊断过程中发生异常", e);
        }
    }

    /**
     * 验证修复效果
     */
    public boolean validateRepairEffect() {
        try {
            ValidationResultVO result = financeDataValidationService.validateAccountBalanceConsistency();
            boolean isGood = result.getAccuracyRate() >= 95.0 && result.getInvalidRecords() == 0;

            if (isGood) {
                log.info("✅ 验证通过：财务数据一致性良好，准确率 {}%", result.getAccuracyRate());
            } else {
                log.warn("⚠️ 验证失败：仍存在数据不一致问题，准确率 {}%", result.getAccuracyRate());
            }

            return isGood;
        } catch (Exception e) {
            log.error("验证修复效果时发生异常", e);
            return false;
        }
    }

    /**
     * 从验证结果中提取不一致的账户ID列表
     */
    private List<Long> extractInconsistentAccountIds(ValidationResultVO result) {
        List<Long> accountIds = new ArrayList<>();

        if (result.getWarnings() != null) {
            // 使用正则表达式从警告信息中提取账户ID
            Pattern pattern = Pattern.compile("账户ID (\\d+)");

            for (String warning : result.getWarnings()) {
                Matcher matcher = pattern.matcher(warning);
                if (matcher.find()) {
                    try {
                        Long accountId = Long.parseLong(matcher.group(1));
                        if (!accountIds.contains(accountId)) {
                            accountIds.add(accountId);
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析账户ID失败: {}", matcher.group(1));
                    }
                }
            }
        }

        log.info("从验证结果中提取到 {} 个需要修复的账户ID: {}", accountIds.size(), accountIds);
        return accountIds;
    }
}
