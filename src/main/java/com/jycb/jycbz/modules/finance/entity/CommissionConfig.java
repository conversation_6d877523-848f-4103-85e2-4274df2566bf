package com.jycb.jycbz.modules.finance.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 佣金配置实体类
 */
@Data
@TableName("jy_commission_config")
@Schema(description = "佣金配置")
public class CommissionConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "配置ID")
    @TableField(exist = false)
    private Long configId;

    @Schema(description = "父级配置ID")
    @TableField(exist = false)
    private Long parentConfigId;

    @Schema(description = "配置名称")
    @TableField("name")
    private String name;

    @Schema(description = "配置类型：system-系统 entity-业务主体 partner-合作商 shop-门店")
    @TableField("config_type")
    private String configType;

    @Schema(description = "关联ID")
    @TableField("relation_id")
    private Integer relationId;

    @Schema(description = "业务主体ID")
    @TableField("entity_id")
    private Integer entityId;

    @Schema(description = "合作商ID")
    @TableField("partner_id")
    private Integer partnerId;

    @Schema(description = "门店ID")
    @TableField("shop_id")
    private Integer shopId;

    @Schema(description = "平台分成比例")
    @TableField("platform_rate")
    private BigDecimal platformRate;

    @Schema(description = "业务主体分成比例")
    @TableField("entity_rate")
    private BigDecimal entityRate;

    @Schema(description = "合作商分成比例")
    @TableField("partner_rate")
    private BigDecimal partnerRate;

    @Schema(description = "门店分成比例")
    @TableField("shop_rate")
    private BigDecimal shopRate;

    @Schema(description = "是否使用父级配置：0-否 1-是")
    @TableField("use_parent_config")
    private Integer useParentConfig;

    @Schema(description = "配置层级：0-系统 1-业务主体 2-合作商 3-门店")
    @TableField("level")
    private Integer level;

    @Schema(description = "状态：0-禁用 1-启用")
    @TableField("status")
    private Integer status;

    @Schema(description = "创建人ID")
    @TableField("create_by")
    private Integer createBy;

    @Schema(description = "更新人ID")
    @TableField("update_by")
    private Integer updateBy;

    @Schema(description = "备注")
    @TableField("remark")
    private String remark;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
    
    // 兼容方法，用于解决字段名不匹配问题
    public BigDecimal getPlatformRatio() {
        return this.platformRate;
    }
    
    public void setPlatformRatio(BigDecimal platformRatio) {
        this.platformRate = platformRatio;
    }
    
    public BigDecimal getEntityRatio() {
        return this.entityRate;
    }
    
    public void setEntityRatio(BigDecimal entityRatio) {
        this.entityRate = entityRatio;
    }
    
    public BigDecimal getPartnerRatio() {
        return this.partnerRate;
    }
    
    public void setPartnerRatio(BigDecimal partnerRatio) {
        this.partnerRate = partnerRatio;
    }
    
    public BigDecimal getShopRatio() {
        return this.shopRate;
    }
    
    public void setShopRatio(BigDecimal shopRatio) {
        this.shopRate = shopRatio;
    }
    
    // 兼容方法，用于获取父级配置类型和ID
    @JsonIgnore
    public String getParentConfigType() {
        if ("partner".equals(this.configType)) {
            return "entity";
        } else if ("shop".equals(this.configType)) {
            return "partner";
        }
        return null;
    }
    
    public Long getParentConfigId() {
        return this.parentConfigId;
    }
    
    // 添加接受int参数的方法
    public void setParentConfigId(int parentConfigId) {
        this.parentConfigId = (long) parentConfigId;
    }
    
    public void setParentConfigId(Long parentConfigId) {
        this.parentConfigId = parentConfigId;
    }
    
    public Long getConfigId() {
        return this.id;
    }
    
    public void setConfigId(int configId) {
        this.id = (long) configId;
    }
    
    public void setConfigId(Integer configId) {
        if (configId != null) {
            this.id = configId.longValue();
        } else {
            this.id = null;
        }
    }
} 