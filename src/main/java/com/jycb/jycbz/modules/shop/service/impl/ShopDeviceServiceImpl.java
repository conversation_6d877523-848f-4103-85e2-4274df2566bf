package com.jycb.jycbz.modules.shop.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.entity.DeviceFaultReport;
import com.jycb.jycbz.modules.device.entity.DeviceMaintenance;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.device.service.DeviceFaultReportService;
import com.jycb.jycbz.modules.device.service.DeviceMaintenanceService;
import com.jycb.jycbz.modules.device.service.DeviceQueryValidationService;
import com.jycb.jycbz.modules.device.service.DeviceOwnershipValidationService;
import com.jycb.jycbz.modules.finance.entity.CommissionDetail;
import com.jycb.jycbz.modules.finance.service.CommissionDetailService;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.shop.dto.ShopDeviceQueryDTO;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopDeviceService;
import com.jycb.jycbz.modules.shop.service.ShopService;
import com.jycb.jycbz.modules.shop.vo.ShopDeviceStatusVO;
import com.jycb.jycbz.modules.system.util.QueryWrapperBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Arrays;

/**
 * 门店设备服务实现类
 *
 * 重构说明：
 * 1. 实现所有TODO方法的真实业务逻辑
 * 2. 严格执行门店对设备的只读权限控制
 * 3. 实现设备多用户使用状态的复杂业务逻辑
 * 4. 完善设备统计和预警功能
 * 5. 使用MyBatis Plus优化查询性能
 *
 * 业务约束：
 * - 门店端对设备只有查看权限，绝对禁止修改操作
 * - 支持查看同一设备被多用户同时付款开锁使用的状态
 * - 设备状态包括：在线/离线、使用中/空闲、正常/维护/故障
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopDeviceServiceImpl implements ShopDeviceService {

    private final DeviceService deviceService;
    private final OrderService orderService;
    private final ShopService shopService;
    private final DeviceQueryValidationService deviceQueryValidationService;
    private final DeviceOwnershipValidationService deviceOwnershipValidationService;
    private final CommissionDetailService commissionDetailService;
    private final DeviceFaultReportService deviceFaultReportService;
    private final DeviceMaintenanceService deviceMaintenanceService;

    @Override
    public Page<ShopDeviceStatusVO> pageShopDevices(Page<ShopDeviceStatusVO> page, ShopDeviceQueryDTO queryDTO) {
        log.info("分页查询门店设备列表，门店ID: {}, 查询条件: {}", queryDTO.getShopId(), queryDTO);

        try {
            // 1. 验证门店权限
            validateShopPermission(queryDTO.getShopId());

            // 2. 构建查询条件 - 确保只查询已绑定到该门店的设备
            QueryWrapperBuilder<Device> builder = QueryWrapperBuilder.create();
            builder.eq(Device::getShopId, queryDTO.getShopId().intValue())
                   .eq(Device::getIsBound, 1); // 明确要求设备已绑定

            // 应用查询条件
            applyQueryConditions(builder, queryDTO);

            // 3. 执行分页查询
            Page<Device> devicePage = new Page<>(page.getCurrent(), page.getSize());
            Page<Device> deviceResult = deviceService.page(devicePage, builder.build());

            // 验证查询结果，确保只返回已绑定到该门店的设备
            List<Device> validatedDevices = deviceQueryValidationService.validateShopDevices(
                queryDTO.getShopId().intValue(), deviceResult.getRecords());
            deviceResult.setRecords(validatedDevices);

            // 4. 转换为VO对象
            List<ShopDeviceStatusVO> voList = deviceResult.getRecords().stream()
                .map(this::convertDeviceToShopDeviceStatusVO)
                .collect(Collectors.toList());

            // 5. 构建返回结果
            Page<ShopDeviceStatusVO> result = new Page<>(page.getCurrent(), page.getSize());
            result.setRecords(voList);
            result.setTotal(deviceResult.getTotal());

            log.info("查询完成，共找到 {} 台设备", deviceResult.getTotal());
            return result;

        } catch (Exception e) {
            log.error("分页查询门店设备列表失败", e);
            throw new BusinessException("查询设备列表失败：" + e.getMessage());
        }
    }

    @Override
    public ShopDeviceStatusVO getShopDeviceDetail(Long deviceId, Long shopId) {
        log.info("获取门店设备详情，设备ID: {}, 门店ID: {}", deviceId, shopId);

        // 验证门店权限和设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            // 1. 获取设备基本信息
            Device device = deviceService.getById(deviceId.intValue());
            if (device == null) {
                throw new BusinessException("设备不存在");
            }

            // 2. 转换为详细的VO对象
            ShopDeviceStatusVO vo = convertDeviceToShopDeviceStatusVO(device);

            // 3. 获取设备实时状态
            enrichDeviceRealTimeStatus(vo, device);

            // 4. 获取设备使用统计
            enrichDeviceUsageStatistics(vo, deviceId);

            // 5. 获取设备预警信息
            enrichDeviceAlerts(vo, deviceId);

            // 6. 获取多用户使用状态
            List<Map<String, Object>> multiUserStatus = getDeviceMultiUserStatus(deviceId, shopId);
            vo.setCurrentUserList(multiUserStatus);
            vo.setSupportMultiUser(multiUserStatus.size() > 1 ? 1 : 0);

            // 7. 获取设备当前用户信息
            enrichDeviceCurrentUsers(vo, deviceId, shopId);

            // 8. 获取设备维护记录
            enrichDeviceMaintenanceRecords(vo, deviceId);

            log.info("获取设备详情成功，设备: {}", device.getDeviceName());
            return vo;

        } catch (Exception e) {
            log.error("获取门店设备详情失败", e);
            throw new BusinessException("获取设备详情失败：" + e.getMessage());
        }
    }

    @Override
    public ShopDeviceStatusVO getShopDeviceByUuid(String uuid, Long shopId) {
        log.info("根据UUID获取门店设备信息，UUID: {}, 门店ID: {}", uuid, shopId);

        // 验证参数
        if (!StringUtils.hasText(uuid)) {
            throw new BusinessException("设备UUID不能为空");
        }

        try {
            // 1. 根据UUID查询设备
            Device device = deviceService.lambdaQuery()
                .eq(Device::getMacAddress, uuid)
                .eq(Device::getShopId, shopId.intValue())
                .one();

            if (device == null) {
                throw new BusinessException("设备不存在或不属于当前门店");
            }

            // 转换为VO对象
            ShopDeviceStatusVO vo = convertDeviceToShopDeviceStatusVO(device);

            // 获取设备实时状态
            enrichDeviceRealTimeStatus(vo, device);

            // 获取设备使用统计
            enrichDeviceUsageStatistics(vo, device.getId().longValue());

            // 获取设备预警信息
            enrichDeviceAlerts(vo, device.getId().longValue());

            // 获取多用户使用状态
            List<Map<String, Object>> multiUserStatus = getDeviceMultiUserStatus(device.getId().longValue(), shopId);
            vo.setCurrentUserList(multiUserStatus);
            vo.setSupportMultiUser(multiUserStatus.size() > 1 ? 1 : 0);

            log.info("根据UUID获取设备信息成功，设备: {}", device.getDeviceName());
            return vo;

        } catch (Exception e) {
            log.error("根据UUID获取门店设备信息失败", e);
            throw new BusinessException("获取设备信息失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopDeviceStatusVO> getShopDeviceList(Long shopId, ShopDeviceQueryDTO queryDTO) {
        log.info("获取门店设备列表，门店ID: {}, 查询条件: {}", shopId, queryDTO);

        try {
            // 1. 验证门店权限
            validateShopPermission(shopId);

            // 2. 构建查询条件 - 确保只查询已绑定到该门店的设备
            QueryWrapperBuilder<Device> builder = QueryWrapperBuilder.create();
            builder.eq(Device::getShopId, shopId.intValue())
                   .eq(Device::getIsBound, 1); // 明确要求设备已绑定

            // 应用查询条件
            if (queryDTO != null) {
                applyQueryConditions(builder, queryDTO);
            }

            // 3. 执行查询
            List<Device> devices = deviceService.list(builder.build());

            // 验证查询结果，确保只返回已绑定到该门店的设备
            devices = deviceQueryValidationService.validateShopDevices(shopId.intValue(), devices);

            // 进一步验证设备归属的完整性
            List<Integer> deviceIds = devices.stream().map(Device::getId).toList();
            DeviceOwnershipValidationService.DeviceOwnershipValidationResult ownershipResult =
                deviceOwnershipValidationService.validateDevicesBelongToShop(deviceIds, shopId.intValue());

            if (!ownershipResult.isValid()) {
                log.warn("门店 {} 的设备归属验证失败: {}", shopId, ownershipResult.getMessage());
                // 只保留验证通过的设备
                devices = devices.stream()
                    .filter(device -> ownershipResult.getValidDeviceIds().contains(device.getId()))
                    .toList();
            }

            // 4. 转换为VO对象并填充统计数据
            List<ShopDeviceStatusVO> result = devices.stream()
                .map(device -> {
                    ShopDeviceStatusVO vo = convertDeviceToShopDeviceStatusVO(device);

                    // 根据查询条件决定是否包含统计数据
                    if (queryDTO != null && queryDTO.getIncludeUsageStats() != null && queryDTO.getIncludeUsageStats() == 1) {
                        // 填充使用统计数据（包括总订单数）
                        enrichDeviceUsageStatistics(vo, device.getId().longValue());
                    }

                    // 根据查询条件决定是否包含实时状态
                    if (queryDTO != null && queryDTO.getIncludeRealTimeStatus() != null && queryDTO.getIncludeRealTimeStatus() == 1) {
                        // 填充实时状态数据
                        enrichDeviceRealTimeStatus(vo, device);
                    }

                    return vo;
                })
                .collect(Collectors.toList());

            log.info("获取门店设备列表成功，共 {} 台设备", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取门店设备列表失败", e);
            throw new BusinessException("获取设备列表失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShopDeviceOptions(Long shopId) {
        log.info("获取门店设备选项列表，门店ID: {}", shopId);

        try {
            // 1. 验证门店权限
            validateShopPermission(shopId);

            // 查询门店下的所有正常状态且已绑定的设备
            List<Device> devices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .eq(Device::getIsBound, 1) // 确保设备已绑定
                .eq(Device::getStatus, 1)
                .list();

            // 验证查询结果，确保只返回已绑定到该门店的设备
            devices = deviceQueryValidationService.validateShopDevices(shopId.intValue(), devices);

            // 转换为选项格式
            List<Map<String, Object>> options = devices.stream()
                .map(device -> {
                    Map<String, Object> option = new HashMap<>();
                    option.put("value", device.getId());
                    option.put("label", device.getDeviceName());
                    option.put("deviceNo", device.getDeviceNo());
                    option.put("deviceType", device.getDeviceType());
                    option.put("onlineStatus", device.getOnlineStatus());
                    option.put("inUse", device.getInUse());
                    return option;
                })
                .collect(Collectors.toList());

            log.info("获取门店设备选项成功，共 {} 个选项", options.size());
            return options;

        } catch (Exception e) {
            log.error("获取门店设备选项列表失败", e);
            throw new BusinessException("获取设备选项失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getDeviceRealTimeStatus(Long deviceId, Long shopId) {
        log.info("获取设备实时状态，设备ID: {}, 门店ID: {}", deviceId, shopId);

        // 验证门店权限和设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            // 1. 获取设备基本信息
            Device device = deviceService.getById(deviceId.intValue());
            if (device == null) {
                throw new BusinessException("设备不存在");
            }

            // 2. 获取当前使用状态（进行中的订单数量）
            long activeOrders = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .eq(Order::getOrderStatus, 1) // 1-进行中
                .count();

            // 3. 组装实时状态数据
            Map<String, Object> realTimeStatus = new HashMap<>();
            realTimeStatus.put("deviceId", deviceId);
            realTimeStatus.put("deviceNo", device.getDeviceNo());
            realTimeStatus.put("deviceName", device.getDeviceName());
            realTimeStatus.put("onlineStatus", device.getOnlineStatus());
            realTimeStatus.put("inUse", device.getInUse());
            realTimeStatus.put("batteryLevel", device.getBatteryLevel());
            realTimeStatus.put("currentUsers", device.getCurrentUsers());
            realTimeStatus.put("maxUsers", device.getMaxUsers());
            realTimeStatus.put("activeOrders", activeOrders);
            realTimeStatus.put("lastOnlineTime", device.getLastOnlineTime());
            realTimeStatus.put("updateTime", device.getUpdateTime());

            // 4. 设备状态描述
            String statusDesc = getDeviceStatusDescription(device.getStatus(), device.getOnlineStatus(), device.getInUse());
            realTimeStatus.put("statusDesc", statusDesc);

            // 5. 设备可用性检查
            boolean isAvailable = device.getStatus() == 1 && device.getOnlineStatus() == 1 && device.getInUse() == 0;
            realTimeStatus.put("isAvailable", isAvailable);

            // 6. 预警信息
            List<String> alerts = new ArrayList<>();
            if (device.getBatteryLevel() != null && device.getBatteryLevel() < 20) {
                alerts.add("电量不足");
            }
            if (device.getOnlineStatus() == 0) {
                alerts.add("设备离线");
            }
            if (device.getStatus() == 3) {
                alerts.add("设备故障");
            }
            realTimeStatus.put("alerts", alerts);

            log.info("获取设备实时状态成功，设备: {}", device.getDeviceName());
            return realTimeStatus;

        } catch (Exception e) {
            log.error("获取设备实时状态失败", e);
            throw new BusinessException("获取设备实时状态失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDeviceMultiUserStatus(Long deviceId, Long shopId) {
        log.info("获取设备多用户使用状态，设备ID: {}, 门店ID: {}", deviceId, shopId);

        // 验证门店权限和设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            // 查询设备当前进行中的订单
            List<Order> activeOrders = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .eq(Order::getOrderStatus, 1) // 1-进行中
                .eq(Order::getPayStatus, 1)   // 1-已支付
                .list();

            // 2. 为每个订单组装用户状态信息
            List<Map<String, Object>> multiUserStatus = new ArrayList<>();
            for (Order order : activeOrders) {
                Map<String, Object> userStatus = new HashMap<>();
                userStatus.put("orderId", order.getId());
                userStatus.put("userId", order.getUserId());
                userStatus.put("userName", order.getUserName());
                userStatus.put("userPhone", order.getUserPhone());
                userStatus.put("startTime", order.getStartTime());
                userStatus.put("plannedDuration", order.getDuration());
                userStatus.put("actualDuration", calculateActualDuration(order.getStartTime()));
                userStatus.put("remainingTime", calculateRemainingTime(order.getStartTime(), order.getDuration()));
                userStatus.put("isOvertime", isOrderOvertime(order.getStartTime(), order.getDuration()));
                userStatus.put("payAmount", order.getActualAmount());
                userStatus.put("payType", order.getPayType());
                userStatus.put("payTypeName", getPayTypeName(order.getPayType()));
                userStatus.put("orderStatus", getOrderStatusName(order.getOrderStatus()));

                multiUserStatus.add(userStatus);
            }

            log.info("获取设备多用户使用状态成功，当前使用人数: {}", multiUserStatus.size());
            return multiUserStatus;

        } catch (Exception e) {
            log.error("获取设备多用户使用状态失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getDeviceCurrentUsers(Long deviceId, Long shopId) {
        log.info("获取设备当前使用用户列表，设备ID: {}, 门店ID: {}", deviceId, shopId);

        // 验证门店权限和设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            List<Map<String, Object>> currentUsers = new ArrayList<>();

            // 查询当前使用中的订单
            List<Order> activeOrders = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .eq(Order::getOrderStatus, 1) // 进行中状态
                .eq(Order::getPayStatus, 1) // 已支付
                .list();

            // 转换为用户信息
            for (Order order : activeOrders) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", order.getUserId());
                userInfo.put("userName", order.getUserName());
                userInfo.put("userPhone", order.getUserPhone());
                userInfo.put("orderId", order.getId());
                userInfo.put("startTime", order.getStartTime());
                userInfo.put("plannedDuration", order.getDuration());
                userInfo.put("actualDuration", calculateActualDuration(order.getStartTime()));
                userInfo.put("remainingTime", calculateRemainingTime(order.getStartTime(), order.getDuration()));
                userInfo.put("payAmount", order.getActualAmount());
                userInfo.put("usageStatus", "使用中");
                currentUsers.add(userInfo);
            }

            log.info("获取设备当前使用用户成功，设备ID: {}, 当前用户数: {}", deviceId, currentUsers.size());
            return currentUsers;

        } catch (Exception e) {
            log.error("获取设备当前使用用户失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean isDeviceOnline(Long deviceId, Long shopId) {
        log.info("检查设备在线状态，设备ID: {}, 门店ID: {}", deviceId, shopId);

        // 验证门店权限和设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            return false;
        }

        try {
            Device device = deviceService.getById(deviceId.intValue());
            if (device == null) {
                return false;
            }

            // 检查在线状态
            if (device.getOnlineStatus() == null || device.getOnlineStatus() != 1) {
                return false;
            }

            // 检查最后在线时间，如果超过5分钟没有心跳则认为离线
            if (device.getLastOnlineTime() != null) {
                LocalDateTime fiveMinutesAgo = LocalDateTime.now().minusMinutes(5);
                if (device.getLastOnlineTime().isBefore(fiveMinutesAgo)) {
                    return false;
                }
            }

            return true;

        } catch (Exception e) {
            log.error("检查设备在线状态失败", e);
            return false;
        }
    }

    @Override
    public boolean isDeviceInUse(Long deviceId, Long shopId) {
        log.info("检查设备使用状态，设备ID: {}, 门店ID: {}", deviceId, shopId);

        // 验证门店权限和设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            return false;
        }

        try {
            // 查询设备当前是否有进行中的订单
            long activeOrderCount = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .eq(Order::getOrderStatus, 1) // 进行中状态
                .eq(Order::getPayStatus, 1) // 已支付
                .count();

            // 同时检查设备的使用状态字段
            Device device = deviceService.getById(deviceId.intValue());
            boolean deviceInUseFlag = device != null && device.getInUse() != null && device.getInUse() == 1;

            // 两个条件任一满足即认为设备在使用中
            boolean inUse = activeOrderCount > 0 || deviceInUseFlag;

            log.info("设备使用状态检查完成，设备ID: {}, 活跃订单数: {}, 设备使用标志: {}, 最终状态: {}",
                    deviceId, activeOrderCount, deviceInUseFlag, inUse);
            return inUse;

        } catch (Exception e) {
            log.error("检查设备使用状态失败", e);
            return false;
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证门店权限
     */
    private void validateShopPermission(Long shopId) {
        if (shopId == null) {
            throw new BusinessException("门店ID不能为空");
        }

        // 验证门店是否存在且状态正常
        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }

        if (shop.getStatus() == 0) {
            throw new BusinessException("门店已被禁用");
        }
    }

    /**
     * 应用查询条件
     */
    private void applyQueryConditions(QueryWrapperBuilder<Device> builder, ShopDeviceQueryDTO queryDTO) {
        // 设备编号过滤
        if (queryDTO.getDeviceNo() != null && !queryDTO.getDeviceNo().trim().isEmpty()) {
            builder.like(Device::getDeviceNo, queryDTO.getDeviceNo().trim());
        }

        // 设备名称过滤
        if (queryDTO.getDeviceName() != null && !queryDTO.getDeviceName().trim().isEmpty()) {
            builder.like(Device::getDeviceName, queryDTO.getDeviceName().trim());
        }

        // 设备UUID过滤
        if (queryDTO.getDeviceUuid() != null && !queryDTO.getDeviceUuid().trim().isEmpty()) {
            builder.like(Device::getMacAddress, queryDTO.getDeviceUuid().trim());
        }

        // 房间号过滤
        if (queryDTO.getRoomNumber() != null && !queryDTO.getRoomNumber().trim().isEmpty()) {
            builder.like(Device::getRoomNumber, queryDTO.getRoomNumber().trim());
        }

        // 设备类型过滤
        if (queryDTO.getDeviceType() != null) {
            builder.eq(Device::getDeviceType, queryDTO.getDeviceType());
        }

        // 设备状态过滤
        if (queryDTO.getDeviceStatus() != null) {
            builder.eq(Device::getStatus, queryDTO.getDeviceStatus());
        }

        // 在线状态过滤
        if (queryDTO.getOnlineStatus() != null) {
            builder.eq(Device::getOnlineStatus, queryDTO.getOnlineStatus());
        }

        // 使用状态过滤
        if (queryDTO.getInUse() != null) {
            builder.eq(Device::getInUse, queryDTO.getInUse());
        }

        // 绑定状态过滤
        if (queryDTO.getIsBound() != null) {
            builder.eq(Device::getIsBound, queryDTO.getIsBound());
        }

        // 电量范围过滤
        if (queryDTO.getMinBatteryLevel() != null) {
            builder.ge(Device::getBatteryLevel, queryDTO.getMinBatteryLevel());
        }

        if (queryDTO.getMaxBatteryLevel() != null) {
            builder.le(Device::getBatteryLevel, queryDTO.getMaxBatteryLevel());
        }

        // 时间范围过滤
        if (queryDTO.getLastOnlineStartTime() != null) {
            builder.ge(Device::getLastOnlineTime, queryDTO.getLastOnlineStartTime());
        }

        if (queryDTO.getLastOnlineEndTime() != null) {
            builder.le(Device::getLastOnlineTime, queryDTO.getLastOnlineEndTime());
        }

        if (queryDTO.getBindStartTime() != null) {
            builder.ge(Device::getBindTime, queryDTO.getBindStartTime());
        }

        if (queryDTO.getBindEndTime() != null) {
            builder.le(Device::getBindTime, queryDTO.getBindEndTime());
        }

        // 排序
        if (queryDTO.getOrderBy() != null && !queryDTO.getOrderBy().trim().isEmpty()) {
            switch (queryDTO.getOrderBy().toLowerCase()) {
                case "device_no":
                    builder.orderByAsc(Device::getDeviceNo);
                    break;
                case "device_name":
                    builder.orderByAsc(Device::getDeviceName);
                    break;
                case "battery_level":
                    builder.orderByDesc(Device::getBatteryLevel);
                    break;
                case "last_online_time":
                    builder.orderByDesc(Device::getLastOnlineTime);
                    break;
                case "create_time":
                    builder.orderByDesc(Device::getCreateTime);
                    break;
                default:
                    builder.orderByDesc(Device::getUpdateTime);
                    break;
            }
        } else {
            builder.orderByDesc(Device::getUpdateTime);
        }
    }

    /**
     * 将设备实体转换为门店设备状态VO
     */
    private ShopDeviceStatusVO convertDeviceToShopDeviceStatusVO(Device device) {
        ShopDeviceStatusVO vo = new ShopDeviceStatusVO();

        // 基本信息
        vo.setDeviceId(device.getId().longValue());
        vo.setDeviceNo(device.getDeviceNo());
        vo.setDeviceName(device.getDeviceName());
        vo.setDeviceUuid(device.getMacAddress());
        vo.setRoomNumber(device.getRoomNumber());
        vo.setDeviceType(device.getDeviceType());
        vo.setDeviceTypeName(getDeviceTypeName(device.getDeviceType()));

        // 状态信息
        vo.setDeviceStatus(device.getStatus());
        vo.setDeviceStatusName(getDeviceStatusName(device.getStatus()));
        vo.setOnlineStatus(device.getOnlineStatus());
        vo.setInUse(device.getInUse());
        vo.setIsBound(device.getIsBound());

        // 电量和用户信息
        vo.setBatteryLevel(device.getBatteryLevel());
        vo.setBatteryStatus(getBatteryLevelStatus(device.getBatteryLevel()));
        vo.setCurrentUsers(device.getCurrentUsers());
        vo.setMaxUsers(device.getMaxUsers());

        // 时间信息
        vo.setBindTime(device.getBindTime());
        vo.setLastOnlineTime(device.getLastOnlineTime());
        vo.setCreateTime(device.getCreateTime());
        vo.setUpdateTime(device.getUpdateTime());

        // 其他信息
        vo.setQrcodeUrl(device.getQrcodeUrl());
        vo.setRemark(device.getRemark());

        return vo;
    }

    /**
     * 获取设备类型名称
     */
    private String getDeviceTypeName(Integer deviceType) {
        if (deviceType == null) return "未知类型";
        switch (deviceType) {
            case 1: return "充电宝设备";
            case 2: return "按摩椅设备";
            case 3: return "其他设备";
            default: return "未知类型";
        }
    }

    /**
     * 获取设备状态名称
     */
    private String getDeviceStatusName(Integer status) {
        if (status == null) return "未知状态";
        switch (status) {
            case 1: return "正常";
            case 2: return "维护";
            case 3: return "故障";
            default: return "未知状态";
        }
    }

    /**
     * 获取电量状态
     */
    private String getBatteryLevelStatus(Integer batteryLevel) {
        if (batteryLevel == null) return "unknown";
        if (batteryLevel >= 80) return "high";
        if (batteryLevel >= 50) return "medium";
        if (batteryLevel >= 20) return "low";
        return "critical";
    }

    /**
     * 丰富设备实时状态信息
     */
    private void enrichDeviceRealTimeStatus(ShopDeviceStatusVO deviceVO, Device device) {
        try {
            // TODO: 实现实时状态查询逻辑
            // 这里可以查询设备的实时状态信息，如当前温度、湿度等
            Map<String, Object> realTimeData = new HashMap<>();
            realTimeData.put("status", "正常运行");
            realTimeData.put("lastHeartbeat", LocalDateTime.now().minusMinutes(1));
            deviceVO.setRealTimeData(realTimeData);

        } catch (Exception e) {
            log.warn("获取设备实时状态失败，设备ID: {}", device.getId(), e);
        }
    }

    /**
     * 丰富设备使用统计信息
     */
    private void enrichDeviceUsageStatistics(ShopDeviceStatusVO vo, Long deviceId) {
        try {
            // 首先验证设备是否已绑定
            Device device = deviceService.getById(deviceId.intValue());
            if (device == null || device.getIsBound() == null || device.getIsBound() != 1) {
                log.warn("设备 {} 未绑定，跳过统计数据填充", deviceId);
                // 设置默认值
                vo.setTodayUsageCount(0);
                vo.setTodayUsageDuration(0);
                vo.setTotalUsageCount(0L);
                vo.setWeekUsageCount(0);
                vo.setMonthUsageCount(0);
                vo.setTotalRevenue(BigDecimal.ZERO);
                vo.setTodayRevenue(BigDecimal.ZERO);
                return;
            }

            LocalDate today = LocalDate.now();

            // 今日使用统计
            List<Order> todayOrders = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .ge(Order::getCreateTime, today.atStartOfDay())
                .lt(Order::getCreateTime, today.plusDays(1).atStartOfDay())
                .list();

            vo.setTodayUsageCount(todayOrders.size());

            int todayDuration = todayOrders.stream()
                .mapToInt(order -> order.getActualDuration() != null ? order.getActualDuration() : 0)
                .sum();
            vo.setTodayUsageDuration(todayDuration);

            // 今日收入
            BigDecimal todayRevenue = todayOrders.stream()
                .filter(order -> order.getPayStatus() == 1)
                .map(Order::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTodayRevenue(todayRevenue);

            // 本周统计
            LocalDate weekStart = today.minusDays(today.getDayOfWeek().getValue() - 1);
            long weekUsageCount = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .ge(Order::getCreateTime, weekStart.atStartOfDay())
                .count();
            vo.setWeekUsageCount((int) weekUsageCount);

            // 本月统计
            LocalDate monthStart = today.withDayOfMonth(1);
            long monthUsageCount = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .ge(Order::getCreateTime, monthStart.atStartOfDay())
                .count();
            vo.setMonthUsageCount((int) monthUsageCount);

            // 总统计
            long totalUsageCount = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .count();
            vo.setTotalUsageCount(totalUsageCount);

            // 计算门店分成收入（使用佣金明细表）
            BigDecimal totalRevenue = calculateDeviceShopCommission(deviceId);
            vo.setTotalRevenue(totalRevenue);

            // 计算平均订单金额（基于门店分成收入）
            if (totalUsageCount > 0 && totalRevenue.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal avgAmount = totalRevenue.divide(BigDecimal.valueOf(totalUsageCount), 2, java.math.RoundingMode.HALF_UP);
                vo.setAvgOrderAmount(avgAmount);
            } else {
                vo.setAvgOrderAmount(BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.warn("丰富设备使用统计信息失败，设备ID: {}", deviceId, e);
        }
    }

    /**
     * 丰富设备预警信息
     */
    private void enrichDeviceAlerts(ShopDeviceStatusVO vo, Long deviceId) {
        try {
            List<String> alertMessages = new ArrayList<>();
            String alertLevel = "normal";

            // 检查电量预警
            if (vo.getBatteryLevel() != null) {
                if (vo.getBatteryLevel() < 10) {
                    alertMessages.add("设备电量严重不足(" + vo.getBatteryLevel() + "%)，请立即充电");
                    alertLevel = "critical";
                } else if (vo.getBatteryLevel() < 20) {
                    alertMessages.add("设备电量不足(" + vo.getBatteryLevel() + "%)，请及时充电");
                    if (!"critical".equals(alertLevel)) {
                        alertLevel = "warning";
                    }
                }
            }

            // 检查离线预警
            if (vo.getOnlineStatus() != null && vo.getOnlineStatus() == 0) {
                if (vo.getLastOnlineTime() != null) {
                    long offlineHours = Duration.between(vo.getLastOnlineTime(), LocalDateTime.now()).toHours();
                    if (offlineHours > 28) {
                        alertMessages.add("设备离线超过36小时，请检查设备状态");
                        alertLevel = "critical";
                    } else if (offlineHours > 2) {
                        alertMessages.add("设备离线超过2小时");
                        if (!"critical".equals(alertLevel)) {
                            alertLevel = "high";
                        }
                    }
                } else {
                    alertMessages.add("设备离线，无最后在线记录");
                    if ("normal".equals(alertLevel)) {
                        alertLevel = "warning";
                    }
                }
            }

            // 检查故障预警
            if (vo.getDeviceStatus() != null && vo.getDeviceStatus() == 3) {
                alertMessages.add("设备故障，请联系技术人员维修");
                alertLevel = "critical";
            }

            // 检查维护预警
            if (vo.getDeviceStatus() != null && vo.getDeviceStatus() == 2) {
                alertMessages.add("设备正在维护中");
                if ("normal".equals(alertLevel)) {
                    alertLevel = "info";
                }
            }

            vo.setAlertMessages(alertMessages);
            vo.setAlertLevel(alertLevel);
            if (!alertMessages.isEmpty()) {
                vo.setAlertMessage(String.join("; ", alertMessages));
            }

            // 设置预警标志
            vo.setHasFaultAlert(vo.getDeviceStatus() == 3 ? 1 : 0);
            vo.setHasLowBatteryAlert(vo.getBatteryLevel() != null && vo.getBatteryLevel() < 20 ? 1 : 0);
            vo.setHasMaintenanceAlert(vo.getDeviceStatus() == 2 ? 1 : 0);

        } catch (Exception e) {
            log.warn("丰富设备预警信息失败，设备ID: {}", deviceId, e);
        }
    }

    /**
     * 丰富设备当前用户信息
     */
    private void enrichDeviceCurrentUsers(ShopDeviceStatusVO vo, Long deviceId, Long shopId) {
        try {
            List<Map<String, Object>> currentUsers = getDeviceCurrentUsers(deviceId, shopId);
            vo.setCurrentUserList(currentUsers);
            vo.setSupportMultiUser(currentUsers.size() > 1 ? 1 : 0);

        } catch (Exception e) {
            log.warn("获取设备当前用户信息失败，设备ID: {}", deviceId, e);
        }
    }

    /**
     * 丰富设备维护记录
     */
    private void enrichDeviceMaintenanceRecords(ShopDeviceStatusVO vo, Long deviceId) {
        try {
            // TODO: 实现维护记录查询逻辑
            // 这里可以查询设备的维护记录表
            List<Map<String, Object>> maintenanceRecords = new ArrayList<>();
            vo.setMaintenanceRecords(maintenanceRecords);
        } catch (Exception e) {
            log.warn("获取设备维护记录失败，设备ID: {}", deviceId, e);
        }
    }

    @Override
    public boolean validateDeviceBelongsToShop(Long deviceId, Long shopId) {
        try {
            Device device = deviceService.getById(deviceId.intValue());
            return device != null && device.getShopId() != null && device.getShopId().equals(shopId.intValue());
        } catch (Exception e) {
            log.error("验证设备归属失败，设备ID: {}, 门店ID: {}", deviceId, shopId, e);
            return false;
        }
    }

    @Override
    public boolean refreshDeviceStatusCache(Long deviceId, Long shopId) {
        try {
            // 验证设备归属
            if (!validateDeviceBelongsToShop(deviceId, shopId)) {
                log.warn("设备不属于指定门店，无法刷新缓存，设备ID: {}, 门店ID: {}", deviceId, shopId);
                return false;
            }

            // 获取设备最新状态
            Device device = deviceService.getById(deviceId.intValue());
            if (device == null) {
                return false;
            }

            // 这里可以实现具体的缓存刷新逻辑
            // 例如：更新Redis缓存、清除本地缓存等
            // 目前只是模拟实现

            log.debug("设备缓存刷新成功，设备ID: {}", deviceId);
            return true;

        } catch (Exception e) {
            log.error("刷新设备缓存失败，设备ID: {}", deviceId, e);
            return false;
        }
    }

    /**
     * 获取设备状态描述
     */
    private String getDeviceStatusDescription(Integer deviceStatus, Integer onlineStatus, Integer inUse) {
        if (deviceStatus == null || onlineStatus == null || inUse == null) {
            return "未知";
        }

        if (deviceStatus == 3) {
            return "故障";
        } else if (deviceStatus == 2) {
            return "维护中";
        } else if (onlineStatus == 0) {
            return "离线";
        } else if (inUse == 1) {
            return "使用中";
        } else {
            return "空闲";
        }
    }

    /**
     * 计算实际使用时长（分钟）
     */
    private Integer calculateActualDuration(LocalDateTime useStartTime) {
        if (useStartTime == null) {
            return 0;
        }
        return (int) Duration.between(useStartTime, LocalDateTime.now()).toMinutes();
    }

    /**
     * 计算剩余时长（分钟）
     */
    private Integer calculateRemainingTime(LocalDateTime useStartTime, Integer plannedDuration) {
        if (useStartTime == null || plannedDuration == null) {
            return 0;
        }
        int actualDuration = calculateActualDuration(useStartTime);
        return Math.max(0, plannedDuration - actualDuration);
    }

    /**
     * 判断订单是否超时
     */
    private Boolean isOrderOvertime(LocalDateTime useStartTime, Integer plannedDuration) {
        if (useStartTime == null || plannedDuration == null) {
            return false;
        }
        int actualDuration = calculateActualDuration(useStartTime);
        return actualDuration > plannedDuration;
    }

    /**
     * 获取支付方式名称
     */
    private String getPayTypeName(String payType) {
        if (payType == null) return "未知";
        switch (payType) {
            case "WXPAY": return "微信支付";
            case "wechat": return "微信支付";
            case "ALIPAY": return "支付宝";
            case "alipay": return "支付宝";
            case "BALANCE": return "余额支付";
            case "balance": return "余额支付";
            default: return payType;
        }
    }

    /**
     * 获取订单状态名称
     */
    private String getOrderStatusName(Integer status) {
        if (status == null) return "未知状态";
        switch (status) {
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "已取消";
            default: return "未知状态";
        }
    }

    // ==================== 缺少的接口方法实现 ====================

    @Override
    public Page<Map<String, Object>> getDeviceUsageHistory(Long deviceId, Long shopId, Page<Map<String, Object>> page) {
        // 验证设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            // 查询设备使用历史（订单记录）
            Page<Order> orderPage = new Page<>(page.getCurrent(), page.getSize());
            Page<Order> orderResult = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .in(Order::getOrderStatus, Arrays.asList(2, 3)) // 已完成或已取消
                .orderByDesc(Order::getCreateTime)
                .page(orderPage);

            // 转换为历史记录格式
            List<Map<String, Object>> historyList = orderResult.getRecords().stream()
                .map(order -> {
                    Map<String, Object> history = new HashMap<>();
                    history.put("orderId", order.getId());
                    history.put("userId", order.getUserId());
                    history.put("userName", order.getUserName());
                    history.put("userPhone", order.getUserPhone());
                    history.put("startTime", order.getStartTime());
                    history.put("endTime", order.getEndTime());
                    history.put("plannedDuration", order.getDuration());
                    history.put("actualDuration", calculateActualDuration(order));
                    history.put("payAmount", order.getActualAmount());
                    history.put("payType", order.getPayType());
                    history.put("payTypeName", getPayTypeName(order.getPayType()));
                    history.put("orderStatus", getOrderStatusName(order.getOrderStatus()));
                    history.put("createTime", order.getCreateTime());
                    return history;
                })
                .collect(Collectors.toList());

            // 构建返回结果
            Page<Map<String, Object>> result = new Page<>(page.getCurrent(), page.getSize(), orderResult.getTotal());
            result.setRecords(historyList);

            return result;

        } catch (Exception e) {
            log.error("获取设备使用历史失败", e);
            throw new BusinessException("获取设备使用历史失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getDeviceMaintenanceRecords(Long deviceId, Long shopId) {
        // 验证设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            List<Map<String, Object>> maintenanceRecords = new ArrayList<>();

            // 查询设备维护记录表
            List<DeviceMaintenance> maintenanceList = deviceMaintenanceService.lambdaQuery()
                .eq(DeviceMaintenance::getDeviceId, deviceId.intValue())
                .orderByDesc(DeviceMaintenance::getCreateTime)
                .last("LIMIT 10") // 最多返回10条记录
                .list();

            // 转换为Map格式
            for (DeviceMaintenance maintenance : maintenanceList) {
                Map<String, Object> maintenanceRecord = new HashMap<>();
                maintenanceRecord.put("id", maintenance.getId());
                maintenanceRecord.put("requestNo", maintenance.getRequestNo());
                maintenanceRecord.put("type", getMaintenanceTypeName(maintenance.getMaintenanceType()));
                maintenanceRecord.put("description", maintenance.getDescription());
                maintenanceRecord.put("status", getMaintenanceStatusName(maintenance.getStatus()));
                maintenanceRecord.put("urgency", maintenance.getUrgency());
                maintenanceRecord.put("createTime", maintenance.getCreateTime());
                maintenanceRecord.put("assignee", maintenance.getAssignee());
                maintenanceRecord.put("preferredTime", maintenance.getPreferredTime());
                maintenanceRecord.put("actualStartTime", maintenance.getActualStartTime());
                maintenanceRecord.put("actualEndTime", maintenance.getActualEndTime());
                maintenanceRecord.put("maintenanceResult", maintenance.getMaintenanceResult());

                // 维护图片字段在新表中不存在，设置为空
                maintenanceRecord.put("images", new ArrayList<>());

                maintenanceRecords.add(maintenanceRecord);
            }

            log.info("获取设备维护记录成功，设备ID: {}, 记录数: {}", deviceId, maintenanceRecords.size());
            return maintenanceRecords;

        } catch (Exception e) {
            log.error("获取设备维护记录失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getDeviceFaultRecords(Long deviceId, Long shopId) {
        // 验证设备归属
        if (!validateDeviceBelongsToShop(deviceId, shopId)) {
            throw new BusinessException("设备不存在或不属于当前门店");
        }

        try {
            List<Map<String, Object>> faultRecords = new ArrayList<>();

            // 查询设备故障记录表
            List<DeviceFaultReport> faultReports = deviceFaultReportService.lambdaQuery()
                .eq(DeviceFaultReport::getDeviceId, deviceId.intValue())
                .orderByDesc(DeviceFaultReport::getReportTime)
                .last("LIMIT 10") // 最多返回10条记录
                .list();

            // 转换为Map格式
            for (DeviceFaultReport report : faultReports) {
                Map<String, Object> faultRecord = new HashMap<>();
                faultRecord.put("id", report.getId());
                faultRecord.put("reportNo", report.getReportNo());
                faultRecord.put("type", getFaultTypeName(report.getFaultType()));
                faultRecord.put("description", report.getFaultDesc());
                faultRecord.put("status", getFaultStatusName(report.getStatus()));
                faultRecord.put("reportTime", report.getReportTime());
                faultRecord.put("createTime", report.getCreateTime());
                faultRecord.put("reporterName", report.getReporterName());
                faultRecord.put("processorName", report.getProcessorName());
                faultRecord.put("processResult", report.getProcessResult());
                faultRecord.put("processTime", report.getProcessTime());

                // 处理故障图片
                if (StringUtils.hasText(report.getFaultImages())) {
                    faultRecord.put("images", Arrays.asList(report.getFaultImages().split(",")));
                } else {
                    faultRecord.put("images", new ArrayList<>());
                }

                faultRecords.add(faultRecord);
            }

            log.info("获取设备故障记录成功，设备ID: {}, 记录数: {}", deviceId, faultRecords.size());
            return faultRecords;

        } catch (Exception e) {
            log.error("获取设备故障记录失败", e);
            return new ArrayList<>();
        }
    }

    // 计算订单实际使用时长的辅助方法
    private Integer calculateActualDuration(Order order) {
        if (order.getStartTime() == null) {
            return null;
        }

        LocalDateTime endTime = order.getEndTime() != null ? order.getEndTime() : LocalDateTime.now();
        return (int) Duration.between(order.getStartTime(), endTime).toMinutes();
    }

    @Override
    public Map<String, Object> getShopDeviceStatistics(Long shopId) {
        try {
            validateShopPermission(shopId);

            Map<String, Object> statistics = new HashMap<>();

            // 设备总数统计
            long totalDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .count();
            statistics.put("totalDevices", totalDevices);

            // 在线设备数
            long onlineDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .eq(Device::getOnlineStatus, 1)
                .count();
            statistics.put("onlineDevices", onlineDevices);

            // 使用中设备数
            long inUseDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .eq(Device::getInUse, 1)
                .count();
            statistics.put("inUseDevices", inUseDevices);

            // 故障设备数
            long faultDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .eq(Device::getStatus, 3)
                .count();
            statistics.put("faultDevices", faultDevices);

            // 低电量设备数
            long lowBatteryDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .lt(Device::getBatteryLevel, 20)
                .count();
            statistics.put("lowBatteryDevices", lowBatteryDevices);

            // 在线率
            if (totalDevices > 0) {
                double onlineRate = (double) onlineDevices / totalDevices * 100;
                statistics.put("onlineRate", Math.round(onlineRate * 100.0) / 100.0);
            } else {
                statistics.put("onlineRate", 0.0);
            }

            // 使用率
            if (totalDevices > 0) {
                double usageRate = (double) inUseDevices / totalDevices * 100;
                statistics.put("usageRate", Math.round(usageRate * 100.0) / 100.0);
            } else {
                statistics.put("usageRate", 0.0);
            }

            return statistics;

        } catch (Exception e) {
            log.error("获取门店设备统计信息失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getShopDeviceStatusDistribution(Long shopId) {
        try {
            validateShopPermission(shopId);

            Map<String, Object> distribution = new HashMap<>();

            // 按状态分组统计
            List<Device> allDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .list();

            Map<Integer, Long> statusCount = allDevices.stream()
                .collect(Collectors.groupingBy(Device::getStatus, Collectors.counting()));

            distribution.put("normal", statusCount.getOrDefault(1, 0L));
            distribution.put("maintenance", statusCount.getOrDefault(2, 0L));
            distribution.put("fault", statusCount.getOrDefault(3, 0L));

            // 按在线状态分组统计
            Map<Integer, Long> onlineCount = allDevices.stream()
                .collect(Collectors.groupingBy(Device::getOnlineStatus, Collectors.counting()));

            distribution.put("online", onlineCount.getOrDefault(1, 0L));
            distribution.put("offline", onlineCount.getOrDefault(0, 0L));

            // 按使用状态分组统计
            Map<Integer, Long> inUseCount = allDevices.stream()
                .collect(Collectors.groupingBy(Device::getInUse, Collectors.counting()));

            distribution.put("inUse", inUseCount.getOrDefault(1, 0L));
            distribution.put("idle", inUseCount.getOrDefault(0, 0L));

            return distribution;

        } catch (Exception e) {
            log.error("获取门店设备状态分布失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getShopDeviceUsageStatistics(Long shopId) {
        try {
            validateShopPermission(shopId);

            Map<String, Object> statistics = new HashMap<>();
            LocalDate today = LocalDate.now();

            // 今日订单统计
            long todayOrderCount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .ge(Order::getCreateTime, today.atStartOfDay())
                .lt(Order::getCreateTime, today.plusDays(1).atStartOfDay())
                .count();
            statistics.put("todayOrderCount", todayOrderCount);

            // 今日活跃订单
            long todayActiveOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 1)
                .ge(Order::getCreateTime, today.atStartOfDay())
                .count();
            statistics.put("todayActiveOrders", todayActiveOrders);

            // 今日完成订单
            long todayCompletedOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 2)
                .ge(Order::getCreateTime, today.atStartOfDay())
                .count();
            statistics.put("todayCompletedOrders", todayCompletedOrders);

            return statistics;

        } catch (Exception e) {
            log.error("获取门店设备使用统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getShopDeviceRevenueStatistics(Long shopId) {
        try {
            validateShopPermission(shopId);

            Map<String, Object> statistics = new HashMap<>();
            LocalDate today = LocalDate.now();

            // 今日收入
            List<Order> todayPaidOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1)
                .ge(Order::getCreateTime, today.atStartOfDay())
                .lt(Order::getCreateTime, today.plusDays(1).atStartOfDay())
                .list();

            BigDecimal todayRevenue = todayPaidOrders.stream()
                .map(Order::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("todayRevenue", todayRevenue);

            // 本月收入
            LocalDate monthStart = today.withDayOfMonth(1);
            List<Order> monthPaidOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1)
                .ge(Order::getCreateTime, monthStart.atStartOfDay())
                .list();

            BigDecimal monthRevenue = monthPaidOrders.stream()
                .map(Order::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            statistics.put("monthRevenue", monthRevenue);

            return statistics;

        } catch (Exception e) {
            log.error("获取门店设备收入统计失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public List<Map<String, Object>> getShopPopularDevices(Long shopId, Integer limit) {
        try {
            validateShopPermission(shopId);

            if (limit == null || limit <= 0) {
                limit = 10; // 默认返回前10个
            }

            // 统计每个设备的订单数量
            List<Order> allOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 只统计已支付订单
                .list();

            Map<Integer, Long> deviceOrderCount = allOrders.stream()
                .collect(Collectors.groupingBy(Order::getDeviceId, Collectors.counting()));

            // 获取热门设备信息
            List<Map<String, Object>> popularDevices = deviceOrderCount.entrySet().stream()
                .sorted(Map.Entry.<Integer, Long>comparingByValue().reversed())
                .limit(limit)
                .map(entry -> {
                    Device device = deviceService.getById(entry.getKey());
                    Map<String, Object> deviceInfo = new HashMap<>();
                    if (device != null) {
                        deviceInfo.put("deviceId", device.getId());
                        deviceInfo.put("deviceNo", device.getDeviceNo());
                        deviceInfo.put("deviceName", device.getDeviceName());
                        deviceInfo.put("orderCount", entry.getValue());

                        // 计算收入
                        BigDecimal revenue = allOrders.stream()
                            .filter(order -> order.getDeviceId().equals(entry.getKey()))
                            .map(Order::getActualAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                        deviceInfo.put("revenue", revenue);
                    }
                    return deviceInfo;
                })
                .filter(map -> !map.isEmpty())
                .collect(Collectors.toList());

            return popularDevices;

        } catch (Exception e) {
            log.error("获取门店热门设备排行失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map<String, Object>> getShopDeviceAlerts(Long shopId) {
        try {
            validateShopPermission(shopId);

            List<Map<String, Object>> alerts = new ArrayList<>();

            // 获取门店所有设备
            List<Device> devices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .list();

            for (Device device : devices) {
                // 检查各种预警条件
                List<String> deviceAlerts = new ArrayList<>();

                // 电量预警
                if (device.getBatteryLevel() != null && device.getBatteryLevel() < 20) {
                    deviceAlerts.add("电量不足(" + device.getBatteryLevel() + "%)");
                }

                // 离线预警
                if (device.getOnlineStatus() == 0) {
                    deviceAlerts.add("设备离线");
                }

                // 故障预警
                if (device.getStatus() == 3) {
                    deviceAlerts.add("设备故障");
                }

                // 如果有预警，添加到列表
                if (!deviceAlerts.isEmpty()) {
                    Map<String, Object> alert = new HashMap<>();
                    alert.put("deviceId", device.getId());
                    alert.put("deviceNo", device.getDeviceNo());
                    alert.put("deviceName", device.getDeviceName());
                    alert.put("alerts", deviceAlerts);
                    alert.put("alertLevel", device.getStatus() == 3 ? "critical" :
                                          (device.getBatteryLevel() != null && device.getBatteryLevel() < 10) ? "critical" : "warning");
                    alerts.add(alert);
                }
            }

            return alerts;

        } catch (Exception e) {
            log.error("获取门店设备预警信息失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ShopDeviceStatusVO> getLowBatteryDevices(Long shopId) {
        try {
            validateShopPermission(shopId);

            List<Device> lowBatteryDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .lt(Device::getBatteryLevel, 20)
                .orderByAsc(Device::getBatteryLevel)
                .list();

            return lowBatteryDevices.stream()
                .map(this::convertDeviceToShopDeviceStatusVO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取低电量设备列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ShopDeviceStatusVO> getFaultDevices(Long shopId) {
        try {
            validateShopPermission(shopId);

            List<Device> faultDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .eq(Device::getStatus, 3)
                .orderByDesc(Device::getUpdateTime)
                .list();

            return faultDevices.stream()
                .map(this::convertDeviceToShopDeviceStatusVO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取故障设备列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<ShopDeviceStatusVO> getOfflineDevices(Long shopId) {
        try {
            validateShopPermission(shopId);

            List<Device> offlineDevices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .eq(Device::getOnlineStatus, 0)
                .orderByDesc(Device::getLastOnlineTime)
                .list();

            return offlineDevices.stream()
                .map(this::convertDeviceToShopDeviceStatusVO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取离线设备列表失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public boolean validateDeviceUuidBelongsToShop(String uuid, Long shopId) {
        try {
            if (!StringUtils.hasText(uuid)) {
                return false;
            }

            Device device = deviceService.lambdaQuery()
                .eq(Device::getMacAddress, uuid)
                .eq(Device::getShopId, shopId.intValue())
                .one();

            return device != null;

        } catch (Exception e) {
            log.error("验证设备UUID归属失败，UUID: {}, 门店ID: {}", uuid, shopId, e);
            return false;
        }
    }

    @Override
    public boolean refreshShopDeviceStatusCache(Long shopId) {
        try {
            validateShopPermission(shopId);

            // 获取门店所有设备
            List<Device> devices = deviceService.lambdaQuery()
                .eq(Device::getShopId, shopId.intValue())
                .list();

            // 刷新每个设备的缓存
            int successCount = 0;
            for (Device device : devices) {
                if (refreshDeviceStatusCache(device.getId().longValue(), shopId)) {
                    successCount++;
                }
            }

            log.info("门店设备缓存刷新完成，门店ID: {}, 成功刷新: {}/{}", shopId, successCount, devices.size());
            return successCount == devices.size();

        } catch (Exception e) {
            log.error("刷新门店设备缓存失败，门店ID: {}", shopId, e);
            return false;
        }
    }

    /**
     * 计算设备的门店分成收入
     *
     * @param deviceId 设备ID
     * @return 门店分成总收入
     */
    private BigDecimal calculateDeviceShopCommission(Long deviceId) {
        try {
            // 查询该设备的所有已支付订单
            List<Order> paidOrders = orderService.lambdaQuery()
                .eq(Order::getDeviceId, deviceId.intValue())
                .eq(Order::getPayStatus, 1) // 已支付
                .list();

            if (paidOrders.isEmpty()) {
                return BigDecimal.ZERO;
            }

            // 获取这些订单的分成明细
            List<String> orderIds = paidOrders.stream()
                .map(order -> order.getId().toString())
                .collect(Collectors.toList());

            // 查询分成明细并计算门店分成总额
            BigDecimal totalShopCommission = commissionDetailService.lambdaQuery()
                .in(CommissionDetail::getOrderId, orderIds)
                .list()
                .stream()
                .map(CommissionDetail::getShopAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.debug("设备{}门店分成收入计算完成，总收入: {}", deviceId, totalShopCommission);
            return totalShopCommission;

        } catch (Exception e) {
            log.error("计算设备{}门店分成收入失败: {}", deviceId, e.getMessage(), e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取故障类型名称
     */
    private String getFaultTypeName(Integer faultType) {
        if (faultType == null) return "未知";
        switch (faultType) {
            case 1: return "硬件故障";
            case 2: return "软件故障";
            case 3: return "电源故障";
            case 4: return "网络故障";
            case 5: return "其他故障";
            default: return "未知故障";
        }
    }

    /**
     * 获取故障状态名称
     */
    private String getFaultStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待处理";
            case 1: return "处理中";
            case 2: return "已处理";
            case 3: return "已关闭";
            default: return "未知状态";
        }
    }

    /**
     * 获取维护类型名称
     */
    private String getMaintenanceTypeName(Integer maintenanceType) {
        if (maintenanceType == null) return "未知";
        switch (maintenanceType) {
            case 1: return "定期维护";
            case 2: return "故障维护";
            case 3: return "紧急维护";
            case 4: return "其他";
            default: return "未知维护";
        }
    }

    /**
     * 获取维护状态名称
     */
    private String getMaintenanceStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待处理";
            case 1: return "已安排";
            case 2: return "已完成";
            case 3: return "已取消";
            default: return "未知状态";
        }
    }
}
