package com.jycb.jycbz.modules.shop.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.finance.entity.CommissionConfig;
import com.jycb.jycbz.modules.finance.entity.CommissionDetail;
import com.jycb.jycbz.modules.finance.service.CommissionDetailService;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.shop.dto.ShopOrderQueryDTO;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopOrderService;
import com.jycb.jycbz.modules.shop.service.ShopService;
import com.jycb.jycbz.modules.shop.vo.ShopOrderVO;
import com.jycb.jycbz.modules.system.util.QueryWrapperBuilder;
import com.jycb.jycbz.modules.user.entity.User;
import com.jycb.jycbz.modules.user.service.UserService;
import com.jycb.jycbz.modules.shop.service.ShopCommissionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 门店订单服务实现类
 *
 * 重构说明：
 * 1. 完全移除原生SQL代码，使用MyBatis Plus实现
 * 2. 使用QueryWrapperBuilder构建查询条件
 * 3. 加强数据权限控制，确保门店只能查看自己的订单
 * 4. 优化分页查询性能
 * 5. 实现所有业务方法的真实逻辑
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopOrderServiceImpl implements ShopOrderService {

    private final OrderService orderService;
    private final ShopService shopService;
    private final DeviceService deviceService;
    private final UserService userService;
    private final ShopCommissionService shopCommissionService;
    private final CommissionDetailService commissionDetailService;

    /**
     * 分页查询门店订单列表
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 订单分页结果
     */
    @Override
    public Page<ShopOrderVO> pageShopOrders(Page<ShopOrderVO> page, ShopOrderQueryDTO queryDTO) {
        log.info("分页查询门店订单列表，门店ID: {}, 查询条件: {}", queryDTO.getShopId(), queryDTO);

        // 验证门店权限
        validateShopPermission(queryDTO.getShopId());

        try {
            // 1. 使用MyBatis Plus构建查询条件
            QueryWrapperBuilder<Order> builder = QueryWrapperBuilder.create();

            // 2. 添加门店ID条件（数据权限控制）
            builder.eq(Order::getShopId, queryDTO.getShopId().intValue());

            // 3. 应用查询条件
            applyOrderQueryConditions(builder, queryDTO);

            // 4. 执行分页查询
            Page<Order> orderPage = new Page<>(page.getCurrent(), page.getSize());
            Page<Order> orderResult = orderService.page(orderPage, builder.build());

            // 5. 转换为ShopOrderVO对象
            List<ShopOrderVO> orderVOs = orderResult.getRecords().stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            // 6. 设置分页结果
            Page<ShopOrderVO> result = new Page<>(page.getCurrent(), page.getSize());
            result.setRecords(orderVOs);
            result.setTotal(orderResult.getTotal());

            log.info("查询完成，共找到 {} 个订单", orderResult.getTotal());
            return result;

        } catch (Exception e) {
            log.error("分页查询门店订单列表失败", e);
            throw new BusinessException("查询订单列表失败：" + e.getMessage());
        }
    }

    /**
     * 应用订单查询条件
     *
     * @param builder 查询构建器
     * @param queryDTO 查询条件
     */
    private void applyOrderQueryConditions(QueryWrapperBuilder<Order> builder, ShopOrderQueryDTO queryDTO) {
        // 订单号模糊查询
        if (StringUtils.hasText(queryDTO.getOrderNo())) {
            builder.like(Order::getOrderNo, queryDTO.getOrderNo().trim());
        }

        // 设备筛选
        if (queryDTO.getDeviceId() != null) {
            builder.eq(Order::getDeviceId, queryDTO.getDeviceId());
        }

        // 用户筛选（通过用户手机号）
        if (StringUtils.hasText(queryDTO.getUserPhone())) {
            // 先查询用户ID
            List<User> users = userService.lambdaQuery()
                .like(User::getMobile, queryDTO.getUserPhone().trim())
                .list();
            if (!users.isEmpty()) {
                List<Integer> userIds = users.stream()
                    .map(User::getId)
                    .collect(Collectors.toList());
                builder.inIfPresent(Order::getUserId, userIds);
            } else {
                // 如果没有找到用户，设置一个不可能的条件
                builder.eq(Order::getUserId, -1);
            }
        }

        // 订单状态
        if (queryDTO.getOrderStatus() != null) {
            builder.eq(Order::getOrderStatus, queryDTO.getOrderStatus());
        }

        // 支付状态
        if (queryDTO.getPayStatus() != null) {
            builder.eq(Order::getPayStatus, queryDTO.getPayStatus());
        }

        // 支付方式
        if (queryDTO.getPayType() != null) {
            builder.eq(Order::getPayType, queryDTO.getPayType());
        }

        // 创建时间范围
        if (queryDTO.getCreateStartTime() != null) {
            builder.ge(Order::getCreateTime, queryDTO.getCreateStartTime());
        }
        if (queryDTO.getCreateEndTime() != null) {
            builder.le(Order::getCreateTime, queryDTO.getCreateEndTime());
        }

        // 支付时间范围
        if (queryDTO.getPayStartTime() != null) {
            builder.ge(Order::getPayTime, queryDTO.getPayStartTime());
        }
        if (queryDTO.getPayEndTime() != null) {
            builder.le(Order::getPayTime, queryDTO.getPayEndTime());
        }

        // 金额范围
        if (queryDTO.getMinAmount() != null) {
            builder.ge(Order::getAmount, queryDTO.getMinAmount());
        }
        if (queryDTO.getMaxAmount() != null) {
            builder.le(Order::getAmount, queryDTO.getMaxAmount());
        }

        // 使用时长范围
        if (queryDTO.getMinDuration() != null) {
            builder.ge(Order::getActualDuration, queryDTO.getMinDuration());
        }
        if (queryDTO.getMaxDuration() != null) {
            builder.le(Order::getActualDuration, queryDTO.getMaxDuration());
        }

        // 排序
        String orderBy = queryDTO.getOrderBy();
        String orderDirection = queryDTO.getOrderDirection();

        if (!StringUtils.hasText(orderBy)) {
            orderBy = "createTime";
        }
        if (!StringUtils.hasText(orderDirection) ||
            (!orderDirection.equalsIgnoreCase("asc") && !orderDirection.equalsIgnoreCase("desc"))) {
            orderDirection = "desc";
        }

        // 根据排序字段设置排序
        if ("createTime".equals(orderBy)) {
            if ("asc".equalsIgnoreCase(orderDirection)) {
                builder.orderByAsc(Order::getCreateTime);
            } else {
                builder.orderByDesc(Order::getCreateTime);
            }
        } else if ("payTime".equals(orderBy)) {
            if ("asc".equalsIgnoreCase(orderDirection)) {
                builder.orderByAsc(Order::getPayTime);
            } else {
                builder.orderByDesc(Order::getPayTime);
            }
        } else if ("amount".equals(orderBy)) {
            if ("asc".equalsIgnoreCase(orderDirection)) {
                builder.orderByAsc(Order::getAmount);
            } else {
                builder.orderByDesc(Order::getAmount);
            }
        } else {
            // 默认按创建时间倒序
            builder.orderByDesc(Order::getCreateTime);
        }
    }

    /**
     * 转换Order实体为ShopOrderVO
     *
     * @param order 订单实体
     * @return ShopOrderVO
     */
    private ShopOrderVO convertOrderToShopOrderVO(Order order) {
        ShopOrderVO vo = new ShopOrderVO();

        // 基础订单信息
        vo.setOrderId(order.getId().longValue());
        vo.setOrderNo(order.getOrderNo());
        vo.setOrderStatus(order.getOrderStatus());
        vo.setTotalAmount(order.getAmount());
        vo.setPaidAmount(order.getActualAmount());
        vo.setPlannedDuration(order.getDuration());
        vo.setActualDuration(order.getActualDuration());
        vo.setPayStatus(order.getPayStatus());
        vo.setPayTime(order.getPayTime());
        vo.setPayType(getPayTypeFromString(order.getPayType()));
        vo.setPayTransactionId(order.getTransactionId());
        vo.setRefundStatus(order.getRefundStatus());
        vo.setRefundTime(order.getRefundTime());
        vo.setRefundAmount(order.getRefundAmount());
        vo.setRefundReason(order.getRefundReason());
        vo.setUseStartTime(order.getStartTime());
        vo.setUseEndTime(order.getEndTime());
        vo.setCreateTime(order.getCreateTime());
        vo.setUpdateTime(order.getUpdateTime());
        vo.setRemark(order.getRemark());

        // 设置权限信息
        vo.setCanOperate(false);
        vo.setPermissionNote("门店端仅有查看权限，无订单操作权限");

        // 获取门店分成金额
        enrichOrderCommissionInfo(vo, order);

        // 获取用户信息
        if (order.getUserId() != null) {
            User user = userService.getById(order.getUserId());
            if (user != null) {
                vo.setUserPhone(user.getMobile());
                vo.setUserNickname(user.getNickname());
                // ShopOrderVO没有userAvatar字段，使用userInfo存储
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("avatar", user.getAvatar());
                vo.setUserInfo(userInfo);
            }
        }

        // 获取设备信息
        if (order.getDeviceId() != null) {
            Device device = deviceService.getById(order.getDeviceId());
            if (device != null) {
                vo.setDeviceName(device.getDeviceName());
                vo.setDeviceNo(device.getDeviceNo());
                // ShopOrderVO没有deviceLocation字段，使用deviceInfo存储
                Map<String, Object> deviceInfo = new HashMap<>();
                deviceInfo.put("location", device.getAddress());
                vo.setDeviceInfo(deviceInfo);
            }
        }

        return vo;
    }

    /**
     * 填充订单分成信息
     *
     * @param vo 订单VO对象
     * @param order 订单实体
     */
    private void enrichOrderCommissionInfo(ShopOrderVO vo, Order order) {
        try {
            // 查询分成明细
            CommissionDetail commissionDetail = commissionDetailService.lambdaQuery()
                .eq(CommissionDetail::getOrderId, order.getId().toString())
                .one();

            if (commissionDetail != null) {
                // 设置门店分成金额
                vo.setShopCommission(commissionDetail.getShopAmount());
                // 设置分成比例
                vo.setCommissionRate(commissionDetail.getShopRatio());

                log.debug("订单{}门店分成信息：分成金额={}, 分成比例={}%",
                    order.getOrderNo(), commissionDetail.getShopAmount(), commissionDetail.getShopRatio());
            } else {
                // 如果没有分成明细，设置为0
                vo.setShopCommission(BigDecimal.ZERO);
                vo.setCommissionRate(BigDecimal.ZERO);

                log.warn("订单{}未找到分成明细记录", order.getOrderNo());
            }
        } catch (Exception e) {
            log.error("获取订单{}分成信息失败: {}", order.getOrderNo(), e.getMessage(), e);
            // 异常情况下设置为0
            vo.setShopCommission(BigDecimal.ZERO);
            vo.setCommissionRate(BigDecimal.ZERO);
        }
    }

    /**
     * 获取门店订单详情
     *
     * @param orderId 订单ID
     * @param shopId 门店ID
     * @return 订单详情
     */
    @Override
    public ShopOrderVO getShopOrderDetail(Long orderId, Long shopId) {
        log.info("获取门店订单详情，订单ID: {}, 门店ID: {}", orderId, shopId);

        // 验证门店权限
        validateShopPermission(shopId);

        // 验证订单是否属于该门店
        if (!validateOrderBelongsToShop(orderId, shopId)) {
            throw new BusinessException("订单不存在或不属于当前门店");
        }

        try {
            // 1. 使用MyBatis Plus查询订单
            Order order = orderService.lambdaQuery()
                .eq(Order::getId, orderId.intValue())
                .eq(Order::getShopId, shopId.intValue())
                .one();

            if (order == null) {
                throw new BusinessException("订单不存在");
            }

            // 2. 转换为VO对象
            ShopOrderVO orderVO = convertOrderToShopOrderVO(order);

            // 3. 获取扩展信息
            enrichOrderDetailInfo(orderVO, order);

            // 4. 设置权限信息
            orderVO.setCanOperate(false);
            orderVO.setPermissionNote("门店端仅有查看权限，无订单操作权限");
            orderVO.setUpdateTime(LocalDateTime.now());

            log.info("获取订单详情成功，订单号: {}", orderVO.getOrderNo());
            return orderVO;

        } catch (Exception e) {
            log.error("获取门店订单详情失败", e);
            throw new BusinessException("获取订单详情失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopOrderVO> getShopOrderList(Long shopId, ShopOrderQueryDTO queryDTO) {
        log.info("获取门店订单列表，门店ID: {}, 查询条件: {}", shopId, queryDTO);

        // 验证门店权限
        validateShopPermission(shopId);

        // 设置查询条件中的门店ID
        queryDTO.setShopId(shopId);

        try {
            // 使用MyBatis Plus构建查询条件
            QueryWrapperBuilder<Order> builder = QueryWrapperBuilder.create();

            // 添加门店ID条件（数据权限控制）
            builder.eq(Order::getShopId, shopId.intValue());

            // 应用查询条件
            applyOrderQueryConditions(builder, queryDTO);

            // 执行查询
            List<Order> orders = orderService.list(builder.build());

            // 转换为VO对象
            return orders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取门店订单列表失败", e);
            throw new BusinessException("获取订单列表失败：" + e.getMessage());
        }
    }

    /**
     * 根据订单号查询订单
     *
     * @param orderNo 订单号
     * @param shopId 门店ID
     * @return 订单详情
     */
    @Override
    public ShopOrderVO getShopOrderByNo(String orderNo, Long shopId) {
        log.info("根据订单号查询订单，订单号: {}, 门店ID: {}", orderNo, shopId);

        // 验证门店权限
        validateShopPermission(shopId);

        if (!StringUtils.hasText(orderNo)) {
            throw new BusinessException("订单号不能为空");
        }

        try {
            // 使用MyBatis Plus查询订单
            Order order = orderService.lambdaQuery()
                .eq(Order::getOrderNo, orderNo.trim())
                .eq(Order::getShopId, shopId.intValue())
                .one();

            if (order == null) {
                throw new BusinessException("订单不存在或不属于当前门店");
            }

            // 转换为VO对象
            ShopOrderVO orderVO = convertOrderToShopOrderVO(order);

            // 获取扩展信息
            enrichOrderDetailInfo(orderVO, order);

            // 设置权限信息
            orderVO.setCanOperate(false);
            orderVO.setPermissionNote("门店端仅有查看权限，无订单操作权限");

            log.info("根据订单号查询订单成功，订单ID: {}", order.getId());
            return orderVO;

        } catch (Exception e) {
            log.error("根据订单号查询订单失败", e);
            throw new BusinessException("查询订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取门店当前进行中的订单
     *
     * @param shopId 门店ID
     * @return 进行中的订单列表
     */
    @Override
    public List<ShopOrderVO> getShopActiveOrders(Long shopId) {
        log.info("获取门店当前进行中的订单，门店ID: {}", shopId);

        // 验证门店权限
        validateShopPermission(shopId);

        try {
            // 查询状态为"使用中"的订单（订单状态为1表示进行中）
            List<Order> activeOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 1) // 1-进行中
                .eq(Order::getPayStatus, 1)   // 1-已支付
                .orderByDesc(Order::getStartTime)
                .list();

            // 转换为VO对象
            List<ShopOrderVO> result = activeOrders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            log.info("获取门店进行中订单成功，共 {} 个订单", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取门店进行中订单失败", e);
            throw new BusinessException("获取进行中订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取门店今日订单
     *
     * @param shopId 门店ID
     * @return 今日订单列表
     */
    @Override
    public List<ShopOrderVO> getShopTodayOrders(Long shopId) {
        log.info("获取门店今日订单，门店ID: {}", shopId);

        // 验证门店权限
        validateShopPermission(shopId);

        try {
            // 获取今日开始和结束时间
            LocalDateTime todayStart = LocalDate.now().atStartOfDay();
            LocalDateTime todayEnd = LocalDate.now().atTime(23, 59, 59);

            // 查询今日订单
            List<Order> todayOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .ge(Order::getCreateTime, todayStart)
                .le(Order::getCreateTime, todayEnd)
                .orderByDesc(Order::getCreateTime)
                .list();

            // 转换为VO对象
            List<ShopOrderVO> result = todayOrders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            log.info("获取门店今日订单成功，共 {} 个订单", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取门店今日订单失败", e);
            throw new BusinessException("获取今日订单失败：" + e.getMessage());
        }
    }

    /**
     * 获取门店订单统计数据
     *
     * @param shopId 门店ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getShopOrderStatistics(Long shopId, LocalDate startDate, LocalDate endDate) {
        log.info("获取门店订单统计数据，门店ID: {}, 开始日期: {}, 结束日期: {}", shopId, startDate, endDate);

        // 验证门店权限
        validateShopPermission(shopId);

        try {
            Map<String, Object> statistics = new HashMap<>();

            // 构建基础查询条件
            QueryWrapperBuilder<Order> baseBuilder = QueryWrapperBuilder.create();
            baseBuilder.eq(Order::getShopId, shopId.intValue());

            // 添加时间条件
            if (startDate != null) {
                baseBuilder.ge(Order::getCreateTime, startDate.atStartOfDay());
            }
            if (endDate != null) {
                baseBuilder.le(Order::getCreateTime, endDate.atTime(23, 59, 59));
            }

            // 1. 统计订单总数和各状态数量
            List<Order> allOrders = orderService.list(baseBuilder.build());

            long totalOrders = allOrders.size();
            long completedOrders = allOrders.stream().filter(o -> o.getOrderStatus() == 2).count();
            long activeOrders = allOrders.stream().filter(o -> o.getOrderStatus() == 1).count();
            long cancelledOrders = allOrders.stream().filter(o -> o.getOrderStatus() == 3).count();
            long refundOrders = allOrders.stream().filter(o -> o.getRefundStatus() == 1).count();

            statistics.put("totalOrders", totalOrders);
            statistics.put("completedOrders", completedOrders);
            statistics.put("activeOrders", activeOrders);
            statistics.put("cancelledOrders", cancelledOrders);
            statistics.put("refundOrders", refundOrders);

            // 2. 统计收入数据
            List<Order> paidOrders = allOrders.stream()
                .filter(o -> o.getPayStatus() == 1)
                .collect(Collectors.toList());

            BigDecimal totalRevenue = paidOrders.stream()
                .map(Order::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal avgOrderAmount = paidOrders.isEmpty() ? BigDecimal.ZERO :
                totalRevenue.divide(BigDecimal.valueOf(paidOrders.size()), 2, RoundingMode.HALF_UP);

            BigDecimal totalRefund = allOrders.stream()
                .filter(o -> o.getRefundStatus() == 1)
                .map(Order::getRefundAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            statistics.put("totalRevenue", totalRevenue);
            statistics.put("avgOrderAmount", avgOrderAmount);
            statistics.put("totalRefund", totalRefund);

            // 3. 统计使用时长（仅统计已完成订单）
            List<Order> completedOrdersList = allOrders.stream()
                .filter(o -> o.getOrderStatus() == 2)
                .collect(Collectors.toList());

            if (!completedOrdersList.isEmpty()) {
                double avgDuration = completedOrdersList.stream()
                    .mapToInt(o -> o.getActualDuration() != null ? o.getActualDuration() :
                              (o.getDuration() != null ? o.getDuration() : 0))
                    .average()
                    .orElse(0.0);

                long totalDuration = completedOrdersList.stream()
                    .mapToLong(o -> o.getActualDuration() != null ? o.getActualDuration() :
                               (o.getDuration() != null ? o.getDuration() : 0))
                    .sum();

                statistics.put("avgDuration", (int) avgDuration);
                statistics.put("totalDuration", totalDuration);
            } else {
                statistics.put("avgDuration", 0);
                statistics.put("totalDuration", 0L);
            }

            // 4. 统计用户数据
            Set<Integer> uniqueUsers = allOrders.stream()
                .map(Order::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            long totalUsers = uniqueUsers.size();

            // 计算复购用户数（在该门店有多个订单的用户）
            Map<Integer, Long> userOrderCounts = allOrders.stream()
                .filter(o -> o.getUserId() != null)
                .collect(Collectors.groupingBy(Order::getUserId, Collectors.counting()));

            long repeatUsers = userOrderCounts.values().stream()
                .filter(count -> count > 1)
                .count();

            statistics.put("totalUsers", totalUsers);
            statistics.put("repeatUsers", repeatUsers);
            statistics.put("repeatPurchaseRate", totalUsers > 0 ?
                BigDecimal.valueOf(repeatUsers * 100.0 / totalUsers).setScale(2, RoundingMode.HALF_UP) :
                BigDecimal.ZERO);

            // 5. 统计支付方式分布
            Map<String, Long> payTypeDistribution = paidOrders.stream()
                .filter(o -> StringUtils.hasText(o.getPayType()))
                .collect(Collectors.groupingBy(Order::getPayType, Collectors.counting()));

            List<Map<String, Object>> payTypeList = payTypeDistribution.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("payType", entry.getKey());
                    item.put("count", entry.getValue());
                    return item;
                })
                .collect(Collectors.toList());

            statistics.put("payTypeDistribution", payTypeList);

            // 6. 计算订单完成率
            if (totalOrders > 0) {
                BigDecimal completionRate = BigDecimal.valueOf(completedOrders * 100.0 / totalOrders)
                    .setScale(2, RoundingMode.HALF_UP);
                statistics.put("orderCompletionRate", completionRate);
            } else {
                statistics.put("orderCompletionRate", BigDecimal.ZERO);
            }

            // 7. 添加统计时间范围
            statistics.put("startDate", startDate);
            statistics.put("endDate", endDate);
            statistics.put("statisticsTime", LocalDateTime.now());

            log.info("门店订单统计完成，总订单数: {}", totalOrders);
            return statistics;

        } catch (Exception e) {
            log.error("获取门店订单统计数据失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取门店订单趋势数据
     *
     * @param shopId 门店ID
     * @param days 统计天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> getShopOrderTrend(Long shopId, Integer days) {
        log.info("获取门店订单趋势数据，门店ID: {}, 天数: {}", shopId, days);

        // 验证门店权限
        validateShopPermission(shopId);

        if (days == null || days <= 0) {
            days = 7; // 默认7天
        }

        try {
            List<Map<String, Object>> trendData = new ArrayList<>();
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(days - 1);

            // 按天统计订单数量和金额
            for (int i = 0; i < days; i++) {
                LocalDate currentDate = startDate.plusDays(i);
                LocalDateTime dayStart = currentDate.atStartOfDay();
                LocalDateTime dayEnd = currentDate.atTime(23, 59, 59);

                // 查询当天订单
                List<Order> dayOrders = orderService.lambdaQuery()
                    .eq(Order::getShopId, shopId.intValue())
                    .ge(Order::getCreateTime, dayStart)
                    .le(Order::getCreateTime, dayEnd)
                    .list();

                // 统计数据
                long orderCount = dayOrders.size();
                long paidOrderCount = dayOrders.stream().filter(o -> o.getPayStatus() == 1).count();
                BigDecimal dayRevenue = dayOrders.stream()
                    .filter(o -> o.getPayStatus() == 1)
                    .map(Order::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", currentDate.toString());
                dayData.put("orderCount", orderCount);
                dayData.put("paidOrderCount", paidOrderCount);
                dayData.put("revenue", dayRevenue);
                dayData.put("avgOrderAmount", paidOrderCount > 0 ?
                    dayRevenue.divide(BigDecimal.valueOf(paidOrderCount), 2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO);

                trendData.add(dayData);
            }

            log.info("获取门店订单趋势数据成功，统计 {} 天数据", days);
            return trendData;

        } catch (Exception e) {
            log.error("获取门店订单趋势数据失败", e);
            throw new BusinessException("获取订单趋势数据失败：" + e.getMessage());
        }
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证门店权限
     *
     * @param shopId 门店ID
     */
    private void validateShopPermission(Long shopId) {
        if (shopId == null) {
            throw new BusinessException("门店ID不能为空");
        }

        Shop shop = shopService.getById(shopId);
        if (shop == null) {
            throw new BusinessException("门店不存在");
        }

        if (shop.getStatus() == null || shop.getStatus() != 1) {
            throw new BusinessException("门店已禁用，无法查看订单");
        }

        // TODO: 这里可以添加更多的权限验证逻辑
        // 例如：验证当前用户是否有权限访问该门店
    }





    /**
     * 丰富订单详情信息
     *
     * @param orderVO 订单VO
     * @param order 订单实体
     */
    private void enrichOrderDetailInfo(ShopOrderVO orderVO, Order order) {
        // 获取用户详细信息
        if (order.getUserId() != null) {
            User user = userService.getById(order.getUserId());
            if (user != null) {
                orderVO.setUserPhone(user.getMobile());
                orderVO.setUserNickname(user.getNickname());
                // 使用userInfo存储头像信息
                Map<String, Object> userInfo = orderVO.getUserInfo();
                if (userInfo == null) {
                    userInfo = new HashMap<>();
                    orderVO.setUserInfo(userInfo);
                }
                userInfo.put("avatar", user.getAvatar());
                // 可以添加更多用户信息
            }
        }

        // 获取设备详细信息
        if (order.getDeviceId() != null) {
            Device device = deviceService.getById(order.getDeviceId());
            if (device != null) {
                orderVO.setDeviceName(device.getDeviceName());
                orderVO.setDeviceNo(device.getDeviceNo());
                // 使用deviceInfo存储位置信息
                Map<String, Object> deviceInfo = orderVO.getDeviceInfo();
                if (deviceInfo == null) {
                    deviceInfo = new HashMap<>();
                    orderVO.setDeviceInfo(deviceInfo);
                }
                deviceInfo.put("location", device.getAddress());
                // 可以添加更多设备信息
            }
        }

        // 获取门店信息
        if (order.getShopId() != null) {
            Shop shop = shopService.getById(order.getShopId().longValue());
            if (shop != null) {
                orderVO.setShopName(shop.getShopName());
            }
        }
    }

    /**
     * 获取门店热门设备排行
     *
     * @param shopId 门店ID
     * @param limit 限制数量
     * @return 热门设备列表
     */
    @Override
    public List<Map<String, Object>> getShopPopularDevices(Long shopId, Integer limit) {
        log.info("获取门店热门设备排行，门店ID: {}, 限制数量: {}", shopId, limit);

        // 验证门店权限
        validateShopPermission(shopId);

        if (limit == null || limit <= 0) {
            limit = 10; // 默认10个
        }

        try {
            // 查询门店所有订单
            List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 只统计已支付订单
                .list();

            // 按设备ID分组统计
            Map<Integer, List<Order>> deviceOrderMap = orders.stream()
                .filter(o -> o.getDeviceId() != null)
                .collect(Collectors.groupingBy(Order::getDeviceId));

            List<Map<String, Object>> popularDevices = new ArrayList<>();

            for (Map.Entry<Integer, List<Order>> entry : deviceOrderMap.entrySet()) {
                Integer deviceId = entry.getKey();
                List<Order> deviceOrders = entry.getValue();

                // 获取设备信息
                Device device = deviceService.getById(deviceId);
                if (device == null) continue;

                // 统计数据
                long orderCount = deviceOrders.size();
                BigDecimal totalRevenue = deviceOrders.stream()
                    .map(Order::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                long totalDuration = deviceOrders.stream()
                    .filter(o -> o.getOrderStatus() == 2) // 已完成订单
                    .mapToLong(o -> o.getActualDuration() != null ? o.getActualDuration() :
                               (o.getDuration() != null ? o.getDuration() : 0))
                    .sum();

                Map<String, Object> deviceData = new HashMap<>();
                deviceData.put("deviceId", deviceId);
                deviceData.put("deviceName", device.getDeviceName());
                deviceData.put("deviceNo", device.getDeviceNo());
                deviceData.put("orderCount", orderCount);
                deviceData.put("totalRevenue", totalRevenue);
                deviceData.put("totalDuration", totalDuration);
                deviceData.put("avgOrderAmount", orderCount > 0 ?
                    totalRevenue.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP) :
                    BigDecimal.ZERO);

                popularDevices.add(deviceData);
            }

            // 按订单数量排序并限制数量
            return popularDevices.stream()
                .sorted((a, b) -> Long.compare((Long) b.get("orderCount"), (Long) a.get("orderCount")))
                .limit(limit)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取门店热门设备排行失败", e);
            throw new BusinessException("获取热门设备排行失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShopPeakHours(Long shopId) {
        log.info("获取门店热门时段分析，门店ID: {}", shopId);

        // 验证门店权限
        validateShopPermission(shopId);

        try {
            List<Map<String, Object>> peakHours = new ArrayList<>();

            // 获取最近30天的订单数据进行分析
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(30);

            // 查询时间段内的所有已支付订单
            List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 已支付
                .ge(Order::getCreateTime, startTime)
                .le(Order::getCreateTime, endTime)
                .list();

            // 按小时统计订单数量和收入
            Map<Integer, List<Order>> hourlyOrders = orders.stream()
                .collect(Collectors.groupingBy(order -> order.getCreateTime().getHour()));

            // 生成24小时的统计数据
            for (int hour = 0; hour < 24; hour++) {
                List<Order> hourOrders = hourlyOrders.getOrDefault(hour, new ArrayList<>());

                Map<String, Object> hourData = new HashMap<>();
                hourData.put("hour", hour);
                hourData.put("hourLabel", String.format("%02d:00-%02d:59", hour, hour));
                hourData.put("orderCount", hourOrders.size());

                // 计算该时段的收入
                BigDecimal hourRevenue = hourOrders.stream()
                    .map(Order::getActualAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                hourData.put("revenue", hourRevenue);

                // 计算使用率（基于订单数量）
                double usageRate = orders.isEmpty() ? 0.0 : (double) hourOrders.size() / orders.size() * 100;
                hourData.put("usageRate", Math.round(usageRate * 100.0) / 100.0);

                // 判断是否为热门时段（订单数量超过平均值的1.5倍）
                double avgOrderCount = orders.size() / 24.0;
                boolean isPeakHour = hourOrders.size() > avgOrderCount * 1.5;
                hourData.put("isPeakHour", isPeakHour);

                // 设置时段类型
                String periodType;
                if (hour >= 6 && hour < 12) {
                    periodType = "morning";
                } else if (hour >= 12 && hour < 18) {
                    periodType = "afternoon";
                } else if (hour >= 18 && hour < 24) {
                    periodType = "evening";
                } else {
                    periodType = "night";
                }
                hourData.put("periodType", periodType);

                peakHours.add(hourData);
            }

            // 按订单数量降序排序
            peakHours.sort((a, b) -> {
                Integer countA = (Integer) a.get("orderCount");
                Integer countB = (Integer) b.get("orderCount");
                return countB.compareTo(countA);
            });

            log.info("获取门店热门时段分析成功，门店ID: {}, 分析数据: {} 个时段", shopId, peakHours.size());
            return peakHours;

        } catch (Exception e) {
            log.error("获取门店热门时段分析失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public Map<String, Object> getShopRevenueAnalysis(Long shopId, LocalDate startDate, LocalDate endDate) {
        log.info("获取门店收入分析数据，门店ID: {}, 开始日期: {}, 结束日期: {}", shopId, startDate, endDate);

        // 验证门店权限
        validateShopPermission(shopId);

        try {
            Map<String, Object> analysis = new HashMap<>();

            // 设置默认日期范围
            if (startDate == null) startDate = LocalDate.now().minusDays(30);
            if (endDate == null) endDate = LocalDate.now();

            // 查询指定时间段的订单数据
            List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 已支付
                .ge(Order::getPayTime, startDate.atStartOfDay())
                .le(Order::getPayTime, endDate.atTime(23, 59, 59))
                .list();

            // 1. 统计总收入、净收入、分成收入
            BigDecimal totalRevenue = orders.stream()
                .map(Order::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal actualRevenue = orders.stream()
                .map(Order::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 获取门店分成比例
            BigDecimal commissionRate = getShopCommissionRate(shopId);
            BigDecimal commissionRevenue = actualRevenue.multiply(commissionRate);

            analysis.put("totalRevenue", totalRevenue);
            analysis.put("actualRevenue", actualRevenue);
            analysis.put("commissionRevenue", commissionRevenue);
            analysis.put("commissionRate", commissionRate.multiply(new BigDecimal("100")));

            // 2. 分析收入来源分布（按支付方式）
            Map<String, BigDecimal> revenueByPayType = orders.stream()
                .collect(Collectors.groupingBy(
                    order -> getPayTypeName(getPayTypeFromString(order.getPayType())),
                    Collectors.reducing(BigDecimal.ZERO, Order::getActualAmount, BigDecimal::add)
                ));

            List<Map<String, Object>> revenueSource = revenueByPayType.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> source = new HashMap<>();
                    source.put("payType", entry.getKey());
                    source.put("amount", entry.getValue());
                    source.put("percentage", actualRevenue.compareTo(BigDecimal.ZERO) > 0 ?
                        entry.getValue().divide(actualRevenue, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")) : BigDecimal.ZERO);
                    return source;
                })
                .collect(Collectors.toList());
            analysis.put("revenueSource", revenueSource);

            // 3. 计算收入增长趋势（与上一个同等时间段比较）
            LocalDate prevStartDate = startDate.minusDays(ChronoUnit.DAYS.between(startDate, endDate) + 1);
            LocalDate prevEndDate = startDate.minusDays(1);

            List<Order> prevOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1)
                .ge(Order::getPayTime, prevStartDate.atStartOfDay())
                .le(Order::getPayTime, prevEndDate.atTime(23, 59, 59))
                .list();

            BigDecimal prevRevenue = prevOrders.stream()
                .map(Order::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal growthRate = BigDecimal.ZERO;
            if (prevRevenue.compareTo(BigDecimal.ZERO) > 0) {
                growthRate = actualRevenue.subtract(prevRevenue)
                    .divide(prevRevenue, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            }
            analysis.put("revenueGrowthRate", growthRate);
            analysis.put("prevPeriodRevenue", prevRevenue);

            // 4. 生成收入趋势图数据（按天统计）
            List<Map<String, Object>> revenueTrend = new ArrayList<>();
            LocalDate currentDate = startDate;
            while (!currentDate.isAfter(endDate)) {
                final LocalDate date = currentDate;
                BigDecimal dayRevenue = orders.stream()
                    .filter(order -> order.getPayTime() != null &&
                        order.getPayTime().toLocalDate().equals(date))
                    .map(Order::getActualAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                Map<String, Object> dayData = new HashMap<>();
                dayData.put("date", date);
                dayData.put("revenue", dayRevenue);
                dayData.put("orderCount", orders.stream()
                    .filter(order -> order.getPayTime() != null &&
                        order.getPayTime().toLocalDate().equals(date))
                    .count());
                revenueTrend.add(dayData);

                currentDate = currentDate.plusDays(1);
            }
            analysis.put("revenueTrend", revenueTrend);

            // 5. 其他统计信息
            analysis.put("totalOrders", orders.size());
            analysis.put("avgOrderAmount", orders.isEmpty() ? BigDecimal.ZERO :
                actualRevenue.divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP));
            analysis.put("analysisStartDate", startDate);
            analysis.put("analysisEndDate", endDate);
            analysis.put("analysisDays", ChronoUnit.DAYS.between(startDate, endDate) + 1);

            log.info("获取门店收入分析数据成功，门店ID: {}, 总收入: {}, 订单数: {}",
                shopId, actualRevenue, orders.size());
            return analysis;

        } catch (Exception e) {
            log.error("获取门店收入分析数据失败", e);
            return new HashMap<>();
        }
    }

    @Override
    public Map<String, Object> getShopUserAnalysis(Long shopId) {
        log.info("获取门店用户分析数据，门店ID: {}", shopId);

        // 验证门店权限
        validateShopPermission(shopId);

        try {
            Map<String, Object> analysis = new HashMap<>();

            // 获取最近30天的订单数据
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime startTime = endTime.minusDays(30);
            LocalDateTime lastMonthStart = startTime.minusDays(30);

            // 查询最近30天的订单
            List<Order> recentOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 已支付
                .ge(Order::getCreateTime, startTime)
                .le(Order::getCreateTime, endTime)
                .list();

            // 查询上个月的订单（用于计算留存率）
            List<Order> lastMonthOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1)
                .ge(Order::getCreateTime, lastMonthStart)
                .lt(Order::getCreateTime, startTime)
                .list();

            // 1. 统计用户数据
            Set<Integer> totalUserIds = recentOrders.stream()
                .map(Order::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            Set<Integer> lastMonthUserIds = lastMonthOrders.stream()
                .map(Order::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            // 新用户：本月有订单但上月没有订单的用户
            Set<Integer> newUserIds = new HashSet<>(totalUserIds);
            newUserIds.removeAll(lastMonthUserIds);

            // 活跃用户：最近7天有订单的用户
            LocalDateTime last7Days = endTime.minusDays(7);
            Set<Integer> activeUserIds = recentOrders.stream()
                .filter(order -> order.getCreateTime().isAfter(last7Days))
                .map(Order::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

            analysis.put("totalUsers", (long) totalUserIds.size());
            analysis.put("newUsers", (long) newUserIds.size());
            analysis.put("activeUsers", (long) activeUserIds.size());

            // 2. 计算留存率（上月用户在本月仍有消费的比例）
            Set<Integer> retainedUsers = new HashSet<>(lastMonthUserIds);
            retainedUsers.retainAll(totalUserIds);

            BigDecimal retentionRate = lastMonthUserIds.isEmpty() ? BigDecimal.ZERO :
                new BigDecimal(retainedUsers.size())
                    .divide(new BigDecimal(lastMonthUserIds.size()), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            analysis.put("retentionRate", retentionRate);

            // 3. 计算复购率（有多次订单的用户比例）
            Map<Integer, Long> userOrderCounts = recentOrders.stream()
                .collect(Collectors.groupingBy(Order::getUserId, Collectors.counting()));

            long repeatPurchaseUsers = userOrderCounts.values().stream()
                .mapToLong(count -> count > 1 ? 1 : 0)
                .sum();

            BigDecimal repeatPurchaseRate = totalUserIds.isEmpty() ? BigDecimal.ZERO :
                new BigDecimal(repeatPurchaseUsers)
                    .divide(new BigDecimal(totalUserIds.size()), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            analysis.put("repeatPurchaseRate", repeatPurchaseRate);

            // 4. 计算平均订单数和平均收入
            BigDecimal avgOrdersPerUser = totalUserIds.isEmpty() ? BigDecimal.ZERO :
                new BigDecimal(recentOrders.size())
                    .divide(new BigDecimal(totalUserIds.size()), 2, RoundingMode.HALF_UP);
            analysis.put("avgOrdersPerUser", avgOrdersPerUser);

            BigDecimal totalRevenue = recentOrders.stream()
                .map(Order::getActualAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal avgRevenuePerUser = totalUserIds.isEmpty() ? BigDecimal.ZERO :
                totalRevenue.divide(new BigDecimal(totalUserIds.size()), 2, RoundingMode.HALF_UP);
            analysis.put("avgRevenuePerUser", avgRevenuePerUser);

            // 5. 用户价值分布
            List<Map<String, Object>> userValueDistribution = userOrderCounts.entrySet().stream()
                .map(entry -> {
                    Integer userId = entry.getKey();
                    Long orderCount = entry.getValue();

                    BigDecimal userRevenue = recentOrders.stream()
                        .filter(order -> userId.equals(order.getUserId()))
                        .map(Order::getActualAmount)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    Map<String, Object> userValue = new HashMap<>();
                    userValue.put("userId", userId);
                    userValue.put("orderCount", orderCount);
                    userValue.put("totalRevenue", userRevenue);

                    // 用户价值等级
                    String valueLevel;
                    if (userRevenue.compareTo(new BigDecimal("1000")) >= 0) {
                        valueLevel = "高价值";
                    } else if (userRevenue.compareTo(new BigDecimal("300")) >= 0) {
                        valueLevel = "中价值";
                    } else {
                        valueLevel = "低价值";
                    }
                    userValue.put("valueLevel", valueLevel);

                    return userValue;
                })
                .sorted((a, b) -> {
                    BigDecimal revenueA = (BigDecimal) a.get("totalRevenue");
                    BigDecimal revenueB = (BigDecimal) b.get("totalRevenue");
                    return revenueB.compareTo(revenueA);
                })
                .limit(20) // 只返回前20名用户
                .collect(Collectors.toList());

            analysis.put("userValueDistribution", userValueDistribution);
            analysis.put("totalRevenue", totalRevenue);
            analysis.put("totalOrders", recentOrders.size());
            analysis.put("analysisStartDate", startTime.toLocalDate());
            analysis.put("analysisEndDate", endTime.toLocalDate());

            log.info("获取门店用户分析数据成功，门店ID: {}, 总用户数: {}, 新用户数: {}, 活跃用户数: {}",
                shopId, totalUserIds.size(), newUserIds.size(), activeUserIds.size());
            return analysis;

        } catch (Exception e) {
            log.error("获取门店用户分析数据失败", e);
            return new HashMap<>();
        }
    }

    // ==================== 其他方法的简化实现 ====================

    @Override
    public Map<String, Object> getShopOrderStatusDistribution(Long shopId) {
        validateShopPermission(shopId);

        Map<String, Object> distribution = new HashMap<>();

        try {
            // 统计各种状态的订单数量
            Map<String, Long> statusCounts = new HashMap<>();

            // 0-待支付
            long pendingPayment = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 0)
                .count();
            statusCounts.put("pendingPayment", pendingPayment);

            // 1-进行中
            long inProgress = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 1)
                .count();
            statusCounts.put("inProgress", inProgress);

            // 2-已完成
            long completed = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 2)
                .count();
            statusCounts.put("completed", completed);

            // 3-已取消
            long cancelled = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 3)
                .count();
            statusCounts.put("cancelled", cancelled);

            // 4-已退款
            long refunded = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 4)
                .count();
            statusCounts.put("refunded", refunded);

            // 计算总数和百分比
            long total = pendingPayment + inProgress + completed + cancelled + refunded;

            distribution.put("total", total);
            distribution.put("statusCounts", statusCounts);

            // 计算百分比
            Map<String, Double> statusPercentages = new HashMap<>();
            if (total > 0) {
                statusPercentages.put("pendingPayment", (double) pendingPayment / total * 100);
                statusPercentages.put("inProgress", (double) inProgress / total * 100);
                statusPercentages.put("completed", (double) completed / total * 100);
                statusPercentages.put("cancelled", (double) cancelled / total * 100);
                statusPercentages.put("refunded", (double) refunded / total * 100);
            }
            distribution.put("statusPercentages", statusPercentages);

            // 状态名称映射
            Map<String, String> statusNames = new HashMap<>();
            statusNames.put("pendingPayment", "待支付");
            statusNames.put("inProgress", "进行中");
            statusNames.put("completed", "已完成");
            statusNames.put("cancelled", "已取消");
            statusNames.put("refunded", "已退款");
            distribution.put("statusNames", statusNames);

            log.info("获取门店订单状态分布成功，门店ID: {}, 总订单数: {}", shopId, total);
            return distribution;

        } catch (Exception e) {
            log.error("获取门店订单状态分布失败", e);
            throw new BusinessException("获取订单状态分布失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getShopPaymentMethodDistribution(Long shopId) {
        validateShopPermission(shopId);

        Map<String, Object> distribution = new HashMap<>();

        try {
            // 统计各种支付方式的订单数量和金额
            Map<String, Map<String, Object>> paymentStats = new HashMap<>();

            // 微信支付
            long wechatCount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayType, "WXPAY")
                .eq(Order::getPayStatus, 1) // 已支付
                .count();

            BigDecimal wechatAmount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayType, "WXPAY")
                .eq(Order::getPayStatus, 1)
                .list()
                .stream()
                .map(order -> order.getActualAmount() != null ? order.getActualAmount() : order.getAmount())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, Object> wechatStats = new HashMap<>();
            wechatStats.put("count", wechatCount);
            wechatStats.put("amount", wechatAmount);
            paymentStats.put("wechat", wechatStats);

            // 支付宝
            long alipayCount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayType, "ALIPAY")
                .eq(Order::getPayStatus, 1)
                .count();

            BigDecimal alipayAmount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayType, "ALIPAY")
                .eq(Order::getPayStatus, 1)
                .list()
                .stream()
                .map(order -> order.getActualAmount() != null ? order.getActualAmount() : order.getAmount())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, Object> alipayStats = new HashMap<>();
            alipayStats.put("count", alipayCount);
            alipayStats.put("amount", alipayAmount);
            paymentStats.put("alipay", alipayStats);

            // 余额支付
            long balanceCount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayType, "BALANCE")
                .eq(Order::getPayStatus, 1)
                .count();

            BigDecimal balanceAmount = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayType, "BALANCE")
                .eq(Order::getPayStatus, 1)
                .list()
                .stream()
                .map(order -> order.getActualAmount() != null ? order.getActualAmount() : order.getAmount())
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, Object> balanceStats = new HashMap<>();
            balanceStats.put("count", balanceCount);
            balanceStats.put("amount", balanceAmount);
            paymentStats.put("balance", balanceStats);

            // 计算总数和百分比
            long totalCount = wechatCount + alipayCount + balanceCount;
            BigDecimal totalAmount = wechatAmount.add(alipayAmount).add(balanceAmount);

            distribution.put("totalCount", totalCount);
            distribution.put("totalAmount", totalAmount);
            distribution.put("paymentStats", paymentStats);

            // 计算百分比
            Map<String, Double> countPercentages = new HashMap<>();
            Map<String, Double> amountPercentages = new HashMap<>();

            if (totalCount > 0) {
                countPercentages.put("wechat", (double) wechatCount / totalCount * 100);
                countPercentages.put("alipay", (double) alipayCount / totalCount * 100);
                countPercentages.put("balance", (double) balanceCount / totalCount * 100);
            }

            if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                amountPercentages.put("wechat", wechatAmount.divide(totalAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).doubleValue());
                amountPercentages.put("alipay", alipayAmount.divide(totalAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).doubleValue());
                amountPercentages.put("balance", balanceAmount.divide(totalAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).doubleValue());
            }

            distribution.put("countPercentages", countPercentages);
            distribution.put("amountPercentages", amountPercentages);

            // 支付方式名称映射
            Map<String, String> paymentNames = new HashMap<>();
            paymentNames.put("wechat", "微信支付");
            paymentNames.put("alipay", "支付宝");
            paymentNames.put("balance", "余额支付");
            distribution.put("paymentNames", paymentNames);

            log.info("获取门店支付方式分布成功，门店ID: {}, 总订单数: {}", shopId, totalCount);
            return distribution;

        } catch (Exception e) {
            log.error("获取门店支付方式分布失败", e);
            throw new BusinessException("获取支付方式分布失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopOrderVO> getShopAbnormalOrders(Long shopId) {
        validateShopPermission(shopId);

        try {
            // 查询异常订单：超时未支付、超时未完成、异常状态等
            List<Order> abnormalOrders = new ArrayList<>();

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime oneDayAgo = now.minusDays(1);
            LocalDateTime oneHourAgo = now.minusHours(1);

            // 1. 超时未支付的订单（创建超过1小时仍未支付）
            List<Order> unpaidOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 0) // 待支付
                .lt(Order::getCreateTime, oneHourAgo)
                .list();
            abnormalOrders.addAll(unpaidOrders);

            // 2. 超时未完成的订单（开始使用超过24小时仍未完成）
            List<Order> overtimeOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 1) // 进行中
                .lt(Order::getStartTime, oneDayAgo)
                .list();
            abnormalOrders.addAll(overtimeOrders);

            // 3. 支付异常的订单（支付状态与订单状态不匹配）
            List<Order> paymentAbnormalOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .ne(Order::getOrderStatus, 0) // 非待支付状态
                .eq(Order::getPayStatus, 0) // 但支付状态为未支付
                .list();
            abnormalOrders.addAll(paymentAbnormalOrders);

            // 去重并转换为VO
            List<ShopOrderVO> result = abnormalOrders.stream()
                .distinct()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            // 为每个异常订单添加异常原因
            result.forEach(orderVO -> {
                List<String> abnormalReasons = new ArrayList<>();

                Order order = abnormalOrders.stream()
                    .filter(o -> o.getId().equals(orderVO.getId().intValue()))
                    .findFirst()
                    .orElse(null);

                if (order != null) {
                    // 检查各种异常情况
                    if (order.getOrderStatus() == 0 && order.getCreateTime().isBefore(oneHourAgo)) {
                        abnormalReasons.add("超时未支付");
                    }
                    if (order.getOrderStatus() == 1 && order.getStartTime() != null && order.getStartTime().isBefore(oneDayAgo)) {
                        abnormalReasons.add("超时未完成");
                    }
                    if (order.getOrderStatus() != 0 && order.getPayStatus() == 0) {
                        abnormalReasons.add("支付状态异常");
                    }
                }

                orderVO.setAbnormalReasons(abnormalReasons);
            });

            log.info("获取门店异常订单成功，门店ID: {}, 异常订单数: {}", shopId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取门店异常订单失败", e);
            throw new BusinessException("获取异常订单失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopOrderVO> getShopRefundOrders(Long shopId) {
        validateShopPermission(shopId);

        try {
            // 查询退款相关的订单
            List<Order> refundOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 4) // 已退款状态
                .orderByDesc(Order::getUpdateTime)
                .list();

            // 转换为VO对象
            List<ShopOrderVO> result = refundOrders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            // 为每个退款订单添加退款信息
            result.forEach(orderVO -> {
                Order order = refundOrders.stream()
                    .filter(o -> o.getId().equals(orderVO.getId().intValue()))
                    .findFirst()
                    .orElse(null);

                if (order != null) {
                    // 设置退款相关信息
                    orderVO.setRefundAmount(order.getAmount()); // 退款金额
                    orderVO.setRefundTime(order.getUpdateTime()); // 退款时间
                    orderVO.setRefundReason("用户申请退款"); // 退款原因（实际应从退款记录表获取）
                    orderVO.setRefundStatus(1); // 退款状态：1-已退款
                }
            });

            log.info("获取门店退款订单成功，门店ID: {}, 退款订单数: {}", shopId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取门店退款订单失败", e);
            throw new BusinessException("获取退款订单失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopOrderVO> getShopOvertimeOrders(Long shopId) {
        validateShopPermission(shopId);

        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime overtimeThreshold = now.minusHours(24); // 24小时为超时阈值

            // 查询超时订单：进行中但超过24小时未完成的订单
            List<Order> overtimeOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 1) // 进行中状态
                .isNotNull(Order::getStartTime)
                .lt(Order::getStartTime, overtimeThreshold)
                .select(Order::getId, Order::getOrderNo, Order::getShopId, Order::getOrderStatus,
                       Order::getStartTime, Order::getEndTime, Order::getAmount, Order::getPayStatus,
                       Order::getDeviceId, Order::getDeviceNo, Order::getUserId, Order::getCreateTime) // 明确指定查询字段
                .orderByAsc(Order::getStartTime) // 按开始时间升序，最早的在前面
                .list();

            // 转换为VO对象
            List<ShopOrderVO> result = overtimeOrders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            // 为每个超时订单添加超时信息
            result.forEach(orderVO -> {
                Order order = overtimeOrders.stream()
                    .filter(o -> o.getId().equals(orderVO.getId().intValue()))
                    .findFirst()
                    .orElse(null);

                if (order != null && order.getStartTime() != null) {
                    // 计算超时时长
                    Duration overtimeDuration = Duration.between(order.getStartTime(), now);
                    long overtimeHours = overtimeDuration.toHours();
                    long overtimeMinutes = overtimeDuration.toMinutes() % 60;

                    orderVO.setOvertimeHours(overtimeHours);
                    orderVO.setOvertimeMinutes(overtimeMinutes);
                    orderVO.setOvertimeDescription(String.format("已超时 %d 小时 %d 分钟", overtimeHours, overtimeMinutes));

                    // 设置超时等级
                    if (overtimeHours >= 48) {
                        orderVO.setOvertimeLevel("严重");
                    } else if (overtimeHours >= 24) {
                        orderVO.setOvertimeLevel("中等");
                    } else {
                        orderVO.setOvertimeLevel("轻微");
                    }
                }
            });

            log.info("获取门店超时订单成功，门店ID: {}, 超时订单数: {}", shopId, result.size());
            return result;

        } catch (Exception e) {
            log.error("获取门店超时订单失败", e);
            throw new BusinessException("获取超时订单失败：" + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> exportShopOrders(ShopOrderQueryDTO queryDTO) {
        log.info("导出门店订单数据，门店ID: {}, 查询条件: {}", queryDTO.getShopId(), queryDTO);

        validateShopPermission(queryDTO.getShopId());

        try {
            // 查询订单数据
            List<ShopOrderVO> orders = getShopOrderList(queryDTO.getShopId(), queryDTO);

            // 生成文件名
            String fileName = String.format("shop_orders_%s_%s.xlsx",
                queryDTO.getShopId(),
                LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

            // 这里应该调用Excel导出服务生成文件
            // String filePath = excelExportService.exportShopOrders(orders, fileName);

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("downloadUrl", "/api/shop/orders/download/" + fileName);
            result.put("recordCount", orders.size());
            result.put("exportTime", LocalDateTime.now());
            result.put("status", "success");

            log.info("门店订单数据导出成功，文件名: {}, 记录数: {}", fileName, orders.size());
            return result;

        } catch (Exception e) {
            log.error("导出门店订单数据失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("status", "failed");
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public Map<String, Object> exportShopOrderStatistics(Long shopId, LocalDate startDate, LocalDate endDate) {
        log.info("导出门店订单统计报表，门店ID: {}, 开始日期: {}, 结束日期: {}", shopId, startDate, endDate);

        validateShopPermission(shopId);

        try {
            // 获取统计数据
            Map<String, Object> statistics = getShopOrderStatistics(shopId, startDate, endDate);

            // 生成文件名
            String fileName = String.format("shop_order_statistics_%s_%s_to_%s.xlsx",
                shopId,
                startDate != null ? startDate.toString() : "all",
                endDate != null ? endDate.toString() : "now");

            // 这里应该调用Excel导出服务生成统计报表
            // String filePath = excelExportService.exportShopOrderStatistics(statistics, fileName);

            Map<String, Object> result = new HashMap<>();
            result.put("fileName", fileName);
            result.put("downloadUrl", "/api/shop/orders/statistics/download/" + fileName);
            result.put("statisticsData", statistics);
            result.put("exportTime", LocalDateTime.now());
            result.put("status", "success");

            log.info("门店订单统计报表导出成功，文件名: {}", fileName);
            return result;

        } catch (Exception e) {
            log.error("导出门店订单统计报表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("status", "failed");
            result.put("error", e.getMessage());
            return result;
        }
    }

    @Override
    public boolean validateOrderBelongsToShop(Long orderId, Long shopId) {
        if (orderId == null || shopId == null) {
            return false;
        }

        try {
            // 通过订单服务查询订单信息，检查shopId是否匹配
            Order order = orderService.getById(orderId.intValue());
            if (order == null) {
                log.warn("订单不存在，订单ID: {}", orderId);
                return false;
            }

            // 检查订单是否属于指定门店
            boolean belongs = shopId.equals(order.getShopId().longValue());
            if (!belongs) {
                log.warn("订单不属于指定门店，订单ID: {}, 门店ID: {}, 订单所属门店ID: {}",
                        orderId, shopId, order.getShopId());
            }

            return belongs;

        } catch (Exception e) {
            log.error("验证订单归属失败，订单ID: {}, 门店ID: {}", orderId, shopId, e);
            return false;
        }
    }

    @Override
    public boolean validateOrderNoBelongsToShop(String orderNo, Long shopId) {
        if (!StringUtils.hasText(orderNo) || shopId == null) {
            return false;
        }

        try {
            // 通过订单号查询订单信息，检查shopId是否匹配
            Order order = orderService.lambdaQuery()
                .eq(Order::getOrderNo, orderNo)
                .one();

            if (order == null) {
                log.warn("订单不存在，订单号: {}", orderNo);
                return false;
            }

            // 检查订单是否属于指定门店
            boolean belongs = shopId.equals(order.getShopId().longValue());
            if (!belongs) {
                log.warn("订单不属于指定门店，订单号: {}, 门店ID: {}, 订单所属门店ID: {}",
                        orderNo, shopId, order.getShopId());
            }

            return belongs;

        } catch (Exception e) {
            log.error("验证订单号归属失败，订单号: {}, 门店ID: {}", orderNo, shopId, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getShopRealTimeOrderData(Long shopId) {
        validateShopPermission(shopId);

        Map<String, Object> data = new HashMap<>();

        try {
            LocalDate today = LocalDate.now();
            LocalDateTime todayStart = today.atStartOfDay();
            LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

            // 1. 统计进行中的订单数
            long activeOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 1) // 进行中
                .count();
            data.put("activeOrders", activeOrders);

            // 2. 统计今日订单数
            long todayOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .between(Order::getCreateTime, todayStart, todayEnd)
                .count();
            data.put("todayOrders", todayOrders);

            // 3. 统计今日收入（已支付的订单）
            BigDecimal todayRevenue = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 已支付
                .between(Order::getCreateTime, todayStart, todayEnd)
                .list()
                .stream()
                .map(Order::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            data.put("todayRevenue", todayRevenue);

            // 4. 统计在线设备数（需要设备服务支持）
            // 这里暂时设置为0，实际应该调用设备服务
            data.put("onlineDevices", 0);

            // 5. 统计待支付订单数
            long pendingPaymentOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 0) // 待支付
                .count();
            data.put("pendingPaymentOrders", pendingPaymentOrders);

            // 6. 统计今日完成订单数
            long todayCompletedOrders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 2) // 已完成
                .between(Order::getUpdateTime, todayStart, todayEnd)
                .count();
            data.put("todayCompletedOrders", todayCompletedOrders);

            // 7. 计算今日订单完成率
            double completionRate = 0.0;
            if (todayOrders > 0) {
                completionRate = (double) todayCompletedOrders / todayOrders * 100;
            }
            data.put("todayCompletionRate", Math.round(completionRate * 100.0) / 100.0);

            // 8. 统计时间
            data.put("statisticsTime", LocalDateTime.now());

            log.debug("获取门店实时订单数据成功，门店ID: {}", shopId);
            return data;

        } catch (Exception e) {
            log.error("获取门店实时订单数据失败", e);
            // 返回默认数据，避免前端报错
            data.put("activeOrders", 0);
            data.put("todayOrders", 0);
            data.put("todayRevenue", BigDecimal.ZERO);
            data.put("onlineDevices", 0);
            data.put("pendingPaymentOrders", 0);
            data.put("todayCompletedOrders", 0);
            data.put("todayCompletionRate", 0.0);
            data.put("statisticsTime", LocalDateTime.now());
            data.put("error", "获取实时数据失败：" + e.getMessage());
            return data;
        }
    }

    @Override
    public Map<String, Object> getShopTodayRealTimeStats(Long shopId) {
        validateShopPermission(shopId);
        return getShopRealTimeOrderData(shopId);
    }

    @Override
    public boolean refreshShopOrderCache(Long shopId) {
        log.info("刷新门店订单缓存，门店ID: {}", shopId);

        validateShopPermission(shopId);

        try {
            // 1. 刷新门店订单统计缓存
            refreshOrderStatisticsCache(shopId);

            // 2. 刷新实时订单数据缓存
            refreshRealTimeOrderCache(shopId);

            // 3. 刷新热门时段分析缓存
            refreshPeakHoursCache(shopId);

            // 4. 刷新用户分析缓存
            refreshUserAnalysisCache(shopId);

            log.info("门店订单缓存刷新成功，门店ID: {}", shopId);
            return true;

        } catch (Exception e) {
            log.error("刷新门店订单缓存失败", e);
            return false;
        }
    }

    @Override
    public List<ShopOrderVO> getOrdersByDevice(Long deviceId, Long shopId, ShopOrderQueryDTO queryDTO) {
        validateShopPermission(shopId);

        if (deviceId == null) {
            throw new BusinessException("设备ID不能为空");
        }

        try {
            // 验证设备是否属于该门店
            Device device = deviceService.getById(deviceId.intValue());
            if (device == null || !shopId.equals(device.getShopId().longValue())) {
                throw new BusinessException("设备不存在或不属于当前门店");
            }

            // 构建查询条件
            QueryWrapperBuilder<Order> builder = QueryWrapperBuilder.create();

            // 添加门店ID和设备ID条件
            builder.eq(Order::getShopId, shopId.intValue())
                   .eq(Order::getDeviceId, deviceId.intValue());

            // 应用其他查询条件
            if (queryDTO != null) {
                applyOrderQueryConditions(builder, queryDTO);
            }

            // 执行查询
            List<Order> orders = orderService.list(builder.build());

            // 转换为VO对象
            List<ShopOrderVO> result = orders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            // 为每个订单添加设备信息
            result.forEach(orderVO -> {
                orderVO.setDeviceId(deviceId);
                orderVO.setDeviceName(device.getDeviceName());
                orderVO.setDeviceNo(device.getDeviceNo());
            });

            log.info("按设备查询订单成功，设备ID: {}, 门店ID: {}, 订单数: {}", deviceId, shopId, result.size());
            return result;

        } catch (Exception e) {
            log.error("按设备查询订单失败", e);
            throw new BusinessException("查询设备订单失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopOrderVO> getOrdersByUser(Long userId, Long shopId, ShopOrderQueryDTO queryDTO) {
        validateShopPermission(shopId);

        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        try {
            // 验证用户是否存在
            User user = userService.getById(userId.intValue());
            if (user == null) {
                throw new BusinessException("用户不存在");
            }

            // 构建查询条件
            QueryWrapperBuilder<Order> builder = QueryWrapperBuilder.create();

            // 添加门店ID和用户ID条件
            builder.eq(Order::getShopId, shopId.intValue())
                   .eq(Order::getUserId, userId.intValue());

            // 应用其他查询条件
            if (queryDTO != null) {
                applyOrderQueryConditions(builder, queryDTO);
            }

            // 执行查询
            List<Order> orders = orderService.list(builder.build());

            // 转换为VO对象
            List<ShopOrderVO> result = orders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            // 为每个订单添加用户信息
            result.forEach(orderVO -> {
                orderVO.setUserId(userId);
                orderVO.setUserName(user.getNickname());
                orderVO.setUserPhone(user.getMobile());
            });

            log.info("按用户查询订单成功，用户ID: {}, 门店ID: {}, 订单数: {}", userId, shopId, result.size());
            return result;

        } catch (Exception e) {
            log.error("按用户查询订单失败", e);
            throw new BusinessException("查询用户订单失败：" + e.getMessage());
        }
    }

    @Override
    public List<ShopOrderVO> getOrdersByDateRange(Long shopId, LocalDate startDate, LocalDate endDate) {
        log.info("根据时间段查询订单，门店ID: {}, 开始日期: {}, 结束日期: {}", shopId, startDate, endDate);

        validateShopPermission(shopId);

        if (startDate == null) {
            startDate = LocalDate.now().minusDays(30); // 默认查询最近30天
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        try {
            // 构建查询条件
            LocalDateTime startDateTime = startDate.atStartOfDay();
            LocalDateTime endDateTime = endDate.atTime(23, 59, 59);

            // 查询指定时间段的订单
            List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .ge(Order::getCreateTime, startDateTime)
                .le(Order::getCreateTime, endDateTime)
                .orderByDesc(Order::getCreateTime)
                .list();

            // 转换为VO对象
            List<ShopOrderVO> result = orders.stream()
                .map(this::convertOrderToShopOrderVO)
                .collect(Collectors.toList());

            log.info("根据时间段查询订单成功，门店ID: {}, 订单数: {}", shopId, result.size());
            return result;

        } catch (Exception e) {
            log.error("根据时间段查询订单失败", e);
            throw new BusinessException("查询时间段订单失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShopOrderAmountRanking(Long shopId, Integer limit) {
        log.info("获取门店订单金额排行，门店ID: {}, 限制数量: {}", shopId, limit);

        validateShopPermission(shopId);

        if (limit == null || limit <= 0) {
            limit = 20; // 默认20条
        }

        try {
            // 查询门店所有已支付订单
            List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getPayStatus, 1) // 已支付
                .isNotNull(Order::getActualAmount)
                .orderByDesc(Order::getActualAmount)
                .last("LIMIT " + limit)
                .list();

            // 转换为排行榜数据
            List<Map<String, Object>> ranking = new ArrayList<>();
            for (int i = 0; i < orders.size(); i++) {
                Order order = orders.get(i);

                Map<String, Object> rankData = new HashMap<>();
                rankData.put("rank", i + 1);
                rankData.put("orderId", order.getId());
                rankData.put("orderNo", order.getOrderNo());
                rankData.put("amount", order.getActualAmount());
                rankData.put("payTime", order.getPayTime());
                rankData.put("duration", order.getActualDuration());

                // 获取用户信息
                if (order.getUserId() != null) {
                    User user = userService.getById(order.getUserId());
                    if (user != null) {
                        rankData.put("userPhone", user.getMobile());
                        rankData.put("userNickname", user.getNickname());
                    }
                }

                // 获取设备信息
                if (order.getDeviceId() != null) {
                    Device device = deviceService.getById(order.getDeviceId());
                    if (device != null) {
                        rankData.put("deviceNo", device.getDeviceNo());
                        rankData.put("deviceName", device.getDeviceName());
                    }
                }

                ranking.add(rankData);
            }

            log.info("获取门店订单金额排行成功，门店ID: {}, 排行数量: {}", shopId, ranking.size());
            return ranking;

        } catch (Exception e) {
            log.error("获取门店订单金额排行失败", e);
            throw new BusinessException("获取订单金额排行失败：" + e.getMessage());
        }
    }

    @Override
    public List<Map<String, Object>> getShopOrderDurationRanking(Long shopId, Integer limit) {
        log.info("获取门店订单时长排行，门店ID: {}, 限制数量: {}", shopId, limit);

        validateShopPermission(shopId);

        if (limit == null || limit <= 0) {
            limit = 20; // 默认20条
        }

        try {
            // 查询门店所有已完成订单，按实际使用时长排序
            List<Order> orders = orderService.lambdaQuery()
                .eq(Order::getShopId, shopId.intValue())
                .eq(Order::getOrderStatus, 2) // 已完成
                .isNotNull(Order::getActualDuration)
                .orderByDesc(Order::getActualDuration)
                .last("LIMIT " + limit)
                .list();

            // 转换为排行榜数据
            List<Map<String, Object>> ranking = new ArrayList<>();
            for (int i = 0; i < orders.size(); i++) {
                Order order = orders.get(i);

                Map<String, Object> rankData = new HashMap<>();
                rankData.put("rank", i + 1);
                rankData.put("orderId", order.getId());
                rankData.put("orderNo", order.getOrderNo());
                rankData.put("duration", order.getActualDuration());
                rankData.put("durationHours", order.getActualDuration() / 60.0); // 转换为小时
                rankData.put("amount", order.getActualAmount());
                rankData.put("startTime", order.getStartTime());
                rankData.put("endTime", order.getEndTime());

                // 计算时长描述
                int hours = order.getActualDuration() / 60;
                int minutes = order.getActualDuration() % 60;
                rankData.put("durationDescription", String.format("%d小时%d分钟", hours, minutes));

                // 获取用户信息
                if (order.getUserId() != null) {
                    User user = userService.getById(order.getUserId());
                    if (user != null) {
                        rankData.put("userPhone", user.getMobile());
                        rankData.put("userNickname", user.getNickname());
                    }
                }

                // 获取设备信息
                if (order.getDeviceId() != null) {
                    Device device = deviceService.getById(order.getDeviceId());
                    if (device != null) {
                        rankData.put("deviceNo", device.getDeviceNo());
                        rankData.put("deviceName", device.getDeviceName());
                    }
                }

                ranking.add(rankData);
            }

            log.info("获取门店订单时长排行成功，门店ID: {}, 排行数量: {}", shopId, ranking.size());
            return ranking;

        } catch (Exception e) {
            log.error("获取门店订单时长排行失败", e);
            throw new BusinessException("获取订单时长排行失败：" + e.getMessage());
        }
    }







    /**
     * 获取订单状态名称
     */
    private String getOrderStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "待支付";
            case 1: return "使用中";
            case 2: return "已完成";
            case 3: return "已取消";
            default: return "未知状态";
        }
    }

    /**
     * 获取支付状态名称
     */
    private String getPayStatusName(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 0: return "未支付";
            case 1: return "已支付";
            case 2: return "已退款";
            default: return "未知状态";
        }
    }

    /**
     * 从字符串获取支付方式
     */
    private Integer getPayTypeFromString(String payTypeStr) {
        if (payTypeStr == null) return null;
        switch (payTypeStr.toUpperCase()) {
            case "WXPAY": return 1;
            case "ALIPAY": return 2;
            case "BALANCE": return 3;
            default: return null;
        }
    }

    /**
     * 获取支付方式名称
     */
    private String getPayTypeName(Integer payType) {
        if (payType == null) return "未知";
        switch (payType) {
            case 1: return "微信支付";
            case 2: return "支付宝";
            case 3: return "余额支付";
            default: return "未知支付方式";
        }
    }



    /**
     * 获取门店分成比例
     *
     * 优化说明：
     * 1. 使用专门的门店分成服务，提供更好的封装
     * 2. 集成财务模块的分成配置体系
     * 3. 支持层级配置继承（门店->合作商->业务主体->系统）
     * 4. 使用缓存提升性能
     * 5. 提供更灵活的默认值机制
     */
    private BigDecimal getShopCommissionRate(Long shopId) {
        return shopCommissionService.getShopCommissionRate(shopId);
    }

    /**
     * 丰富用户信息
     */
    private void enrichUserInfo(ShopOrderVO orderVO, Map<String, Object> orderInfo) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", orderInfo.get("user_id"));
        userInfo.put("phone", orderInfo.get("user_phone"));
        userInfo.put("nickname", orderInfo.get("user_nickname"));
        userInfo.put("avatar", orderInfo.get("user_avatar"));
        orderVO.setUserInfo(userInfo);
    }

    /**
     * 丰富设备信息
     */
    private void enrichDeviceInfo(ShopOrderVO orderVO, Map<String, Object> orderInfo) {
        Map<String, Object> deviceInfo = new HashMap<>();
        deviceInfo.put("deviceId", orderInfo.get("device_id"));
        deviceInfo.put("deviceNo", orderInfo.get("device_no"));
        deviceInfo.put("deviceName", orderInfo.get("device_name"));
        deviceInfo.put("roomNumber", orderInfo.get("room_number"));
        orderVO.setDeviceInfo(deviceInfo);
    }

    /**
     * 丰富支付信息
     */
    private void enrichPaymentInfo(ShopOrderVO orderVO, Map<String, Object> orderInfo) {
        Map<String, Object> paymentInfo = new HashMap<>();
        paymentInfo.put("payType", orderInfo.get("pay_type"));
        paymentInfo.put("transactionId", orderInfo.get("transaction_id"));
        paymentInfo.put("payTime", orderInfo.get("pay_time"));
        paymentInfo.put("amount", orderInfo.get("amount"));
        paymentInfo.put("actualAmount", orderInfo.get("actual_amount"));
        orderVO.setPaymentInfo(paymentInfo);
    }

    // ==================== 缓存刷新辅助方法 ====================

    /**
     * 刷新订单统计缓存
     */
    private void refreshOrderStatisticsCache(Long shopId) {
        try {
            // 这里应该清除相关的缓存键
            // cacheService.evict("shop_order_statistics", shopId);
            log.debug("订单统计缓存刷新成功，门店ID: {}", shopId);
        } catch (Exception e) {
            log.warn("刷新订单统计缓存失败", e);
        }
    }

    /**
     * 刷新实时订单数据缓存
     */
    private void refreshRealTimeOrderCache(Long shopId) {
        try {
            // 这里应该清除实时数据缓存
            // cacheService.evict("shop_realtime_order_data", shopId);
            log.debug("实时订单数据缓存刷新成功，门店ID: {}", shopId);
        } catch (Exception e) {
            log.warn("刷新实时订单数据缓存失败", e);
        }
    }

    /**
     * 刷新热门时段分析缓存
     */
    private void refreshPeakHoursCache(Long shopId) {
        try {
            // 这里应该清除热门时段分析缓存
            // cacheService.evict("shop_peak_hours", shopId);
            log.debug("热门时段分析缓存刷新成功，门店ID: {}", shopId);
        } catch (Exception e) {
            log.warn("刷新热门时段分析缓存失败", e);
        }
    }

    /**
     * 刷新用户分析缓存
     */
    private void refreshUserAnalysisCache(Long shopId) {
        try {
            // 这里应该清除用户分析缓存
            // cacheService.evict("shop_user_analysis", shopId);
            log.debug("用户分析缓存刷新成功，门店ID: {}", shopId);
        } catch (Exception e) {
            log.warn("刷新用户分析缓存失败", e);
        }
    }

}
