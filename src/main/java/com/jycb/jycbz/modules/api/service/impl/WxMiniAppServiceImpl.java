package com.jycb.jycbz.modules.api.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jycb.jycbz.common.exception.BusinessException;
import com.jycb.jycbz.common.utils.SpringUtils;
import com.jycb.jycbz.config.WxMiniAppConfig;
import com.jycb.jycbz.modules.admin.service.WeixinService;
import com.jycb.jycbz.modules.api.dto.DeviceUseDTO;
import com.jycb.jycbz.modules.api.dto.OrderCreateDTO;
import com.jycb.jycbz.modules.api.dto.WxLoginDTO;
import com.jycb.jycbz.modules.api.service.WxMiniAppService;
import com.jycb.jycbz.modules.api.service.WxPayService;
import com.jycb.jycbz.modules.api.vo.DeviceInfoVO;
import com.jycb.jycbz.modules.api.vo.OrderInfoVO;
import com.jycb.jycbz.modules.api.vo.PaymentInfoVO;
import com.jycb.jycbz.modules.api.vo.UserInfoVO;
import com.jycb.jycbz.modules.device.entity.Device;
import com.jycb.jycbz.modules.device.entity.DeviceFee;
import com.jycb.jycbz.modules.device.entity.DeviceLog;
import com.jycb.jycbz.modules.device.service.DeviceFeeService;
import com.jycb.jycbz.modules.device.service.DeviceFeeRealTimeService;
import com.jycb.jycbz.modules.device.service.DeviceLogService;
import com.jycb.jycbz.modules.device.service.DeviceService;
import com.jycb.jycbz.modules.device.service.DeviceStatusService;
import com.jycb.jycbz.modules.device.util.DeviceFeeCalculator;
import com.jycb.jycbz.modules.order.entity.Order;
import com.jycb.jycbz.modules.order.event.OrderStatusChangeEvent;
import com.jycb.jycbz.modules.order.service.OrderService;
import com.jycb.jycbz.modules.shop.entity.Shop;
import com.jycb.jycbz.modules.shop.service.ShopService;
import com.jycb.jycbz.modules.user.entity.User;
import com.jycb.jycbz.modules.user.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 微信小程序服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxMiniAppServiceImpl implements WxMiniAppService {

    private final UserService userService;
    private final DeviceService deviceService;
    private final DeviceLogService deviceLogService;
    private final DeviceFeeService deviceFeeService;
    private final DeviceFeeRealTimeService deviceFeeRealTimeService;
    private final OrderService orderService;
    private final ShopService shopService;
    private final DeviceStatusService deviceStatusService;
    private final ApplicationEventPublisher eventPublisher;
    
    @Autowired
    private WxMiniAppConfig wxMiniAppConfig;
    
    @Autowired
    private WeixinService weixinService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserInfoVO login(WxLoginDTO loginDTO) {
        if (loginDTO == null || !StringUtils.hasText(loginDTO.getCode())) {
            throw new BusinessException("登录参数不能为空");
        }

        // 通过微信code获取openid
        String openid = getOpenidByCode(loginDTO.getCode());
        if (!StringUtils.hasText(openid)) {
            throw new BusinessException("获取微信用户信息失败");
        }

        // 查询用户是否存在
        User user = userService.findByOpenid(openid);
        
        // 用户不存在则创建新用户
        if (user == null) {
            user = new User();
            user.setOpenid(openid);
            user.setNickname(loginDTO.getNickName());
            user.setAvatar(loginDTO.getAvatarUrl());
            user.setGender(loginDTO.getGender());
            user.setProvince(loginDTO.getProvince());
            user.setCity(loginDTO.getCity());
            user.setStatus(1); // 正常状态
            user.setCreateTime(LocalDateTime.now());
            user.setLastLoginTime(LocalDateTime.now());
            
            // 保存用户
            userService.save(user);
        } else {
            // 更新用户信息
            if (StringUtils.hasText(loginDTO.getNickName())) {
                user.setNickname(loginDTO.getNickName());
            }
            if (StringUtils.hasText(loginDTO.getAvatarUrl())) {
                user.setAvatar(loginDTO.getAvatarUrl());
            }
            user.setLastLoginTime(LocalDateTime.now());
            userService.updateById(user);
        }

        // 检查用户状态
        if (user.getStatus() != 1) {
            throw new BusinessException("用户已被禁用");
        }
        
        // 如果提供了手机号获取凭证，则获取并更新用户手机号
        if (StringUtils.hasText(loginDTO.getPhoneCode())) {
            try {
                weixinService.getAndUpdatePhoneNumber(user.getId(), loginDTO.getPhoneCode());
                // 重新获取更新后的用户信息
                user = userService.getById(user.getId());
            } catch (BusinessException e) {
                // 获取手机号失败不影响登录流程，只记录日志
                log.error("获取用户手机号失败: {}", e.getMessage());
            } catch (Exception e) {
                // 其他异常也不影响登录流程
                log.error("获取用户手机号失败", e);
            }
        }

        // 登录，生成token
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        long tokenExpire = StpUtil.getTokenTimeout();

        // 构建返回结果
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        userInfoVO.setUserId(user.getId().longValue());
        userInfoVO.setRegisterTime(user.getCreateTime() != null ? 
            user.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        userInfoVO.setLastLoginTime(user.getLastLoginTime() != null ? 
            user.getLastLoginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        userInfoVO.setToken(token);
        userInfoVO.setTokenExpire(tokenExpire);

        return userInfoVO;
    }

    /**
     * 通过微信code获取openid
     * 实际项目中需要调用微信API
     */
    private String getOpenidByCode(String code) {
        try {
            log.info("开始通过code获取微信openid，code长度: {}", code.length());
            
            // 微信小程序登录凭证校验接口
            String url = "https://api.weixin.qq.com/sns/jscode2session";
            
            // 构建请求参数
            Map<String, String> params = new HashMap<>();
            params.put("appid", wxMiniAppConfig.getAppid());
            params.put("secret", wxMiniAppConfig.getSecret());
            params.put("js_code", code);
            params.put("grant_type", "authorization_code");
            
            log.debug("微信登录请求参数: appid={}, js_code={}, grant_type={}", 
                    maskSensitiveInfo(wxMiniAppConfig.getAppid()), 
                    maskSensitiveInfo(code), 
                    "authorization_code");
            
            // 发送HTTP请求
            RestTemplate restTemplate = new RestTemplate();
            log.debug("发送请求到微信接口: {}", url);
            
            ResponseEntity<String> response = restTemplate.getForEntity(
                url + "?appid={appid}&secret={secret}&js_code={js_code}&grant_type={grant_type}", 
                String.class, 
                params
            );
            
            // 解析响应
            if (response.getStatusCode() == HttpStatus.OK) {
                String responseBody = response.getBody();
                log.debug("微信接口响应: {}", responseBody);
                
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode jsonNode = objectMapper.readTree(responseBody);

                // 检查是否有错误码
                if (jsonNode.has("errcode") && jsonNode.get("errcode").asInt() != 0) {
                    int errorCode = jsonNode.get("errcode").asInt();
                    String errorMsg = jsonNode.has("errmsg") ? jsonNode.get("errmsg").asText() : "未知错误";
                    
                    log.error("获取微信用户信息失败: errcode={}, errmsg={}", errorCode, errorMsg);
                    
                    // 根据错误码提供更具体的错误信息
                    String detailedErrorMsg;
                    switch (errorCode) {
                        case -1:
                            detailedErrorMsg = "系统繁忙，请稍后再试";
                            break;
                        case 40029:
                            detailedErrorMsg = "无效的code，可能已过期或被使用过";
                            break;
                        case 40226:
                            detailedErrorMsg = "高风险等级用户，小程序登录拦截";
                            break;
                        case 45011:
                            detailedErrorMsg = "请求频率限制";
                            break;
                        case 40013:
                            detailedErrorMsg = "无效的AppID";
                            break;
                        case 40125:
                            detailedErrorMsg = "无效的appsecret";
                            break;
                        default:
                            detailedErrorMsg = "获取微信用户信息失败，错误码：" + errorCode;
                    }
                    
                    log.error("微信登录失败详细信息: {}", detailedErrorMsg);
                    throw new BusinessException(detailedErrorMsg);
                }
                
                // 获取openid
                String openid = jsonNode.has("openid") ? jsonNode.get("openid").asText() : null;
                if (StringUtils.hasText(openid)) {
                    // 可以选择性地保存unionid（如果有的话）
                    String unionid = jsonNode.has("unionid") ? jsonNode.get("unionid").asText() : null;
                    String sessionKey = jsonNode.has("session_key") ? jsonNode.get("session_key").asText() : null;
                    
                    log.info("获取微信用户信息成功，openid: {}, unionid: {}, session_key长度: {}", 
                            maskSensitiveInfo(openid), 
                            unionid != null ? maskSensitiveInfo(unionid) : "无", 
                            sessionKey != null ? sessionKey.length() : 0);
                    return openid;
                } else {
                    log.error("微信接口返回成功但未包含openid: {}", responseBody);
                    throw new BusinessException("获取微信openid失败，请稍后再试");
                }
            } else {
                log.error("微信接口请求失败，状态码: {}, 响应: {}", response.getStatusCode().value(), response.getBody());
                throw new BusinessException("微信服务暂时不可用，请稍后再试");
            }
        } catch (BusinessException e) {
            // 业务异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("调用微信API异常: {}", e.getMessage(), e);
            throw new BusinessException("调用微信API异常: " + e.getMessage());
        }
    }

    /**
     * 对敏感信息进行脱敏处理
     */
    private String maskSensitiveInfo(String info) {
        if (info == null || info.length() <= 6) {
            return "***";
        }
        return info.substring(0, 3) + "****" + info.substring(info.length() - 3);
    }

    @Override
    public UserInfoVO getUserInfo(Long userId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        
        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");
        
        // 获取用户信息
        User user = userService.getById(userIdInt);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        
        // 构建返回结果
        UserInfoVO userInfoVO = new UserInfoVO();
        BeanUtils.copyProperties(user, userInfoVO);
        userInfoVO.setUserId(user.getId().longValue());
        userInfoVO.setRegisterTime(user.getCreateTime() != null ? 
            user.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        userInfoVO.setLastLoginTime(user.getLastLoginTime() != null ? 
            user.getLastLoginTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null);
        
        // 敏感信息脱敏
        if (StringUtils.hasText(user.getMobile())) {
            userInfoVO.setMobile(maskSensitiveInfo(user.getMobile()));
        }
        
        return userInfoVO;
    }

    @Override
    public DeviceInfoVO scanDeviceCode(Long userId, String deviceCode) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (!StringUtils.hasText(deviceCode)) {
            throw new BusinessException("设备二维码不能为空");
        }

        // 根据设备二维码或MAC地址获取设备信息
        Device device = null;
        
        // 首先尝试作为绑定码查询
        device = deviceService.getDeviceByBindCode(deviceCode);
        log.info("通过绑定码查询设备: bindCode={}, 结果={}", deviceCode, device != null ? "成功" : "失败");
        
        // 如果找不到，则尝试作为MAC地址查询
        if (device == null) {
            device = deviceService.getDeviceByMacAddress(deviceCode);
            log.info("通过MAC地址查询设备: mac={}, 结果={}", deviceCode, device != null ? "成功" : "失败");
        }
        
        // 如果找不到，尝试去除前导零后按设备编号查询
        if (device == null) {
            String deviceNoWithoutLeadingZeros = deviceCode.replaceFirst("^0+", "");
            if (!deviceNoWithoutLeadingZeros.equals(deviceCode)) {
                // 只有在去除前导零后与原始值不同时才进行查询
                device = deviceService.getDeviceByDeviceNo(deviceNoWithoutLeadingZeros);
                log.info("通过去除前导零后的设备编号查询设备: deviceNo={}, 结果={}", 
                        deviceNoWithoutLeadingZeros, device != null ? "成功" : "失败");
            }
        }
        
        // 如果还是找不到，则尝试解析二维码内容
        if (device == null && deviceCode.contains("jycb://device?mac=")) {
            // 从二维码URL中提取MAC地址
            String macAddress = deviceCode.substring(deviceCode.indexOf("mac=") + 4);
            device = deviceService.getDeviceByMacAddress(macAddress);
            log.info("通过二维码URL中的MAC地址查询设备: mac={}, 结果={}", 
                    macAddress, device != null ? "成功" : "失败");
        }
        
        if (device == null) {
            throw new BusinessException("设备不存在或二维码无效");
        }

        // 检查设备状态，只有故障状态(3)的设备才禁止使用
        if (device.getStatus() == 3) {
            log.warn("设备故障，无法使用: deviceId={}, status={}", device.getId(), device.getStatus());
            throw new BusinessException("设备故障，无法使用");
        }

        // 如果设备状态为维护中(2)，记录日志但允许使用
        if (device.getStatus() == 2) {
            log.warn("设备处于维护状态，但仍允许使用: deviceId={}", device.getId());
        }

        // 获取门店信息
        Shop shop = shopService.getById(device.getShopId());
        if (shop == null) {
            throw new BusinessException("设备所属门店不存在");
        }

        // 获取设备费率
        DeviceFee deviceFee = deviceFeeService.getDeviceFeeByDeviceId(device.getId());
        BigDecimal hourlyRate;
        if (deviceFee != null) {
            // 使用统一的计费计算工具
            hourlyRate = DeviceFeeCalculator.getHourlyRate(deviceFee);
        } else {
            // 否则使用计算方法
            hourlyRate = deviceFeeService.calculateUseFee(device.getId(), 60);
        }

        // 构建返回结果
        DeviceInfoVO deviceInfoVO = new DeviceInfoVO();
        BeanUtils.copyProperties(device, deviceInfoVO);
        deviceInfoVO.setDeviceId(device.getId().longValue());
        deviceInfoVO.setShopId(shop.getId().longValue());
        deviceInfoVO.setShopName(shop.getName());
        deviceInfoVO.setHourlyRate(hourlyRate);
        deviceInfoVO.setAvailable(device.getStatus() == 1);
        deviceInfoVO.setQrCode(device.getBindCode());

        // 检查设备是否有正在使用中的订单
        Order currentOrder = orderService.getById(device.getId());
        if (currentOrder != null) {
            deviceInfoVO.setCurrentOrderId(currentOrder.getId().longValue());
        }

        // 记录设备扫描日志
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceId(device.getId());
        deviceLog.setDeviceNo(device.getDeviceNo());
        deviceLog.setOperatorId(userId.intValue());
        deviceLog.setLogType("SCAN");
        deviceLog.setContent("扫描设备二维码: " + deviceCode);
        // 使用createTime代替operationTime
        deviceLog.setCreateTime(LocalDateTime.now());
        // 使用Double.valueOf()进行类型转换
        deviceLog.setLatitude(device.getLatitude() != null ? Double.valueOf(device.getLatitude().toString()) : null);
        deviceLog.setLongitude(device.getLongitude() != null ? Double.valueOf(device.getLongitude().toString()) : null);
        deviceLog.setAddress(device.getAddress());
        deviceLogService.save(deviceLog);

        return deviceInfoVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentInfoVO scanAndCreateOrder(Long userId, String deviceCode) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (!StringUtils.hasText(deviceCode)) {
            throw new BusinessException("设备二维码不能为空");
        }

        // 1. 获取设备信息
        Device device = null;
        
        // 首先尝试作为绑定码查询
        device = deviceService.getDeviceByBindCode(deviceCode);
        
        // 如果找不到，则尝试作为MAC地址查询
        if (device == null) {
            device = deviceService.getDeviceByMacAddress(deviceCode);
        }
        
        // 如果还是找不到，则尝试解析二维码内容
        if (device == null && deviceCode.contains("jycb://device?mac=")) {
            // 从二维码URL中提取MAC地址
            String macAddress = deviceCode.substring(deviceCode.indexOf("mac=") + 4);
            device = deviceService.getDeviceByMacAddress(macAddress);
        }
        
        if (device == null) {
            throw new BusinessException("设备不存在或二维码无效");
        }

        // 检查设备状态，只有故障状态(3)的设备才禁止使用
        if (device.getStatus() == 3) {
            log.warn("设备故障，无法使用: deviceId={}, status={}", device.getId(), device.getStatus());
            throw new BusinessException("设备故障，无法使用");
        }

        // 如果设备状态为维护中(2)，记录日志但允许使用
        if (device.getStatus() == 2) {
            log.warn("设备处于维护状态，但仍允许使用: deviceId={}", device.getId());
        }

        // 检查设备是否已绑定
        if (device.getIsBound() != 1) {
            throw new BusinessException("设备未绑定，无法使用");
        }

        // 检查设备是否有可用容量
        if (!deviceService.checkDeviceHasCapacity(device.getId())) {
            throw new BusinessException("设备当前使用人数已达上限，请稍后再试");
        }

        // 获取门店信息
        Shop shop = shopService.getById(device.getShopId());
        if (shop == null) {
            throw new BusinessException("门店信息不存在");
        }

        // 2. 获取设备费率并创建订单（使用实时费率获取）
        DeviceFee deviceFee = deviceFeeRealTimeService.getCurrentEffectiveFee(device.getId());
        if (deviceFee == null) {
            throw new BusinessException("设备费率配置异常");
        }

        // 根据费率类型计算默认金额
        BigDecimal amount;
        Integer defaultDuration; // 默认使用时长（分钟）

        switch (deviceFee.getFeeType()) {
            case 1: // 按时间计费
                // 按分钟计费：默认1分钟
                // 按小时计费：默认1小时
                // 按天计费：默认1天
                if ("分钟".equals(deviceFee.getUnit())) {
                    defaultDuration = 1; // 1分钟
                    amount = DeviceFeeCalculator.calculateFee(deviceFee, defaultDuration);
                } else if ("小时".equals(deviceFee.getUnit())) {
                    defaultDuration = 60; // 60分钟（1小时）
                    amount = DeviceFeeCalculator.calculateFee(deviceFee, defaultDuration);
                } else if ("天".equals(deviceFee.getUnit())) {
                    defaultDuration = 1440; // 1440分钟（1天）
                    amount = DeviceFeeCalculator.calculateFee(deviceFee, defaultDuration);
                } else {
                    // 未知单位，默认按分钟计费
                    defaultDuration = 1;
                    amount = DeviceFeeCalculator.calculateFee(deviceFee, defaultDuration);
                }
                break;
            case 2: // 按次数计费
                defaultDuration = 0; // 按次数计费不需要时长
                amount = deviceFee.getPrice(); // 直接使用价格
                break;
            case 3: // 包天计费
                defaultDuration = 1440; // 1440分钟（1天）
                amount = deviceFee.getPrice(); // 直接使用价格
                break;
            default:
                throw new BusinessException("未知的费率类型");
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setDeviceId(device.getId());
        order.setDeviceNo(device.getDeviceNo());
        order.setEntityId(shop.getEntityId());
        order.setPartnerId(shop.getPartnerId());
        order.setShopId(shop.getId());
        order.setUserId(userId.intValue());
        order.setOrderStatus(0); // 待支付
        order.setAmount(amount);
        order.setDuration(defaultDuration); // 设置默认使用时长
        order.setPayStatus(0); // 未支付
        order.setPayType("WXPAY");
        order.setCreateTime(LocalDateTime.now());
        // 设置开始时间为当前时间
        order.setStartTime(LocalDateTime.now());

        // 保存订单
        orderService.save(order);

        // 记录设备扫描日志
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceId(device.getId());
        deviceLog.setDeviceNo(device.getDeviceNo());
        deviceLog.setOperatorId(userId.intValue());
        deviceLog.setLogType("SCAN_AND_ORDER");
        deviceLog.setContent("扫描设备二维码并创建订单: " + deviceCode);
        deviceLog.setCreateTime(LocalDateTime.now());
        deviceLog.setLatitude(device.getLatitude() != null ? Double.valueOf(device.getLatitude().toString()) : null);
        deviceLog.setLongitude(device.getLongitude() != null ? Double.valueOf(device.getLongitude().toString()) : null);
        deviceLog.setAddress(device.getAddress());
        deviceLogService.save(deviceLog);

        // 3. 生成支付信息
        try {
            // 获取用户信息
            User user = userService.getById(userId.intValue());
            if (user == null || !StringUtils.hasText(user.getOpenid())) {
                throw new BusinessException("用户信息异常，无法完成支付");
            }

            // 调用微信支付服务创建支付订单
            WxPayService wxPayService = SpringUtils.getBean(WxPayService.class);
            if (wxPayService == null) {
                log.error("微信支付服务未注册");
                throw new BusinessException("支付服务不可用");
            }

            PaymentInfoVO paymentInfoVO = wxPayService.createWxPayOrder(order.getId().longValue(), user.getOpenid());
            if (paymentInfoVO == null) {
                throw new BusinessException("创建支付订单失败");
            }

            // 设置订单基本信息
            paymentInfoVO.setOrderId(order.getId().longValue());
            paymentInfoVO.setOrderNo(order.getOrderNo());
            paymentInfoVO.setPayAmount(order.getAmount());
            paymentInfoVO.setPayType(1); // 1-微信支付
            paymentInfoVO.setPayStatus(0); // 待支付
            
            return paymentInfoVO;
        } catch (Exception e) {
            log.error("创建微信支付订单异常", e);
            throw new BusinessException("创建支付订单失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderInfoVO createOrder(Long userId, OrderCreateDTO orderCreateDTO) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (orderCreateDTO == null || !StringUtils.hasText(orderCreateDTO.getDeviceCode())) {
            throw new BusinessException("设备编码不能为空");
        }

        // 获取设备信息
        Device device = null;
        String deviceCode = orderCreateDTO.getDeviceCode();
        
        // 首先尝试作为绑定码查询
        device = deviceService.getDeviceByBindCode(deviceCode);
        
        // 如果找不到，则尝试作为MAC地址查询
        if (device == null) {
            device = deviceService.getDeviceByMacAddress(deviceCode);
        }
        
        // 如果还是找不到，则尝试解析二维码内容
        if (device == null && deviceCode.contains("jycb://device?mac=")) {
            // 从二维码URL中提取MAC地址
            String macAddress = deviceCode.substring(deviceCode.indexOf("mac=") + 4);
            device = deviceService.getDeviceByMacAddress(macAddress);
        }
        
        if (device == null) {
            throw new BusinessException("设备不存在");
        }

        // 检查设备状态，只有故障状态(3)的设备才禁止使用
        if (device.getStatus() == 3) {
            log.warn("设备故障，无法使用: deviceId={}, status={}", device.getId(), device.getStatus());
            throw new BusinessException("设备故障，无法使用");
        }

        // 如果设备状态为维护中(2)，记录日志但允许使用
        if (device.getStatus() == 2) {
            log.warn("设备处于维护状态，但仍允许使用: deviceId={}", device.getId());
        }

        // 检查设备是否已绑定
        if (device.getIsBound() != 1) {
            throw new BusinessException("设备未绑定，无法使用");
        }

        // 检查设备是否有可用容量
        if (!deviceService.checkDeviceHasCapacity(device.getId())) {
            throw new BusinessException("设备当前使用人数已达上限，请稍后再试");
        }

        // 获取门店信息
        Shop shop = shopService.getById(device.getShopId());
        if (shop == null) {
            throw new BusinessException("门店信息不存在");
        }

        // 获取设备费率（使用实时费率获取）
        DeviceFee deviceFee = deviceFeeRealTimeService.getCurrentEffectiveFee(device.getId());
        BigDecimal hourlyRate;
        boolean isDayRate = false;
        
        if (deviceFee != null) {
            // 检查是否为包天费率
            if (deviceFee.getFeeType() != null && deviceFee.getFeeType() == DeviceFeeCalculator.FEE_TYPE_DAY) {
                isDayRate = true;
                // 包天费率直接使用原价，不转换为小时费率
                hourlyRate = deviceFee.getPrice();
            } else {
                // 使用统一的计费计算工具
                hourlyRate = DeviceFeeCalculator.getHourlyRate(deviceFee);
            }
        } else {
            // 否则使用实时计算方法
            hourlyRate = deviceFeeRealTimeService.calculateRealTimeUseFee(device.getId(), 60);
        }
        
        if (hourlyRate == null || hourlyRate.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("设备费率配置异常");
        }

        // 计算订单金额
        BigDecimal amount;
        Integer duration = orderCreateDTO.getDuration(); // 预设使用时长（分钟）
        if (duration != null && duration > 0) {
            // 使用统一的计费计算工具按预设时长计费
            if (deviceFee != null) {
                // 如果是包天费率，直接使用包天价格，不考虑时长
                if (isDayRate) {
                    amount = deviceFee.getPrice();
                } else {
                    amount = DeviceFeeCalculator.calculateFee(deviceFee, duration);
                }
            } else {
                // 如果没有找到设备费率配置，使用实时计算
                amount = deviceFeeRealTimeService.calculateRealTimeUseFee(device.getId(), duration);
            }
        } else {
            // 默认1小时起步，或者包天价格
            amount = hourlyRate;
            duration = isDayRate ? 1440 : 60; // 包天为1440分钟(24小时)，否则默认60分钟
        }

        // 创建订单
        Order order = new Order();
        order.setOrderNo(generateOrderNo());
        order.setDeviceId(device.getId());
        order.setDeviceNo(device.getDeviceNo());
        order.setEntityId(shop.getEntityId());
        order.setPartnerId(shop.getPartnerId());
        order.setShopId(shop.getId());
        order.setUserId(userId.intValue());
        order.setOrderStatus(0); // 待支付
        order.setAmount(amount);
        order.setPayStatus(0); // 未支付
        order.setPayType("WXPAY");
        order.setCreateTime(LocalDateTime.now());
        // 设置开始时间为当前时间
        order.setStartTime(LocalDateTime.now());

        // 保存订单
        orderService.save(order);

        // 构建返回结果
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        BeanUtils.copyProperties(order, orderInfoVO);
        orderInfoVO.setOrderId(order.getId().longValue());
        orderInfoVO.setUserId(order.getUserId().longValue());
        orderInfoVO.setDeviceId(order.getDeviceId().longValue());
        orderInfoVO.setShopId(order.getShopId().longValue());
        orderInfoVO.setDeviceName(device.getDeviceName());
        orderInfoVO.setShopName(shop.getShopName());
        orderInfoVO.setAmount(amount);
        orderInfoVO.setDuration(duration);

        return orderInfoVO;
    }

    /**
     * 生成订单编号
     */
    private String generateOrderNo() {
        return "OD" + System.currentTimeMillis() + UUID.randomUUID().toString().substring(0, 6);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PaymentInfoVO payOrder(Long userId, Long orderId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (orderId == null) {
            throw new BusinessException("订单ID不能为空");
        }

        log.info("支付订单: userId={}, orderId={}", userId, orderId);

        // 获取订单信息
        Order order = orderService.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: orderId={}", orderId);
            throw new BusinessException("订单不存在");
        }

        log.debug("订单信息: orderId={}, orderUserId={}, orderStatus={}, payStatus={}",
                 orderId, order.getUserId(), order.getOrderStatus(), order.getPayStatus());

        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");

        // 验证订单所属用户
        if (order.getUserId() == null) {
            log.warn("订单用户ID为空: orderId={}", orderId);
            throw new BusinessException("订单数据异常");
        }

        // 详细的权限验证调试日志
        log.debug("权限验证详情: userId={} (类型:{}), orderUserId={} (类型:{}), userIdInt={} (类型:{})",
                 userId, userId.getClass().getSimpleName(),
                 order.getUserId(), order.getUserId().getClass().getSimpleName(),
                 userIdInt, userIdInt.getClass().getSimpleName());

        log.debug("equals比较结果: order.getUserId().equals(userIdInt) = {}", order.getUserId().equals(userIdInt));
        log.debug("数值比较结果: order.getUserId().intValue() == userIdInt.intValue() = {}",
                 order.getUserId().intValue() == userIdInt.intValue());

        // 使用数值比较而不是对象比较，避免类型不匹配问题
        if (order.getUserId().intValue() != userIdInt.intValue()) {
            log.warn("无权操作此订单: userId={}, orderUserId={}, orderId={}", userId, order.getUserId(), orderId);
            throw new BusinessException("无权操作此订单");
        }

        // 检查订单状态 - 只有待支付(0)和已退款(4)的订单可以支付
        if (order.getOrderStatus() != 0 && order.getOrderStatus() != 4) {
            log.warn("订单状态异常，无法支付: orderId={}, orderStatus={}", orderId, order.getOrderStatus());
            String statusDesc = getOrderStatusDesc(order.getOrderStatus());
            throw new BusinessException("订单状态为" + statusDesc + "，无法支付");
        }

        // 检查支付状态 - 已支付的订单不能重复支付
        if (order.getPayStatus() != null && order.getPayStatus() == 1) {
            log.warn("订单已支付，无法重复支付: orderId={}, payStatus={}", orderId, order.getPayStatus());
            throw new BusinessException("订单已支付，无法重复支付");
        }

        // 获取用户信息
        User user = userService.getById(userIdInt);
        if (user == null || !StringUtils.hasText(user.getOpenid())) {
            log.warn("用户信息异常，无法完成支付: userId={}, openid={}", userId, user != null ? user.getOpenid() : null);
            throw new BusinessException("用户信息异常，无法完成支付");
        }

        try {
            // 调用微信支付服务创建支付订单
            WxPayService wxPayService = SpringUtils.getBean(WxPayService.class);
            if (wxPayService == null) {
                log.error("微信支付服务未注册");
                throw new BusinessException("支付服务不可用");
            }

            // 将Integer转换为Long
            PaymentInfoVO paymentInfoVO = wxPayService.createWxPayOrder(orderId, user.getOpenid());
            if (paymentInfoVO == null) {
                throw new BusinessException("创建支付订单失败");
            }

            // 设置订单基本信息
            paymentInfoVO.setOrderId(order.getId().longValue());
            paymentInfoVO.setOrderNo(order.getOrderNo());
            paymentInfoVO.setPayAmount(order.getAmount());
            paymentInfoVO.setPayType(1); // 1-微信支付
            paymentInfoVO.setPayStatus(0); // 待支付
            
            log.info("创建支付订单成功: orderId={}", orderId);
            return paymentInfoVO;
        } catch (Exception e) {
            log.error("创建微信支付订单异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new BusinessException("创建支付订单失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlockDevice(Long userId, DeviceUseDTO deviceUseDTO) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (deviceUseDTO == null) {
            throw new BusinessException("设备使用参数不能为空");
        }
        
        log.info("解锁设备请求: userId={}, orderId={}, deviceId={}, deviceCode={}, paymentSuccess={}",
                userId, 
                deviceUseDTO.getOrderId(), 
                deviceUseDTO.getDeviceId(), 
                deviceUseDTO.getDeviceCode(),
                deviceUseDTO.getPaymentSuccess());

        // 获取订单信息
        if (deviceUseDTO.getOrderId() == null) {
            throw new BusinessException("订单ID不能为空");
        }
        
        Order order = orderService.getById(deviceUseDTO.getOrderId());
        if (order == null) {
            log.error("订单不存在: orderId={}", deviceUseDTO.getOrderId());
            throw new BusinessException("订单不存在");
        }

        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");

        // 检查用户是否有未完成的订单
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Order::getUserId, userIdInt)
                   .eq(Order::getOrderStatus, 1) // 1-进行中
                   .ne(Order::getId, deviceUseDTO.getOrderId()); // 排除当前订单
        List<Order> activeOrders = orderService.list(queryWrapper);

        // 如果用户已经有其他进行中的订单，提示用户
        if (!activeOrders.isEmpty()) {
            log.warn("用户已有其他进行中的订单: userId={}, activeOrderCount={}", userId, activeOrders.size());
            
            // 获取第一个活跃订单的设备信息
            Order activeOrder = activeOrders.get(0);
            Device activeDevice = deviceService.getById(activeOrder.getDeviceId());
            String deviceName = activeDevice != null ? activeDevice.getDeviceName() : "未知设备";
            
            // 如果允许用户同时使用多个设备，则不抛出异常，只记录日志
            // throw new BusinessException("您已有正在使用的设备：" + deviceName + "，请先结束使用");
        }

        // 检查订单支付状态
        if (order.getPayStatus() == null || order.getPayStatus() != 1) {
            // 再次查询微信支付状态，确保最新状态
            try {
                log.info("订单未标记为已支付，尝试查询微信支付状态: orderId={}", order.getId());
                WxPayService wxPayService = SpringUtils.getBean(WxPayService.class);
                boolean payStatus = wxPayService.queryWxPayOrderStatus(order.getId().longValue());
                
                if (payStatus) {
                    // 如果支付成功，更新订单状态
                    log.info("微信支付查询结果: 订单已支付，更新订单状态: orderId={}", order.getId());
                    orderService.payOrder(order.getOrderNo(), "WXPAY", null);
                    log.info("解锁设备时检测到订单已支付但状态未更新，已更新订单状态: orderId={}", order.getId());
                } else {
                    // 如果微信支付查询结果为未支付，但前端显示支付成功，可能是支付状态同步延迟
                    // 这种情况下，我们信任前端的支付成功状态，更新订单为已支付
                    if (deviceUseDTO.getPaymentSuccess() != null && deviceUseDTO.getPaymentSuccess()) {
                        log.info("微信支付查询结果为未支付，但前端报告支付成功，信任前端状态: orderId={}", order.getId());
                        orderService.payOrder(order.getOrderNo(), "WXPAY", null);
                    } else {
                        throw new BusinessException("订单未支付，无法使用设备");
                    }
                }
            } catch (Exception e) {
                // 如果前端明确表示支付成功，但后端查询异常，我们信任前端状态
                if (deviceUseDTO.getPaymentSuccess() != null && deviceUseDTO.getPaymentSuccess()) {
                    log.info("微信支付查询异常，但前端报告支付成功，信任前端状态: orderId={}, error={}", 
                            order.getId(), e.getMessage());
                    orderService.payOrder(order.getOrderNo(), "WXPAY", null);
                } else {
                    log.error("解锁设备时查询支付状态异常", e);
                    throw new BusinessException("订单未支付，无法使用设备");
                }
            }
        }

        // 获取设备信息
        Device device = null;
        
        // 首先尝试从订单中获取设备ID
        if (order.getDeviceId() != null) {
            device = deviceService.getById(order.getDeviceId());
            log.info("从订单中获取设备: orderId={}, deviceId={}, 结果={}", 
                    order.getId(), order.getDeviceId(), device != null ? "成功" : "失败");
        }
        
        // 如果从订单中获取失败，尝试使用传入的设备ID
        if (device == null && deviceUseDTO.getDeviceId() != null && deviceUseDTO.getDeviceId() > 0) {
            device = deviceService.getById(deviceUseDTO.getDeviceId());
            log.info("使用传入的设备ID获取设备: deviceId={}, 结果={}", 
                    deviceUseDTO.getDeviceId(), device != null ? "成功" : "失败");
        }
        
        // 如果还是获取失败，尝试使用设备MAC地址
        if (device == null && StringUtils.hasText(deviceUseDTO.getDeviceCode())) {
            // 尝试作为MAC地址查询
            device = deviceService.getDeviceByMacAddress(deviceUseDTO.getDeviceCode());
            log.info("使用MAC地址获取设备: mac={}, 结果={}", 
                    deviceUseDTO.getDeviceCode(), device != null ? "成功" : "失败");
            
            // 如果找不到，尝试作为绑定码查询
            if (device == null) {
                device = deviceService.getDeviceByBindCode(deviceUseDTO.getDeviceCode());
                log.info("使用绑定码获取设备: bindCode={}, 结果={}", 
                        deviceUseDTO.getDeviceCode(), device != null ? "成功" : "失败");
            }
        }
        
        // 如果设备信息获取失败
        if (device == null) {
            log.error("无法获取设备信息: orderId={}, deviceId={}, deviceCode={}", 
                    order.getId(), deviceUseDTO.getDeviceId(), deviceUseDTO.getDeviceCode());
            throw new BusinessException("无法获取设备信息");
        }
        
        // 验证设备MAC地址是否匹配
        if (StringUtils.hasText(deviceUseDTO.getDeviceCode()) && 
            StringUtils.hasText(device.getMacAddress()) &&
            !deviceUseDTO.getDeviceCode().equals(device.getMacAddress())) {
            
            // 如果MAC地址不匹配，但绑定码匹配，则允许使用
            if (StringUtils.hasText(device.getBindCode()) && 
                deviceUseDTO.getDeviceCode().equals(device.getBindCode())) {
                log.info("设备绑定码匹配: deviceId={}, bindCode={}", device.getId(), device.getBindCode());
            } else {
                log.warn("设备MAC地址不匹配: deviceId={}, expectedMac={}, actualMac={}", 
                        device.getId(), device.getMacAddress(), deviceUseDTO.getDeviceCode());
                // 在实际应用中，可能需要更严格的验证，这里为了兼容性先允许继续
                // throw new BusinessException("设备信息不匹配，无法使用");
            }
        } else {
            log.info("设备MAC地址匹配: deviceId={}, macAddress={}", device.getId(), device.getMacAddress());
        }
        
        // 检查设备状态，只有故障状态(3)的设备才禁止使用
        if (device.getStatus() == 3) {
            log.warn("设备故障，无法使用: deviceId={}, status={}", device.getId(), device.getStatus());
            throw new BusinessException("设备故障，无法使用");
        }

        // ===================== 关键修改点 =====================
        // 检查同一用户是否已经在使用该设备（包括当前订单）
        LambdaQueryWrapper<Order> allUserOrdersQuery = new LambdaQueryWrapper<>();
        allUserOrdersQuery.eq(Order::getDeviceId, device.getId())
                        .eq(Order::getUserId, userIdInt)
                        .eq(Order::getOrderStatus, 1); // 1-进行中
        List<Order> allUserOrdersOnDevice = orderService.list(allUserOrdersQuery);
        
        // 如果用户已经有进行中的订单（包括当前订单），则不增加设备使用人数
        boolean shouldIncrementUsers = allUserOrdersOnDevice.isEmpty();
        
        // 如果当前订单已经是进行中状态，则不需要增加使用人数
        if (order.getOrderStatus() == 1) {
            log.info("当前订单已经是进行中状态，不增加设备使用人数: orderId={}, deviceId={}", order.getId(), device.getId());
            shouldIncrementUsers = false;
        }
        
        // 只有在需要增加设备使用人数的情况下才执行增加操作
        if (shouldIncrementUsers) {
            log.info("用户首次使用该设备，增加设备使用人数: userId={}, deviceId={}", userId, device.getId());
            boolean incrementResult = deviceService.incrementCurrentUsers(device.getId());
            if (!incrementResult) {
                log.warn("设备当前使用人数已达上限，无法解锁: deviceId={}", device.getId());
                throw new BusinessException("设备当前使用人数已达上限，无法解锁");
            }
        } else {
            log.info("用户已经在使用该设备，不增加设备使用人数: userId={}, deviceId={}", userId, device.getId());
        }
        // ===================== 关键修改点结束 =====================

        // 更新设备状态（如果需要）
        if (deviceUseDTO.getBattery() != null) {
            device.setBatteryLevel(deviceUseDTO.getBattery());
        }
        if (deviceUseDTO.getLatitude() != null && deviceUseDTO.getLongitude() != null) {
            device.setLatitude(new BigDecimal(deviceUseDTO.getLatitude().toString()));
            device.setLongitude(new BigDecimal(deviceUseDTO.getLongitude().toString()));
            if (StringUtils.hasText(deviceUseDTO.getAddress())) {
                device.setAddress(deviceUseDTO.getAddress());
            }
        }
        
        // 确保设备状态为使用中
        device.setInUse(1);
        deviceService.updateById(device);

        // 记录设备操作日志
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceId(device.getId());
        deviceLog.setDeviceNo(device.getDeviceNo());
        deviceLog.setOperatorId(userId.intValue());
        deviceLog.setLogType("UNLOCK");
        deviceLog.setCreateTime(LocalDateTime.now());
        deviceLog.setLatitude(deviceUseDTO.getLatitude());
        deviceLog.setLongitude(deviceUseDTO.getLongitude());
        deviceLog.setAddress(deviceUseDTO.getAddress());
        deviceLog.setContent("成功解锁设备，订单号：" + order.getOrderNo());
        
        // 电池电量信息存储在content字段中
        if (deviceUseDTO.getBattery() != null) {
            deviceLog.setContent("电池电量: " + deviceUseDTO.getBattery() + "%");
        }
        
        deviceLogService.save(deviceLog);

        // 实际项目中，这里需要通过某种方式向设备发送开锁指令
        // 由于设备不联网，所以通过小程序中转，小程序负责与设备通信
        log.info("设备解锁成功: deviceId={}, orderId={}, userId={}", device.getId(), order.getId(), userId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderInfoVO endUseDevice(Long userId, Long orderId) {
        if (userId == null || orderId == null) {
            throw new BusinessException("参数错误");
        }
        
        log.info("结束使用设备: userId={}, orderId={}", userId, orderId);
        
        // 查询订单
        Order order = orderService.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: orderId={}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");
        
        // 验证订单所属用户
        if (!order.getUserId().equals(userIdInt)) {
            log.warn("无权操作此订单: userId={}, orderUserId={}", userId, order.getUserId());
            throw new BusinessException("无权操作此订单");
        }
        
        // 验证订单状态
        if (order.getOrderStatus() != 1) {
            log.warn("订单状态异常，无法结束使用: orderId={}, status={}", orderId, order.getOrderStatus());
            throw new BusinessException("订单状态异常，无法结束使用");
        }
        
        // 获取设备信息
        Device device = deviceService.getById(order.getDeviceId());
        if (device == null) {
            log.warn("设备不存在: deviceId={}", order.getDeviceId());
            throw new BusinessException("设备不存在");
        }
        
        // 计算实际使用时长
        LocalDateTime now = LocalDateTime.now();
        int actualMinutes = 0;
        String useDuration = "未知";
        
        if (order.getStartTime() != null) {
            long minutes = Duration.between(order.getStartTime(), now).toMinutes();
            actualMinutes = (int) minutes;
            useDuration = actualMinutes + "分钟";
            
            // 设置实际使用时长
            order.setActualDuration(actualMinutes);
            log.info("计算实际使用时长：订单编号={}，开始时间={}，结束时间={}，使用时长={}分钟", 
                    order.getOrderNo(), order.getStartTime(), now, actualMinutes);
        }
        
        // 计算实际费用
        if (actualMinutes > 0) {
            try {
                // 获取设备费率
                DeviceFee deviceFee = deviceFeeService.getDeviceFeeByDeviceId(device.getId());
                
                // 计算实际费用
                BigDecimal actualAmount;
                if (deviceFee != null) {
                    // 使用统一的计费计算工具
                    actualAmount = com.jycb.jycbz.modules.device.util.DeviceFeeCalculator.calculateFee(
                            deviceFee, actualMinutes);
                } else {
                    // 使用设备费率服务计算
                    actualAmount = deviceFeeService.calculateUseFee(device.getId(), actualMinutes);
                }
                
                // 设置实际费用
                order.setActualAmount(actualAmount);
                log.info("计算实际费用：订单编号={}，实际费用={}，预设费用={}", 
                        order.getOrderNo(), actualAmount, order.getAmount());
            } catch (Exception e) {
                log.error("计算实际费用异常：订单编号={}", order.getOrderNo(), e);
            }
        }
        
        // 更新订单状态为已完成
        String remark = "用户结束使用，使用时长：" + useDuration;
        
        // 设置订单结束时间和状态
        order.setOrderStatus(2); // 2-已完成
        order.setEndTime(now);
        order.setRemark(remark);
        boolean updated = orderService.updateById(order);
        
        if (updated) {
            log.info("更新订单状态成功：订单编号={}，状态={}", order.getOrderNo(), order.getOrderStatus());
            
            // 发布订单完成事件
            eventPublisher.publishEvent(new OrderStatusChangeEvent(
                    this, order, OrderStatusChangeEvent.OrderStatusType.COMPLETED));
        }
        
        // 检查用户是否还有其他使用该设备的订单
        LambdaQueryWrapper<Order> activeOrdersQuery = new LambdaQueryWrapper<>();
        activeOrdersQuery.eq(Order::getDeviceId, device.getId())
                        .eq(Order::getUserId, userIdInt)
                        .eq(Order::getOrderStatus, 1) // 1-进行中
                        .ne(Order::getId, orderId); // 排除当前订单
        List<Order> userActiveOrdersOnDevice = orderService.list(activeOrdersQuery);
        
        // 只有当用户没有其他使用该设备的订单时，才减少设备使用人数
        if (userActiveOrdersOnDevice.isEmpty()) {
            // 减少设备使用人数
            boolean decrementResult = deviceService.decrementCurrentUsers(device.getId());
            if (decrementResult) {
                log.info("设备[{}]使用人数减少成功", device.getId());
            } else {
                log.warn("设备[{}]使用人数减少失败，可能当前使用人数已为0", device.getId());
            }
            
            // 检查设备是否还有其他用户在使用
            LambdaQueryWrapper<Order> otherUsersQuery = new LambdaQueryWrapper<>();
            otherUsersQuery.eq(Order::getDeviceId, device.getId())
                          .eq(Order::getOrderStatus, 1) // 1-进行中
                          .ne(Order::getUserId, userIdInt); // 排除当前用户
            long otherUsersCount = orderService.count(otherUsersQuery);
            
            // 如果没有其他用户在使用该设备，则将设备状态设置为空闲
            if (otherUsersCount == 0) {
                device.setInUse(0); // 0-空闲
                deviceService.updateById(device);
                log.info("设备[{}]没有其他用户使用，状态更新为空闲", device.getId());
            } else {
                log.info("设备[{}]还有其他用户正在使用，保持使用中状态", device.getId());
            }
        } else {
            log.info("用户[{}]还有其他订单正在使用设备[{}]，不减少设备使用人数", userIdInt, device.getId());
        }
        
        // 使用DeviceStatusService同步设备状态
        deviceStatusService.syncDeviceOrderStatus(device.getId(), null);
        
        // 记录设备使用结束日志
        DeviceLog deviceLog = new DeviceLog();
        deviceLog.setDeviceId(device.getId());
        deviceLog.setDeviceNo(device.getDeviceNo());
        deviceLog.setOperatorId(userIdInt);
        deviceLog.setOperatorType(3); // 3-小程序用户
        deviceLog.setLogType("END_USE");
        deviceLog.setContent("订单[" + order.getOrderNo() + "]结束使用设备，使用时长：" + useDuration);
        deviceLog.setCreateTime(now);
        
        // 如果有位置信息，记录位置
        if (device.getLatitude() != null && device.getLongitude() != null) {
            deviceLog.setLatitude(device.getLatitude().doubleValue());
            deviceLog.setLongitude(device.getLongitude().doubleValue());
            deviceLog.setAddress(device.getAddress());
        }
        
        deviceLogService.save(deviceLog);
        
        // 返回订单信息
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        BeanUtils.copyProperties(order, orderInfoVO);
        orderInfoVO.setOrderId(order.getId().longValue());
        orderInfoVO.setUserId(order.getUserId().longValue());
        orderInfoVO.setDeviceId(order.getDeviceId().longValue());
        orderInfoVO.setShopId(order.getShopId().longValue());
        
        // 获取设备信息，补充设备名称
        orderInfoVO.setDeviceName(device.getDeviceName());
        orderInfoVO.setQrCode(device.getBindCode());
        
        // 获取门店信息，补充门店名称
        Shop shop = shopService.getById(order.getShopId());
        if (shop != null) {
            orderInfoVO.setShopName(shop.getShopName());
        }
        
        // 设置实际使用时长和实际费用
        orderInfoVO.setDuration(actualMinutes);
        orderInfoVO.setActualAmount(order.getActualAmount());
        
        log.info("结束使用设备成功: orderId={}, 使用时长={}, 实际费用={}", 
                orderId, actualMinutes, order.getActualAmount());
        return orderInfoVO;
    }

    @Override
    public List<OrderInfoVO> getUserOrders(Long userId, Integer status, Integer page, Integer size) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 设置默认值
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        log.info("查询用户订单列表: userId={}, status={}, page={}, size={}", userId, status, page, size);

        // 构建查询条件
        LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<>();
        
        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");
        
        queryWrapper.eq(Order::getUserId, userIdInt);
        if (status != null) {
            queryWrapper.eq(Order::getOrderStatus, status);
        }
        queryWrapper.orderByDesc(Order::getCreateTime);

        // 执行分页查询
        Page<Order> pageParam = new Page<>(page, size);
        Page<Order> pageResult = orderService.page(pageParam, queryWrapper);
        
        // 记录查询结果
        log.info("用户订单查询结果: userId={}, 总记录数={}, 当前页记录数={}", 
                userId, pageResult.getTotal(), pageResult.getRecords().size());

        // 转换为VO列表
        List<OrderInfoVO> orderInfoVOList = new ArrayList<>();
        for (Order order : pageResult.getRecords()) {
            OrderInfoVO orderInfoVO = new OrderInfoVO();
            BeanUtils.copyProperties(order, orderInfoVO);
            orderInfoVO.setOrderId(order.getId().longValue());
            orderInfoVO.setUserId(order.getUserId().longValue());
            orderInfoVO.setDeviceId(order.getDeviceId().longValue());
            orderInfoVO.setShopId(order.getShopId() != null ? order.getShopId().longValue() : null);
            
            // 设置订单状态
            orderInfoVO.setStatus(order.getOrderStatus());
            
            // 设置金额信息
            orderInfoVO.setAmount(order.getAmount());
            orderInfoVO.setPayAmount(order.getAmount()); // 实付金额，假设与订单金额相同
            
            // 设置支付信息
            if ("WXPAY".equals(order.getPayType())) {
                orderInfoVO.setPayType(1); // 1-微信支付
            } else if ("ALIPAY".equals(order.getPayType())) {
                orderInfoVO.setPayType(2); // 2-支付宝
            } else {
                orderInfoVO.setPayType(null);
            }
            orderInfoVO.setPayStatus(order.getPayStatus());
            
            // 计算使用时长（分钟）
            if (order.getStartTime() != null) {
                LocalDateTime endTimeForCalc = order.getEndTime() != null ? order.getEndTime() : LocalDateTime.now();
                long minutes = java.time.Duration.between(order.getStartTime(), endTimeForCalc).toMinutes();
                orderInfoVO.setDuration((int) minutes);
            }
            
            // 设置使用状态
            if (order.getOrderStatus() != null) {
                switch (order.getOrderStatus()) {
                    case 0: // 未支付
                        orderInfoVO.setUseStatus(0); // 未使用
                        break;
                    case 1: // 使用中
                        orderInfoVO.setUseStatus(1); // 使用中
                        break;
                    case 2: // 已完成
                        orderInfoVO.setUseStatus(2); // 已结束
                        break;
                    case 3: // 已取消
                        orderInfoVO.setUseStatus(0); // 未使用
                        break;
                    default:
                        orderInfoVO.setUseStatus(0);
                }
            }
            
            // 设置备注
            orderInfoVO.setRemark(order.getRemark());
            
            // 获取设备信息，补充设备名称和位置信息
            Device device = deviceService.getById(order.getDeviceId());
            if (device != null) {
                orderInfoVO.setDeviceName(device.getDeviceName());
                
                // 设置位置信息
                if (device.getLatitude() != null) {
                    orderInfoVO.setLatitude(device.getLatitude().doubleValue());
                }
                if (device.getLongitude() != null) {
                    orderInfoVO.setLongitude(device.getLongitude().doubleValue());
                }
                orderInfoVO.setAddress(device.getAddress());
                
                // 设置二维码URL
                if (device.getQrcodeUrl() != null) {
                    orderInfoVO.setQrCode(device.getQrcodeUrl());
                } else {
                    // 如果没有完整URL，则使用绑定码
                    orderInfoVO.setQrCode(device.getBindCode());
                }
            }
            
            // 获取门店信息，补充门店名称
            if (order.getShopId() != null) {
                Shop shop = shopService.getById(order.getShopId());
                if (shop != null) {
                    orderInfoVO.setShopName(shop.getShopName());
                }
            }
            
            orderInfoVOList.add(orderInfoVO);
        }

        return orderInfoVOList;
    }

    @Override
    public OrderInfoVO getOrderDetail(Long userId, Long orderId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (orderId == null) {
            throw new BusinessException("订单ID不能为空");
        }

        log.info("获取订单详情: userId={}, orderId={}", userId, orderId);

        // 获取订单信息
        Order order = orderService.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: orderId={}", orderId);
            throw new BusinessException("订单不存在");
        }

        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");

        // 验证订单所属用户
        if (order.getUserId() == null) {
            log.warn("订单用户ID为空: orderId={}", orderId);
            throw new BusinessException("订单数据异常");
        }

        if (order.getUserId().intValue() != userIdInt.intValue()) {
            log.warn("无权查看此订单: userId={}, orderId={}, orderUserId={}", userId, orderId, order.getUserId());
            throw new BusinessException("无权查看此订单");
        }

        // 构建返回结果
        OrderInfoVO orderInfoVO = new OrderInfoVO();
        BeanUtils.copyProperties(order, orderInfoVO);
        orderInfoVO.setOrderId(order.getId().longValue());
        orderInfoVO.setUserId(order.getUserId().longValue());
        orderInfoVO.setDeviceId(order.getDeviceId().longValue());
        orderInfoVO.setShopId(order.getShopId().longValue());

        // 获取设备信息，补充设备名称和二维码
        Device device = deviceService.getById(order.getDeviceId());
        if (device != null) {
            orderInfoVO.setDeviceName(device.getDeviceName());
            orderInfoVO.setQrCode(device.getBindCode());
        }

        // 获取门店信息，补充门店名称
        Shop shop = shopService.getById(order.getShopId());
        if (shop != null) {
            orderInfoVO.setShopName(shop.getShopName());
        }

        log.info("获取订单详情成功: orderId={}", orderId);
        return orderInfoVO;
    }

    @Override
    public DeviceInfoVO getDeviceStatus(Long userId, String deviceId) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        if (deviceId == null) {
            throw new BusinessException("设备ID不能为空");
        }

        log.info("获取设备状态: userId={}, deviceId={}", userId, deviceId);

        // 获取设备信息
        Device device = null;
        
        // 首先尝试按绑定码查询
        device = deviceService.getDeviceByBindCode(deviceId);
        log.info("通过绑定码查询设备: bindCode={}, 结果={}", deviceId, device != null ? "成功" : "失败");
        
        // 如果没找到，尝试按MAC地址查询
        if (device == null) {
            device = deviceService.getDeviceByMacAddress(deviceId);
            log.info("通过MAC地址查询设备: mac={}, 结果={}", deviceId, device != null ? "成功" : "失败");
        }
        
        // 如果没找到，尝试去除前导零后按设备编号查询
        if (device == null) {
            String deviceNoWithoutLeadingZeros = deviceId.replaceFirst("^0+", "");
            if (!deviceNoWithoutLeadingZeros.equals(deviceId)) {
                // 只有在去除前导零后与原始值不同时才进行查询
                device = deviceService.getDeviceByDeviceNo(deviceNoWithoutLeadingZeros);
                log.info("通过去除前导零后的设备编号查询设备: deviceNo={}, 结果={}", 
                        deviceNoWithoutLeadingZeros, device != null ? "成功" : "失败");
            }
        }
        
        // 如果仍未找到，尝试按ID查询
        if (device == null) {
            try {
                // 检查是否是数字ID
                if (deviceId.matches("\\d+")) {
                    Long id = Long.parseLong(deviceId);
                    device = deviceService.getById(id.intValue());
                    log.info("通过数字ID查询设备: id={}, 结果={}", id, device != null ? "成功" : "失败");
                }
            } catch (Exception e) {
                log.warn("设备ID转换异常: deviceId={}, error={}", deviceId, e.getMessage());
            }
        }
        
        // 如果是UUID格式，尝试遍历所有设备查找匹配的MAC地址
        if (device == null && deviceId.matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}")) {
            log.info("尝试遍历查找匹配UUID格式MAC地址的设备: {}", deviceId);
            List<Device> allDevices = deviceService.list();
            for (Device d : allDevices) {
                if (d.getMacAddress() != null && d.getMacAddress().equalsIgnoreCase(deviceId)) {
                    device = d;
                    log.info("通过遍历找到匹配MAC地址的设备: deviceId={}, mac={}", d.getId(), deviceId);
                    break;
                }
            }
        }
        
        if (device == null) {
            log.error("设备不存在: deviceId={}", deviceId);
            throw new BusinessException("设备不存在");
        }

        // 检查设备状态，只有故障状态(3)的设备才禁止使用
        if (device.getStatus() == 3) {
            log.warn("设备故障，无法使用: deviceId={}, status={}", device.getId(), device.getStatus());
            throw new BusinessException("设备故障，无法使用");
        }

        // 如果设备状态为维护中(2)，记录日志但允许使用
        if (device.getStatus() == 2) {
            log.warn("设备处于维护状态，但仍允许使用: deviceId={}", device.getId());
        }

        // 获取门店信息
        Shop shop = shopService.getById(device.getShopId());
        if (shop == null) {
            log.warn("设备所属门店不存在: deviceId={}, shopId={}", device.getId(), device.getShopId());
            throw new BusinessException("设备所属门店不存在");
        }

        // 获取设备费率
        DeviceFee deviceFee = deviceFeeService.getDeviceFeeByDeviceId(device.getId());
        BigDecimal hourlyRate;
        if (deviceFee != null) {
            // 使用统一的计费计算工具
            hourlyRate = DeviceFeeCalculator.getHourlyRate(deviceFee);
        } else {
            // 否则使用计算方法
            hourlyRate = deviceFeeService.calculateUseFee(device.getId(), 60);
        }

        // 构建返回结果
        DeviceInfoVO deviceInfoVO = new DeviceInfoVO();
        BeanUtils.copyProperties(device, deviceInfoVO);
        deviceInfoVO.setDeviceId(device.getId().longValue());
        deviceInfoVO.setShopId(shop.getId().longValue());
        deviceInfoVO.setShopName(shop.getName());
        deviceInfoVO.setHourlyRate(hourlyRate);
        deviceInfoVO.setAvailable(device.getStatus() == 1);
        deviceInfoVO.setQrCode(device.getBindCode());
        deviceInfoVO.setMacAddress(device.getMacAddress());

        // 检查设备是否有正在使用中的订单
        Order currentOrder = orderService.getById(device.getId());
        if (currentOrder != null) {
            deviceInfoVO.setCurrentOrderId(currentOrder.getId().longValue());
        }

        log.info("获取设备状态成功: deviceId={}, deviceName={}", device.getId(), device.getDeviceName());
        return deviceInfoVO;
    }

    @Override
    public boolean checkOrderPaymentStatus(Long userId, Long orderId) {
        log.info("检查订单支付状态: userId={}, orderId={}", userId, orderId);
        
        // 验证订单是否存在
        Order order = orderService.getById(orderId);
        if (order == null) {
            log.warn("订单不存在: orderId={}", orderId);
            throw new BusinessException("订单不存在");
        }
        
        // 安全地将Long转换为Integer
        Integer userIdInt = safeConvertLongToInt(userId, "用户ID");

        // 验证订单是否属于当前用户
        if (order.getUserId() == null) {
            log.warn("订单用户ID为空: orderId={}", orderId);
            throw new BusinessException("订单数据异常");
        }

        if (order.getUserId().intValue() != userIdInt.intValue()) {
            log.warn("订单不属于当前用户: userId={}, orderId={}, orderUserId={}", userId, orderId, order.getUserId());
            throw new BusinessException("无权查询此订单");
        }
        
        // 检查订单支付状态
        if (order.getPayStatus() != null && order.getPayStatus() == 1) {
            log.info("订单已支付: orderId={}", orderId);
            return true;
        }
        
        // 如果订单未支付，查询微信支付状态
        try {
            WxPayService wxPayService = SpringUtils.getBean(WxPayService.class);
            boolean payStatus = wxPayService.queryWxPayOrderStatus(orderId);
            
            // 如果微信支付成功但订单状态未更新，则更新订单状态
            if (payStatus && (order.getPayStatus() == null || order.getPayStatus() != 1)) {
                log.info("微信支付成功但订单状态未更新，更新订单状态: orderId={}", orderId);
                
                // 使用orderService.payOrder方法更新订单状态并触发事件
                boolean result = orderService.payOrder(order.getOrderNo(), "WXPAY", order.getTransactionId());
                
                if (result) {
                    log.info("订单支付成功，已更新订单状态: orderId={}, orderNo={}", orderId, order.getOrderNo());
                    
                    // 获取设备信息
                    Device device = deviceService.getById(order.getDeviceId());
                    if (device != null) {
                        // 设置设备为使用中
                        device.setInUse(1);
                        deviceService.updateById(device);
                        
                        // ===================== 关键修改点 =====================
                        // 检查同一用户是否已经在使用该设备（包括当前订单）
                        LambdaQueryWrapper<Order> allUserOrdersQuery = new LambdaQueryWrapper<>();
                        allUserOrdersQuery.eq(Order::getDeviceId, device.getId())
                                        .eq(Order::getUserId, order.getUserId())
                                        .eq(Order::getOrderStatus, 1); // 1-进行中
                        List<Order> allUserOrdersOnDevice = orderService.list(allUserOrdersQuery);
                        
                        // 如果用户已经有进行中的订单（包括当前订单），且不止一个，说明已经在使用该设备
                        boolean userAlreadyUsingDevice = allUserOrdersOnDevice.size() > 1;
                        
                        // 只有当用户不是已经在使用该设备时，才增加设备使用人数
                        if (!userAlreadyUsingDevice) {
                            deviceService.incrementCurrentUsers(device.getId());
                            log.info("用户开始使用设备，增加设备使用人数: userId={}, deviceId={}", 
                                    userId, device.getId());
                        } else {
                            log.info("用户已经在使用该设备，不增加设备使用人数: userId={}, deviceId={}", 
                                    userId, device.getId());
                        }
                        // ===================== 关键修改点结束 =====================
                        
                        // 记录设备日志
                        DeviceLog deviceLog = new DeviceLog();
                        deviceLog.setDeviceId(device.getId());
                        deviceLog.setDeviceNo(device.getDeviceNo());
                        deviceLog.setOperatorId(order.getUserId().intValue());
                        deviceLog.setOperatorType(3); // 3-小程序用户
                        deviceLog.setLogType("PAY_SUCCESS");
                        deviceLog.setContent("订单支付成功：" + order.getOrderNo() + "，开始使用设备");
                        deviceLog.setCreateTime(LocalDateTime.now());
                        deviceLogService.save(deviceLog);
                        
                        log.info("记录设备日志成功: deviceId={}, logType={}", device.getId(), "PAY_SUCCESS");
                    } else {
                        log.warn("设备不存在，无法更新设备状态: deviceId={}", order.getDeviceId());
                    }
                }
            }
            
            return payStatus;
        } catch (Exception e) {
            log.error("查询微信支付状态异常", e);
            return false;
        }
    }

    /**
     * 安全地将Long转换为Integer，避免数值溢出
     *
     * @param longValue Long类型值
     * @param paramName 参数名称（用于日志记录）
     * @return Integer类型值
     */
    private Integer safeConvertLongToInt(Long longValue, String paramName) {
        if (longValue == null) {
            log.warn("{}为空，无法转换", paramName);
            throw new BusinessException(paramName + "不能为空");
        }

        try {
            Integer intValue = longValue.intValue();
            log.debug("{}类型转换: Long {} -> Integer {}", paramName, longValue, intValue);
            return intValue;
        } catch (Exception e) {
            log.error("{}类型转换异常: {} = {}, error={}", paramName, paramName, longValue, e.getMessage());
            throw new BusinessException(paramName + "格式错误");
        }
    }

    /**
     * 获取订单状态描述
     *
     * @param orderStatus 订单状态
     * @return 状态描述
     */
    private String getOrderStatusDesc(Integer orderStatus) {
        if (orderStatus == null) {
            return "未知";
        }
        switch (orderStatus) {
            case 0: return "待支付";
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "已取消";
            case 4: return "已退款";
            default: return "未知状态";
        }
    }
}