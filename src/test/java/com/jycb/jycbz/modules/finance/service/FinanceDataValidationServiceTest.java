package com.jycb.jycbz.modules.finance.service;

import com.jycb.jycbz.modules.finance.service.impl.FinanceDataValidationServiceImpl;
import com.jycb.jycbz.modules.finance.vo.ValidationResultVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 财务数据验证服务测试类
 * 用于验证余额一致性检查修复效果
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class FinanceDataValidationServiceTest {

    @Autowired
    private FinanceDataValidationService financeDataValidationService;

    /**
     * 测试账户余额一致性检查
     */
    @Test
    public void testValidateAccountBalanceConsistency() {
        log.info("开始测试账户余额一致性检查");
        
        try {
            ValidationResultVO result = financeDataValidationService.validateAccountBalanceConsistency();
            
            log.info("测试结果：");
            log.info("- 验证类型: {}", result.getValidationType());
            log.info("- 是否有效: {}", result.getIsValid());
            log.info("- 总记录数: {}", result.getTotalRecords());
            log.info("- 有效记录数: {}", result.getValidRecords());
            log.info("- 无效记录数: {}", result.getInvalidRecords());
            log.info("- 准确率: {}%", result.getAccuracyRate());
            log.info("- 级别: {}", result.getLevel());
            log.info("- 描述: {}", result.getDescription());
            
            if (result.getWarnings() != null && !result.getWarnings().isEmpty()) {
                log.info("警告信息:");
                for (String warning : result.getWarnings()) {
                    log.info("  - {}", warning);
                }
            }
            
            if (result.getSuggestions() != null && !result.getSuggestions().isEmpty()) {
                log.info("建议信息:");
                for (String suggestion : result.getSuggestions()) {
                    log.info("  - {}", suggestion);
                }
            }
            
            // 验证修复效果
            if (result.getAccuracyRate() >= 95.0) {
                log.info("✅ 测试通过：准确率达到 {}%，修复成功！", result.getAccuracyRate());
            } else {
                log.warn("⚠️ 测试警告：准确率仅为 {}%，可能需要进一步优化", result.getAccuracyRate());
            }
            
        } catch (Exception e) {
            log.error("测试失败", e);
            throw e;
        }
    }

    /**
     * 测试实时一致性检查
     */
    @Test
    public void testRealTimeConsistencyCheck() {
        log.info("开始测试实时一致性检查");
        
        try {
            var result = financeDataValidationService.realTimeConsistencyCheck();
            
            log.info("实时检查结果：");
            log.info("- 整体一致性: {}", result.get("overallConsistent"));
            log.info("- 余额一致性: {}", result.get("balanceConsistent"));
            log.info("- 订单一致性: {}", result.get("orderConsistent"));
            log.info("- 分成一致性: {}", result.get("commissionConsistent"));
            
            if (result.get("issues") != null) {
                log.info("发现的问题: {}", result.get("issues"));
            }
            
        } catch (Exception e) {
            log.error("实时检查测试失败", e);
            throw e;
        }
    }

    /**
     * 测试账户余额修复功能
     */
    @Test
    public void testRepairAccountBalance() {
        log.info("开始测试账户余额修复功能");
        
        try {
            // 先检查当前状态
            ValidationResultVO beforeResult = financeDataValidationService.validateAccountBalanceConsistency();
            log.info("修复前准确率: {}%", beforeResult.getAccuracyRate());
            
            if (beforeResult.getInvalidRecords() > 0) {
                // 执行修复
                var repairResult = financeDataValidationService.repairAccountBalance();
                
                log.info("修复结果：");
                log.info("- 是否成功: {}", repairResult.get("success"));
                log.info("- 消息: {}", repairResult.get("message"));
                log.info("- 修复详情: {}", repairResult.get("data"));
                
                // 再次检查修复效果
                ValidationResultVO afterResult = financeDataValidationService.validateAccountBalanceConsistency();
                log.info("修复后准确率: {}%", afterResult.getAccuracyRate());
                
                if (afterResult.getAccuracyRate() > beforeResult.getAccuracyRate()) {
                    log.info("✅ 修复成功：准确率从 {}% 提升到 {}%", 
                            beforeResult.getAccuracyRate(), afterResult.getAccuracyRate());
                } else {
                    log.warn("⚠️ 修复效果不明显：准确率从 {}% 变为 {}%", 
                            beforeResult.getAccuracyRate(), afterResult.getAccuracyRate());
                }
            } else {
                log.info("✅ 当前账户余额已经一致，无需修复");
            }
            
        } catch (Exception e) {
            log.error("余额修复测试失败", e);
            throw e;
        }
    }
}
