-- 门店管理员API补充接口数据库补丁
-- 执行时间：2024-01-01
-- 说明：为门店管理员API补充接口添加必要的数据库字段和表

-- 1. 为财务账户表添加提现密码字段
ALTER TABLE `jy_finance_account` 
ADD COLUMN `withdraw_password` VARCHAR(255) NULL COMMENT '提现密码' AFTER `version`;

-- 2. 创建设备故障报告表（如果不存在）
CREATE TABLE IF NOT EXISTS `jy_device_fault_report` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '故障报告ID',
  `fault_no` VARCHAR(50) NOT NULL COMMENT '故障编号',
  `device_id` BIGINT NOT NULL COMMENT '设备ID',
  `shop_id` BIGINT NOT NULL COMMENT '门店ID',
  `fault_type` INT NOT NULL COMMENT '故障类型(1:无法开锁 2:无法关锁 3:电量异常 4:通信异常 5:其他)',
  `description` TEXT COMMENT '故障描述',
  `images` JSON COMMENT '故障图片URL数组',
  `urgency` INT DEFAULT 2 COMMENT '紧急程度(1:低 2:中 3:高)',
  `status` INT DEFAULT 0 COMMENT '处理状态(0:待处理 1:处理中 2:已解决 3:已关闭)',
  `report_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
  `assign_time` DATETIME NULL COMMENT '分配时间',
  `assignee` VARCHAR(100) NULL COMMENT '处理人员',
  `estimated_fix_time` DATETIME NULL COMMENT '预计修复时间',
  `actual_fix_time` DATETIME NULL COMMENT '实际修复时间',
  `fix_description` TEXT NULL COMMENT '修复说明',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_fault_no` (`fault_no`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_fault_type` (`fault_type`),
  KEY `idx_report_time` (`report_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备故障报告表';

-- 3. 创建设备维护申请表（如果不存在）
CREATE TABLE IF NOT EXISTS `jy_device_maintenance_request` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '维护申请ID',
  `request_no` VARCHAR(50) NOT NULL COMMENT '申请编号',
  `device_id` BIGINT NOT NULL COMMENT '设备ID',
  `shop_id` BIGINT NOT NULL COMMENT '门店ID',
  `maintenance_type` INT NOT NULL COMMENT '维护类型(1:定期保养 2:深度清洁 3:部件更换)',
  `description` TEXT COMMENT '维护说明',
  `urgency` INT DEFAULT 2 COMMENT '紧急程度(1:低 2:中 3:高)',
  `preferred_time` DATETIME NULL COMMENT '期望维护时间',
  `status` INT DEFAULT 0 COMMENT '申请状态(0:待安排 1:已安排 2:进行中 3:已完成 4:已取消)',
  `assign_time` DATETIME NULL COMMENT '安排时间',
  `assignee` VARCHAR(100) NULL COMMENT '维护人员',
  `scheduled_time` DATETIME NULL COMMENT '计划维护时间',
  `actual_start_time` DATETIME NULL COMMENT '实际开始时间',
  `actual_end_time` DATETIME NULL COMMENT '实际结束时间',
  `maintenance_result` TEXT NULL COMMENT '维护结果',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_no` (`request_no`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_preferred_time` (`preferred_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备维护申请表';

-- 4. 创建设备清洁申请表（如果不存在）
CREATE TABLE IF NOT EXISTS `jy_device_cleaning_request` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '清洁申请ID',
  `request_no` VARCHAR(50) NOT NULL COMMENT '申请编号',
  `device_id` BIGINT NOT NULL COMMENT '设备ID',
  `shop_id` BIGINT NOT NULL COMMENT '门店ID',
  `cleaning_type` INT NOT NULL COMMENT '清洁类型(1:日常清洁 2:深度清洁 3:消毒处理)',
  `description` TEXT COMMENT '清洁说明',
  `urgency` INT DEFAULT 2 COMMENT '紧急程度(1:低 2:中 3:高)',
  `preferred_time` DATETIME NULL COMMENT '期望清洁时间',
  `status` INT DEFAULT 0 COMMENT '申请状态(0:待安排 1:已安排 2:进行中 3:已完成 4:已取消)',
  `assign_time` DATETIME NULL COMMENT '安排时间',
  `assignee` VARCHAR(100) NULL COMMENT '清洁人员',
  `scheduled_time` DATETIME NULL COMMENT '计划清洁时间',
  `actual_start_time` DATETIME NULL COMMENT '实际开始时间',
  `actual_end_time` DATETIME NULL COMMENT '实际结束时间',
  `cleaning_result` TEXT NULL COMMENT '清洁结果',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_no` (`request_no`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_cleaning_type` (`cleaning_type`),
  KEY `idx_preferred_time` (`preferred_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备清洁申请表';

-- 5. 创建门店通知表（如果不存在）
CREATE TABLE IF NOT EXISTS `jy_shop_notification` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `shop_id` BIGINT NOT NULL COMMENT '门店ID',
  `title` VARCHAR(200) NOT NULL COMMENT '通知标题',
  `content` TEXT COMMENT '通知内容',
  `type` VARCHAR(50) NOT NULL COMMENT '通知类型',
  `priority` INT DEFAULT 1 COMMENT '优先级(1:低 2:中 3:高)',
  `is_read` BOOLEAN DEFAULT FALSE COMMENT '是否已读',
  `related_id` BIGINT NULL COMMENT '关联ID',
  `related_type` VARCHAR(50) NULL COMMENT '关联类型',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `read_time` DATETIME NULL COMMENT '阅读时间',
  PRIMARY KEY (`id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_priority` (`priority`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店通知表';

-- 6. 创建系统配置表（如果不存在）
CREATE TABLE IF NOT EXISTS `jy_system_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` VARCHAR(50) NOT NULL COMMENT '配置类型',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `description` VARCHAR(200) NULL COMMENT '配置描述',
  `is_encrypted` BOOLEAN DEFAULT FALSE COMMENT '是否加密',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config` (`config_type`, `config_key`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';

-- 7. 插入系统配置数据
INSERT INTO `jy_system_config` (`config_type`, `config_key`, `config_value`, `description`) VALUES
('app', 'name', '今夜城堡门店端', '应用名称'),
('app', 'version', '1.0.0', '应用版本'),
('app', 'api_version', 'v1.0', 'API版本'),
('upload', 'max_size', '10485760', '上传文件最大大小(字节)'),
('upload', 'image_types', 'jpg,jpeg,png,gif,bmp,webp', '支持的图片类型'),
('upload', 'doc_types', 'pdf,doc,docx,xls,xlsx,txt', '支持的文档类型'),
('finance', 'withdraw_min_amount', '100.00', '最小提现金额'),
('finance', 'withdraw_max_amount', '10000.00', '最大提现金额'),
('finance', 'withdraw_fee_rate', '0.001', '提现手续费率'),
('finance', 'withdraw_daily_limit', '3', '每日提现次数限制'),
('order', 'max_duration', '480', '最大使用时长(分钟)'),
('order', 'min_duration', '30', '最小使用时长(分钟)'),
('order', 'auto_complete_time', '10', '自动完成时间(分钟)'),
('cos', 'region', 'ap-guangzhou', '腾讯云COS地域'),
('cos', 'bucket_name', 'jycb-files', '腾讯云COS存储桶名称'),
('cos', 'secret_id', '', '腾讯云COS SecretId（需要配置）'),
('cos', 'secret_key', '', '腾讯云COS SecretKey（需要配置）')
ON DUPLICATE KEY UPDATE 
  `config_value` = VALUES(`config_value`),
  `description` = VALUES(`description`),
  `update_time` = CURRENT_TIMESTAMP;

-- 8. 为现有门店账户设置默认提现密码（可选，建议门店管理员自行设置）
-- UPDATE `jy_finance_account` SET `withdraw_password` = MD5('123456') WHERE `account_type` = 'shop' AND `withdraw_password` IS NULL;

-- 执行完成提示
SELECT '门店管理员API数据库补丁执行完成！' AS message;
