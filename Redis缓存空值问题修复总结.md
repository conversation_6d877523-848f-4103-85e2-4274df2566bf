# Redis缓存空值问题修复总结

## 🔍 **问题描述**

### 错误信息
```
java.lang.IllegalArgumentException: Cache 'commission_config' does not allow 'null' values; 
Avoid storing null via '@Cacheable(unless="#result == null")' or configure RedisCache to allow 'null' via RedisCacheConfiguration
```

### 问题原因
1. **缓存配置问题**：Redis缓存默认不允许存储null值
2. **业务逻辑**：当门店没有分成配置时，`getCommissionConfigSource()` 方法可能返回null
3. **缓存注解缺失**：部分`@Cacheable`注解没有添加`unless="#result == null"`条件

## ✅ **修复方案**

### **1. 修复缓存注解**

#### CommissionConfigServiceImpl.java
```java
// 修复前
@Cacheable(key = "'system'")
public CommissionConfig getSystemConfig() {
    return baseMapper.getSystemConfig();
}

// 修复后
@Cacheable(value = "commission_config", key = "'system'", unless = "#result == null")
public CommissionConfig getSystemConfig() {
    return baseMapper.getSystemConfig();
}
```

#### 修复的方法列表
- ✅ `getSystemConfig()` - 添加了`unless = "#result == null"`
- ✅ `getEntityConfig()` - 添加了`unless = "#result == null"`
- ✅ `getPartnerConfig()` - 添加了`unless = "#result == null"`
- ✅ `getShopConfig()` - 添加了`unless = "#result == null"`

### **2. 优化业务逻辑**

#### ShopCommissionServiceImpl.java
```java
// 优化getCommissionConfigSource方法，减少重复查询
@Override
public String getCommissionConfigSource(Long shopId) {
    if (shopId == null) {
        return "default";
    }

    try {
        Shop shop = null; // 只查询一次门店信息
        
        // 检查门店级配置
        CommissionConfig shopConfig = commissionConfigService.getShopConfig(shopId.intValue());
        if (shopConfig != null && shopConfig.getShopRate() != null) {
            return "shop";
        }

        // 获取门店信息（只查询一次）
        shop = shopService.getById(shopId);
        
        // ... 其他逻辑
        
        return "default";
    } catch (Exception e) {
        log.error("获取门店分成配置来源失败，门店ID: {}", shopId, e);
        return "error"; // 确保不返回null
    }
}
```

### **3. 创建Redis缓存配置**

#### RedisCacheConfig.java
```java
@Configuration
@EnableCaching
public class RedisCacheConfig {

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        // 特定缓存配置
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // 分成配置缓存 - 允许null值
        cacheConfigurations.put("commission_config", 
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(6)) // 6小时过期
                .serializeKeysWith(...)
                .serializeValuesWith(...)
                // 注意：不调用disableCachingNullValues()，允许缓存null值
        );

        // 其他缓存配置...
        
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
```

## 🎯 **缓存策略设计**

### **缓存分类**

| 缓存名称 | 允许null值 | 过期时间 | 用途 |
|---------|-----------|---------|------|
| `commission_config` | ✅ 是 | 6小时 | 分成配置（可能为空） |
| `shopCommissionRate` | ❌ 否 | 2小时 | 门店分成比例 |
| `permissions` | ❌ 否 | 30分钟 | 用户权限 |
| `userInfo` | ❌ 否 | 15分钟 | 用户信息 |
| `shopInfo` | ❌ 否 | 1小时 | 门店信息 |
| `deviceInfo` | ❌ 否 | 2小时 | 设备信息 |
| `statistics` | ❌ 否 | 10分钟 | 统计数据 |
| `financeData` | ❌ 否 | 5分钟 | 财务数据 |

### **缓存策略说明**

1. **分成配置缓存**：
   - 允许null值，因为门店可能没有自定义配置
   - 较长过期时间（6小时），因为配置变更频率低

2. **业务数据缓存**：
   - 不允许null值，使用`unless="#result == null"`
   - 根据数据变更频率设置不同过期时间

3. **财务数据缓存**：
   - 较短过期时间（5分钟），保证数据实时性

## 🔧 **部署步骤**

### **1. 代码部署**
```bash
# 1. 编译项目
mvn clean compile

# 2. 重启应用
# 重启Spring Boot应用以加载新的缓存配置
```

### **2. 缓存清理**
```bash
# 清理现有缓存（可选）
redis-cli FLUSHDB
```

### **3. 验证修复**
```bash
# 1. 检查应用启动日志
tail -f logs/application.log | grep "Redis缓存管理器初始化完成"

# 2. 测试门店财务功能
curl -H "Authorization: Bearer <token>" \
     http://localhost:8081/shop/finance/bank-cards

# 3. 检查Redis缓存
redis-cli KEYS "*commission_config*"
```

## 📊 **预期效果**

### **修复前**
- ❌ 缓存存储null值时抛出异常
- ❌ 门店财务功能访问失败
- ❌ 日志中出现大量缓存错误

### **修复后**
- ✅ 缓存正常工作，支持null值存储
- ✅ 门店财务功能正常访问
- ✅ 提升查询性能，减少数据库访问
- ✅ 日志清洁，无缓存相关错误

## 🚨 **注意事项**

1. **缓存一致性**：
   - 分成配置变更时需要清除相关缓存
   - 建议在配置更新时调用缓存清除方法

2. **内存使用**：
   - 监控Redis内存使用情况
   - 根据实际情况调整缓存过期时间

3. **性能监控**：
   - 监控缓存命中率
   - 定期检查缓存效果

## 🔍 **故障排查**

### **如果仍有缓存问题**
```bash
# 1. 检查Redis连接
redis-cli ping

# 2. 查看缓存配置
redis-cli CONFIG GET "*cache*"

# 3. 监控缓存操作
redis-cli MONITOR

# 4. 检查应用日志
grep -i "cache\|redis" logs/application.log
```

### **常见问题**
1. **缓存配置未生效**：确保重启了应用
2. **Redis连接失败**：检查Redis服务状态
3. **序列化问题**：检查对象是否可序列化

---

**修复完成后，门店财务功能应该能正常工作，不再出现缓存相关错误。**
