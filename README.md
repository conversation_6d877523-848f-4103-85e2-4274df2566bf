# JYCB-Z 微信小程序后端

## 项目介绍

聚优成本(JYCB)是一个综合性的设备管理与财务结算系统，主要用于管理设备全生命周期、处理订单、财务结算等功能。系统支持多种角色（系统管理员、业务主体、合作商、门店）的业务需求。

本项目是JYCB系统的微信小程序后端API模块，提供设备扫描、订单创建、支付、使用等功能。

## 特别说明

设备本身不联网，用户使用时通过小程序上传设备状态，系统通过设备UUID进行校验、绑定和归属管理。用户使用流程在小程序端完成，包括支付和开锁逻辑。同一设备可以被多个用户同时付款开锁使用，没有设备使用唯一性限制。

## 技术栈

- Spring Boot 2.7.x
- MyBatis-Plus 3.5.x
- Sa-Token 1.34.x
- MySQL 8.0
- Redis
- Maven

## 功能模块

### 1. 用户模块

- 微信小程序登录
- 用户信息管理

### 2. 设备模块

- 设备扫描
- 设备状态查询
- 设备使用（开锁/关锁）

### 3. 订单模块

- 订单创建
- 订单支付
- 订单查询
- 订单结束

### 4. 支付模块

- 微信支付
- 支付状态查询
- 退款处理

## 微信小程序使用流程

1. **设备发现**：用户通过小程序扫描设备二维码
2. **设备校验**：系统校验设备UUID和状态
3. **创建订单**：用户创建设备使用订单
4. **支付订单**：用户完成支付
5. **开锁使用**：支付成功后，小程序发送开锁指令
6. **使用中**：用户使用设备，系统记录使用状态
7. **结束使用**：用户通过小程序结束使用，系统计费并关闭订单

## API接口说明

### 用户接口

- `POST /api/wx/miniapp/login` - 微信小程序登录
- `GET /api/wx/miniapp/user/info` - 获取用户信息

### 设备接口

- `GET /api/wx/miniapp/device/scan` - 扫描设备二维码
- `GET /api/wx/miniapp/device/status/{deviceId}` - 获取设备当前状态
- `POST /api/wx/miniapp/device/unlock` - 开锁使用设备
- `POST /api/wx/miniapp/device/end-use/{orderId}` - 结束使用设备

### 订单接口

- `POST /api/wx/miniapp/order/create` - 创建设备使用订单
- `POST /api/wx/miniapp/order/pay/{orderId}` - 支付订单
- `GET /api/wx/miniapp/order/list` - 获取用户订单列表
- `GET /api/wx/miniapp/order/{orderId}` - 获取订单详情

### 支付接口

- `POST /api/wx/miniapp/pay/create/{orderId}` - 创建支付订单
- `GET /api/wx/miniapp/pay/query/{orderId}` - 查询支付状态
- `POST /api/wx/miniapp/pay/close/{orderId}` - 关闭支付订单
- `POST /api/wx/miniapp/pay/refund/{orderId}` - 申请退款

## 部署说明

1. 修改`application.yml`配置文件，设置数据库、Redis、微信小程序等相关配置
2. 执行SQL脚本创建数据库和表
3. 打包项目：`mvn clean package`
4. 运行项目：`java -jar jycb-z.jar`

## 开发环境

- JDK 11+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+ 