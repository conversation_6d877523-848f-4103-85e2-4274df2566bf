version: '3.8'

services:
  # 今夜城堡应用
  jycb-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jycb-z-container
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - SPRING_DATASOURCE_URL=********************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=jycb
      - SPRING_DATASOURCE_PASSWORD=jycb123
      - SPRING_REDIS_HOST=jycb-redis
      - SPRING_REDIS_PORT=6379
      - JAVA_OPTS=-Xms512m -Xmx2g -XX:+UseG1GC
    volumes:
      - ./logs:/jycb/logs
    depends_on:
      - jycb-mysql
      - jycb-redis
    networks:
      - jycb-network
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL数据库
  jycb-mysql:
    image: mysql:8.0
    container_name: jycb-mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root123
      - MYSQL_DATABASE=jycb_z
      - MYSQL_USER=jycb
      - MYSQL_PASSWORD=jycb123
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database_fix_script.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - jycb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis缓存
  jycb-redis:
    image: redis:7-alpine
    container_name: jycb-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - jycb-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  jycb-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
