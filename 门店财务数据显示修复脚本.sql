-- =====================================================
-- 门店财务数据显示问题修复脚本
-- 
-- 问题描述：
-- 1. 门店订单显示用户支付原始金额而非门店分成收益
-- 2. 门店设备统计中总订单数显示异常
-- 
-- 修复内容：
-- 1. 验证分成数据完整性
-- 2. 检查订单与分成明细的关联关系
-- 3. 提供数据修复建议
-- =====================================================

-- 1. 检查分成配置是否正确
SELECT 
    '分成配置检查' as check_type,
    config_type,
    config_id,
    platform_ratio,
    entity_ratio,
    partner_ratio,
    shop_ratio,
    (platform_ratio + entity_ratio + partner_ratio + shop_ratio) as total_ratio,
    status
FROM jy_commission_config 
WHERE status = 1
ORDER BY config_type, config_id;

-- 2. 检查已支付订单与分成明细的匹配情况
SELECT 
    '订单分成匹配检查' as check_type,
    COUNT(DISTINCT o.id) as total_paid_orders,
    COUNT(DISTINCT cd.order_id) as orders_with_commission,
    (COUNT(DISTINCT o.id) - COUNT(DISTINCT cd.order_id)) as missing_commission_orders
FROM jy_order o
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id
WHERE o.pay_status = 1;

-- 3. 查找缺少分成明细的已支付订单
SELECT 
    '缺少分成明细的订单' as check_type,
    o.id as order_id,
    o.order_no,
    o.shop_id,
    o.partner_id,
    o.entity_id,
    o.amount as order_amount,
    o.pay_time,
    o.commission_status
FROM jy_order o
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id
WHERE o.pay_status = 1 
  AND cd.order_id IS NULL
ORDER BY o.pay_time DESC
LIMIT 10;

-- 4. 验证现有分成数据的准确性
SELECT 
    '分成数据验证' as check_type,
    cd.order_id,
    cd.order_no,
    cd.order_amount,
    cd.platform_amount,
    cd.entity_amount,
    cd.partner_amount,
    cd.shop_amount,
    (cd.platform_amount + cd.entity_amount + cd.partner_amount + cd.shop_amount) as total_commission,
    ROUND((cd.platform_amount + cd.entity_amount + cd.partner_amount + cd.shop_amount) / cd.order_amount * 100, 2) as commission_percentage
FROM jy_commission_detail cd
WHERE cd.order_amount > 0
ORDER BY cd.create_time DESC
LIMIT 10;

-- 5. 检查门店设备订单统计
SELECT 
    '门店设备订单统计' as check_type,
    d.id as device_id,
    d.device_no,
    d.device_name,
    d.shop_id,
    COUNT(o.id) as total_orders,
    COUNT(CASE WHEN o.pay_status = 1 THEN 1 END) as paid_orders,
    SUM(CASE WHEN o.pay_status = 1 THEN o.amount ELSE 0 END) as total_order_amount,
    SUM(CASE WHEN o.pay_status = 1 THEN COALESCE(cd.shop_amount, 0) ELSE 0 END) as total_shop_commission
FROM jy_device d
LEFT JOIN jy_order o ON d.id = o.device_id
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id
WHERE d.shop_id = 1  -- 替换为实际门店ID
GROUP BY d.id, d.device_no, d.device_name, d.shop_id
ORDER BY total_orders DESC
LIMIT 10;

-- 6. 检查财务账户余额与分成明细的一致性
SELECT 
    '财务账户一致性检查' as check_type,
    fa.account_type,
    fa.account_id,
    fa.available_balance as account_balance,
    COALESCE(commission_sum.total_commission, 0) as calculated_commission,
    (fa.available_balance - COALESCE(commission_sum.total_commission, 0)) as difference
FROM jy_finance_account fa
LEFT JOIN (
    SELECT 
        'shop' as account_type,
        shop_id as account_id,
        SUM(shop_amount) as total_commission
    FROM jy_commission_detail
    GROUP BY shop_id
    
    UNION ALL
    
    SELECT 
        'partner' as account_type,
        partner_id as account_id,
        SUM(partner_amount) as total_commission
    FROM jy_commission_detail
    GROUP BY partner_id
    
    UNION ALL
    
    SELECT 
        'entity' as account_type,
        entity_id as account_id,
        SUM(entity_amount) as total_commission
    FROM jy_commission_detail
    GROUP BY entity_id
) commission_sum ON fa.account_type = commission_sum.account_type 
                 AND fa.account_id = commission_sum.account_id
WHERE fa.account_type IN ('shop', 'partner', 'entity')
ORDER BY fa.account_type, fa.account_id;

-- 7. 生成门店收益报表示例
SELECT 
    '门店收益报表' as report_type,
    s.id as shop_id,
    s.shop_name,
    COUNT(DISTINCT o.id) as total_orders,
    COUNT(DISTINCT CASE WHEN o.pay_status = 1 THEN o.id END) as paid_orders,
    SUM(CASE WHEN o.pay_status = 1 THEN o.amount ELSE 0 END) as total_order_amount,
    SUM(CASE WHEN o.pay_status = 1 THEN COALESCE(cd.shop_amount, 0) ELSE 0 END) as total_shop_commission,
    ROUND(
        SUM(CASE WHEN o.pay_status = 1 THEN COALESCE(cd.shop_amount, 0) ELSE 0 END) / 
        NULLIF(SUM(CASE WHEN o.pay_status = 1 THEN o.amount ELSE 0 END), 0) * 100, 
        2
    ) as commission_rate_percentage
FROM jy_shop s
LEFT JOIN jy_order o ON s.id = o.shop_id
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id
WHERE s.status = 1
GROUP BY s.id, s.shop_name
ORDER BY total_shop_commission DESC;

-- 8. 检查最近的订单分成处理情况
SELECT 
    '最近订单分成处理' as check_type,
    o.id as order_id,
    o.order_no,
    o.amount as order_amount,
    o.pay_time,
    o.commission_status,
    o.commission_time,
    cd.shop_amount,
    cd.create_time as commission_create_time,
    TIMESTAMPDIFF(MINUTE, o.pay_time, cd.create_time) as processing_delay_minutes
FROM jy_order o
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id
WHERE o.pay_status = 1
  AND o.pay_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY o.pay_time DESC
LIMIT 20;
