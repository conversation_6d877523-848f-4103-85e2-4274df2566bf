# 今夜城堡系统 - 管理员API文档

## 1. 概述

本文档描述了今夜城堡系统中系统管理员和业务主体管理员可用的API接口。系统采用RESTful风格设计，返回JSON格式数据。

## 2. 接口基础信息

- **认证方式**: 使用Sa-Token进行身份验证
- **返回格式**: 所有接口返回统一的`Result<T>`格式
  ```json
  {
    "code": 200,      // 状态码，200表示成功
    "message": "操作成功", // 消息
    "data": {}        // 返回数据，根据接口不同而不同
  }
  ```

## 3. 系统管理员API

系统管理员拥有全局视图和最高权限，可管理所有业务主体及其下属资源。

### 3.1 管理员账户管理

#### 3.1.1 获取当前登录管理员信息

- **URL**: `/admin/info`
- **方法**: GET
- **描述**: 获取当前登录管理员的详细信息
- **权限要求**: 已登录管理员
- **返回数据**: 管理员信息对象

#### 3.1.2 管理员登出

- **URL**: `/admin/logout`
- **方法**: POST
- **描述**: 管理员退出登录
- **权限要求**: 已登录管理员
- **返回数据**: 无

#### 3.1.3 分页查询管理员列表

- **URL**: `/admin/list`
- **方法**: GET
- **描述**: 分页查询管理员列表
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `page`: 页码，默认1（可选，URL参数）
  - `size`: 每页数量，默认10（可选，URL参数）
  - `username`: 用户名（可选，URL参数）
  - `realName`: 真实姓名（可选，URL参数）
  - `mobile`: 手机号（可选，URL参数）
  - `adminType`: 管理员类型（可选，URL参数）
  - `status`: 状态（可选，URL参数）
- **返回数据**: 管理员列表分页对象

#### 3.1.4 获取管理员详情

- **URL**: `/admin/{id}`
- **方法**: GET
- **描述**: 根据ID获取管理员详情
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 管理员ID（必填，路径参数）
- **返回数据**: 管理员详情对象

#### 3.1.5 创建管理员

- **URL**: `/admin`
- **方法**: POST
- **描述**: 创建新管理员
- **权限要求**: 系统管理员角色
- **请求参数**: 管理员信息对象
- **返回数据**: 创建的管理员对象

#### 3.1.6 更新管理员

- **URL**: `/admin/{id}`
- **方法**: PUT
- **描述**: 更新管理员信息
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 管理员ID（必填，路径参数）
  - 管理员信息对象（必填，请求体）
- **返回数据**: 更新后的管理员对象

#### 3.1.7 删除管理员

- **URL**: `/admin/{id}`
- **方法**: DELETE
- **描述**: 根据ID删除管理员
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 管理员ID（必填，路径参数）
- **返回数据**: 操作结果

#### 3.1.8 更新管理员状态

- **URL**: `/admin/{id}/status`
- **方法**: PUT
- **描述**: 启用或禁用管理员
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 管理员ID（必填，路径参数）
  - `status`: 状态值（必填，URL参数）
- **返回数据**: 操作结果

#### 3.1.9 重置密码

- **URL**: `/admin/{id}/password`
- **方法**: PUT
- **描述**: 重置管理员密码
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 管理员ID（必填，路径参数）
  - `newPassword`: 新密码（必填，URL参数）
- **返回数据**: 操作结果

#### 3.1.10 修改密码

- **URL**: `/admin/password`
- **方法**: PUT
- **描述**: 修改当前登录管理员的密码
- **权限要求**: 已登录管理员
- **请求参数**:
  - `oldPassword`: 旧密码（必填，URL参数）
  - `newPassword`: 新密码（必填，URL参数）
- **返回数据**: 操作结果

#### 3.1.11 更新管理员个人资料

- **URL**: `/admin/profile`
- **方法**: PUT
- **描述**: 更新管理员个人资料
- **权限要求**: 已登录管理员
- **请求参数**: 个人资料对象
- **返回数据**: 操作结果

### 3.2 业务主体管理

#### 3.2.1 分页查询业务主体列表

- **URL**: `/api/entity/page`
- **方法**: GET
- **描述**: 分页查询业务主体列表
- **权限要求**: 系统管理员角色、entity:list权限
- **请求参数**:
  - `current`: 页码，默认1（可选，URL参数）
  - `size`: 每页记录数，默认10（可选，URL参数）
  - `name`: 业务主体名称（可选，URL参数）
  - `status`: 状态：1-启用 0-禁用（可选，URL参数）
  - `parentId`: 父级ID（可选，URL参数）
- **返回数据**: 业务主体列表分页对象

#### 3.2.2 获取业务主体详情

- **URL**: `/api/entity/{id}`
- **方法**: GET
- **描述**: 获取业务主体详情
- **权限要求**: 系统管理员角色、entity:read权限
- **请求参数**:
  - `id`: 业务主体ID（必填，路径参数）
- **返回数据**: 业务主体详情对象

#### 3.2.3 添加业务主体

- **URL**: `/api/entity`
- **方法**: POST
- **描述**: 添加业务主体
- **权限要求**: 系统管理员角色、entity:add权限
- **请求参数**: 业务主体对象
- **返回数据**: 操作结果

#### 3.2.4 修改业务主体

- **URL**: `/api/entity/{id}`
- **方法**: PUT
- **描述**: 修改业务主体
- **权限要求**: 系统管理员角色、entity:update权限
- **请求参数**:
  - `id`: 业务主体ID（必填，路径参数）
  - 业务主体对象（必填，请求体）
- **返回数据**: 操作结果

#### 3.2.5 删除业务主体

- **URL**: `/api/entity/{id}`
- **方法**: DELETE
- **描述**: 删除业务主体
- **权限要求**: 系统管理员角色、entity:delete权限
- **请求参数**:
  - `id`: 业务主体ID（必填，路径参数）
- **返回数据**: 操作结果

#### 3.2.6 更新业务主体状态

- **URL**: `/api/entity/{id}/status/{status}`
- **方法**: PUT
- **描述**: 更新业务主体状态
- **权限要求**: 系统管理员角色、entity:update权限
- **请求参数**:
  - `id`: 业务主体ID（必填，路径参数）
  - `status`: 状态值（必填，路径参数）
- **返回数据**: 操作结果

#### 3.2.7 获取业务主体树

- **URL**: `/api/entity/tree`
- **方法**: GET
- **描述**: 获取业务主体树
- **权限要求**: 系统管理员角色、entity:list权限
- **请求参数**: 无
- **返回数据**: 业务主体树形结构

#### 3.2.8 获取业务主体统计数据

- **URL**: `/api/entity/{id}/statistics`
- **方法**: GET
- **描述**: 获取业务主体统计数据
- **权限要求**: 系统管理员角色、entity:read权限
- **请求参数**:
  - `id`: 业务主体ID（必填，路径参数）
- **返回数据**: 业务主体统计数据

### 3.3 设备管理

#### 3.3.1 设备费用管理

##### 3.3.1.1 分页查询设备费用列表

- **URL**: `/api/admin/device/fee/page`
- **方法**: GET
- **描述**: 分页查询设备费用列表
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `page`: 页码，默认1（可选，URL参数）
  - `size`: 每页数量，默认10（可选，URL参数）
  - `feeName`: 费用名称（可选，URL参数）
  - `feeType`: 费用类型（可选，URL参数）
  - `entityId`: 业务主体ID（可选，URL参数）
  - `partnerId`: 合作商ID（可选，URL参数）
  - `shopId`: 门店ID（可选，URL参数）
  - `status`: 状态（可选，URL参数）
- **返回数据**: 设备费用列表分页对象

##### 3.3.1.2 获取设备费用详情

- **URL**: `/api/admin/device/fee/{id}`
- **方法**: GET
- **描述**: 获取设备费用详情
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 设备费用ID（必填，路径参数）
- **返回数据**: 设备费用详情对象

##### 3.3.1.3 添加设备费用

- **URL**: `/api/admin/device/fee`
- **方法**: POST
- **描述**: 添加设备费用
- **权限要求**: 系统管理员角色
- **请求参数**: 设备费用对象
- **返回数据**: 操作结果

##### 3.3.1.4 修改设备费用

- **URL**: `/api/admin/device/fee/{id}`
- **方法**: PUT
- **描述**: 修改设备费用
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 设备费用ID（必填，路径参数）
  - 设备费用对象（必填，请求体）
- **返回数据**: 操作结果

##### 3.3.1.5 删除设备费用

- **URL**: `/api/admin/device/fee/{id}`
- **方法**: DELETE
- **描述**: 删除设备费用
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 设备费用ID（必填，路径参数）
- **返回数据**: 操作结果

##### 3.3.1.6 更新设备费用状态

- **URL**: `/api/admin/device/fee/{id}/status/{status}`
- **方法**: PUT
- **描述**: 更新设备费用状态
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 设备费用ID（必填，路径参数）
  - `status`: 状态值（必填，路径参数）
- **返回数据**: 操作结果

##### 3.3.1.7 设置默认设备费用

- **URL**: `/api/admin/device/fee/{id}/default`
- **方法**: PUT
- **描述**: 设置默认设备费用
- **权限要求**: 系统管理员角色
- **请求参数**:
  - `id`: 设备费用ID（必填，路径参数）
- **返回数据**: 操作结果

## 4. 业务主体管理员API

业务主体管理员只能管理自己区域内的合作商和门店，无法管理同级或上级账户。

### 4.1 合作商管理

#### 4.1.1 分页查询当前业务主体下的合作商列表

- **URL**: `/api/entity/partner/page`
- **方法**: GET
- **描述**: 分页查询当前业务主体下的合作商列表
- **权限要求**: 业务主体管理员角色、entity:partner:list权限
- **请求参数**:
  - `current`: 页码，默认1（可选，URL参数）
  - `size`: 每页记录数，默认10（可选，URL参数）
  - `name`: 合作商名称（可选，URL参数）
  - `status`: 状态：1-启用 0-禁用（可选，URL参数）
- **返回数据**: 合作商列表分页对象

#### 4.1.2 获取合作商详情

- **URL**: `/api/entity/partner/{id}`
- **方法**: GET
- **描述**: 获取合作商详情
- **权限要求**: 业务主体管理员角色、entity:partner:read权限
- **请求参数**:
  - `id`: 合作商ID（必填，路径参数）
- **返回数据**: 合作商详情对象

#### 4.1.3 添加合作商

- **URL**: `/api/entity/partner`
- **方法**: POST
- **描述**: 添加合作商
- **权限要求**: 业务主体管理员角色、entity:partner:add权限
- **请求参数**: 合作商对象
- **返回数据**: 操作结果

#### 4.1.4 修改合作商

- **URL**: `/api/entity/partner/{id}`
- **方法**: PUT
- **描述**: 修改合作商
- **权限要求**: 业务主体管理员角色、entity:partner:update权限
- **请求参数**:
  - `id`: 合作商ID（必填，路径参数）
  - 合作商对象（必填，请求体）
- **返回数据**: 操作结果

#### 4.1.5 删除合作商

- **URL**: `/api/entity/partner/{id}`
- **方法**: DELETE
- **描述**: 删除合作商
- **权限要求**: 业务主体管理员角色、entity:partner:delete权限
- **请求参数**:
  - `id`: 合作商ID（必填，路径参数）
- **返回数据**: 操作结果

#### 4.1.6 更新合作商状态

- **URL**: `/api/entity/partner/{id}/status/{status}`
- **方法**: PUT
- **描述**: 更新合作商状态
- **权限要求**: 业务主体管理员角色、entity:partner:update权限
- **请求参数**:
  - `id`: 合作商ID（必填，路径参数）
  - `status`: 状态值（必填，路径参数）
- **返回数据**: 操作结果

#### 4.1.7 更新合作商分成比例

- **URL**: `/api/entity/partner/{id}/revenue-ratio`
- **方法**: PUT
- **描述**: 更新合作商分成比例
- **权限要求**: 业务主体管理员角色、entity:partner:update权限
- **请求参数**:
  - `id`: 合作商ID（必填，路径参数）
  - `revenueRatio`: 分成比例（必填，URL参数）
- **返回数据**: 操作结果

#### 4.1.8 获取合作商统计数据

- **URL**: `/api/entity/partner/statistics`
- **方法**: GET
- **描述**: 获取合作商统计数据
- **权限要求**: 业务主体管理员角色、entity:partner:read权限
- **请求参数**: 无
- **返回数据**: 合作商统计数据

#### 4.1.9 获取所有启用的合作商

- **URL**: `/api/entity/partner/list`
- **方法**: GET
- **描述**: 获取所有启用的合作商（下拉选择用）
- **权限要求**: 业务主体管理员角色、entity:partner:list权限
- **请求参数**: 无
- **返回数据**: 合作商列表

### 4.2 门店管理

业务主体管理员可以通过合作商管理接口查看和管理下属门店。

### 4.3 设备管理

业务主体管理员可以查看和管理区域内的设备，包括设备状态监控、设备分配情况查看等。

### 4.4 财务管理

业务主体管理员可以查看账户余额、收入明细、分成情况等财务数据。

### 4.5 订单管理

业务主体管理员可以查询区域内订单，按合作商/门店筛选订单，查看订单详情等。

## 5. 审计日志

系统记录了所有关键操作的审计日志，包括用户登录、数据增删改查等操作。审计日志包含以下信息：

- 操作用户ID和名称
- 用户角色
- 操作模块
- 操作类型
- 操作对象类型和ID
- 操作描述
- HTTP请求方法和URL
- 请求参数和响应数据
- 执行时长
- 操作IP和用户代理
- 操作状态和错误信息
- 业务主体ID、合作商ID、门店ID
- 操作时间

## 6. 权限控制

系统实现了严格的权限控制机制：

1. 系统管理员拥有全局视图和最高权限，可管理所有业务主体
2. 业务主体管理员只能管理自己区域内的合作商和门店
3. 系统管理员可以设置系统级参数和分成比例，业务主体管理员只能查看自身分成比例（不能修改）
4. 系统管理员负责审核提现申请和执行打款，业务主体管理员无此权限 