// 连接Redis并清理设备缓存的命令
// 复制以下命令到Redis客户端执行

redis-cli -h ************** -p 6379 -a redis_ZWE7i5

// 然后执行以下命令清理缓存：

DEL business:device:*
DEL business:device:detail:*
DEL business:device:mac:*
DEL business:device:no:*
DEL business:device:bind:*
DEL business:shop:devices:*
DEL device::*
DEL deviceByNo::*

// 或者使用通配符删除：
EVAL "
local patterns = {'business:device:*', 'business:shop:devices:*', 'device::*', 'deviceByNo::*'}
local total = 0
for i, pattern in ipairs(patterns) do
    local keys = redis.call('KEYS', pattern)
    if #keys > 0 then
        total = total + redis.call('DEL', unpack(keys))
    end
end
return total
" 0

// 检查剩余缓存：
KEYS business:device:*
KEYS device::*

// 退出Redis客户端：
QUIT
