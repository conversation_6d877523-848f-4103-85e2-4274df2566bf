#!/bin/bash

# ========================================
# 今夜城堡项目安装脚本 (Ubuntu)
# 用途: 安装依赖、配置环境、部署应用
# ========================================

set -e  # 遇到错误立即退出

# 配置变量
APP_NAME="jycb-z"
APP_USER="jycb"
APP_GROUP="jycb"
APP_HOME="/opt/jycb-z"
LOG_DIR="/var/log/jycb-z"
JAVA_VERSION="17"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# 检查是否以root权限运行
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 更新系统包
update_system() {
    log_info "更新系统包..."
    apt update
    apt upgrade -y
}

# 安装Java
install_java() {
    log_info "检查Java环境..."
    
    if command -v java &> /dev/null; then
        CURRENT_JAVA_VERSION=$(java -version 2>&1 | head -n1 | awk -F '"' '{print $2}' | awk -F '.' '{print $1}')
        if [[ $CURRENT_JAVA_VERSION -ge $JAVA_VERSION ]]; then
            log_info "Java已安装，版本: $(java -version 2>&1 | head -n1)"
            return 0
        fi
    fi
    
    log_info "安装Java $JAVA_VERSION..."
    apt install -y openjdk-${JAVA_VERSION}-jdk
    
    # 设置JAVA_HOME
    echo "export JAVA_HOME=/usr/lib/jvm/java-${JAVA_VERSION}-openjdk-amd64" >> /etc/environment
    source /etc/environment
    
    log_info "Java安装完成: $(java -version 2>&1 | head -n1)"
}

# 安装MySQL
install_mysql() {
    log_info "检查MySQL..."
    
    if systemctl is-active --quiet mysql; then
        log_info "MySQL已安装并运行"
        return 0
    fi
    
    if command -v mysql &> /dev/null; then
        log_info "MySQL已安装，启动服务..."
        systemctl start mysql
        systemctl enable mysql
        return 0
    fi
    
    log_info "安装MySQL..."
    apt install -y mysql-server
    
    # 启动并启用MySQL服务
    systemctl start mysql
    systemctl enable mysql
    
    log_info "MySQL安装完成"
    log_warn "请手动运行 'sudo mysql_secure_installation' 来配置MySQL安全设置"
}

# 安装Redis
install_redis() {
    log_info "检查Redis..."
    
    if systemctl is-active --quiet redis-server; then
        log_info "Redis已安装并运行"
        return 0
    fi
    
    if command -v redis-server &> /dev/null; then
        log_info "Redis已安装，启动服务..."
        systemctl start redis-server
        systemctl enable redis-server
        return 0
    fi
    
    log_info "安装Redis..."
    apt install -y redis-server
    
    # 启动并启用Redis服务
    systemctl start redis-server
    systemctl enable redis-server
    
    log_info "Redis安装完成"
}

# 安装其他依赖
install_dependencies() {
    log_info "安装其他依赖..."
    apt install -y curl wget unzip net-tools htop
}

# 创建应用用户
create_app_user() {
    log_info "创建应用用户..."
    
    if id "$APP_USER" &>/dev/null; then
        log_info "用户 $APP_USER 已存在"
    else
        useradd -r -m -s /bin/bash -d "$APP_HOME" "$APP_USER"
        log_info "创建用户: $APP_USER"
    fi
    
    # 创建应用目录
    mkdir -p "$APP_HOME"
    mkdir -p "$LOG_DIR"
    
    # 设置目录权限
    chown -R "$APP_USER:$APP_GROUP" "$APP_HOME"
    chown -R "$APP_USER:$APP_GROUP" "$LOG_DIR"
    
    log_info "应用目录创建完成: $APP_HOME"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # 允许SSH
        ufw allow ssh
        
        # 允许应用端口
        ufw allow 8080/tcp
        
        # 启用防火墙
        ufw --force enable
        
        log_info "防火墙配置完成"
        ufw status
    else
        log_warn "UFW防火墙未安装，跳过防火墙配置"
    fi
}

# 部署应用文件
deploy_app() {
    log_info "部署应用文件..."
    
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    # 复制启动脚本
    if [[ -f "$SCRIPT_DIR/start-jycb-z.sh" ]]; then
        cp "$SCRIPT_DIR/start-jycb-z.sh" "$APP_HOME/"
        chmod +x "$APP_HOME/start-jycb-z.sh"
        chown "$APP_USER:$APP_GROUP" "$APP_HOME/start-jycb-z.sh"
        log_info "启动脚本部署完成"
    else
        log_warn "启动脚本不存在: $SCRIPT_DIR/start-jycb-z.sh"
    fi
    
    # 查找并复制JAR文件
    JAR_FILE=$(find "$SCRIPT_DIR" -name "jycb-z*.jar" -type f | head -n1)
    if [[ -n "$JAR_FILE" && -f "$JAR_FILE" ]]; then
        cp "$JAR_FILE" "$APP_HOME/"
        chown "$APP_USER:$APP_GROUP" "$APP_HOME/$(basename "$JAR_FILE")"
        log_info "JAR文件部署完成: $(basename "$JAR_FILE")"
    else
        log_warn "未找到JAR文件，请手动复制到 $APP_HOME/"
    fi
    
    # 复制配置文件
    if [[ -f "$SCRIPT_DIR/application.yml" ]]; then
        cp "$SCRIPT_DIR/application.yml" "$APP_HOME/"
        chown "$APP_USER:$APP_GROUP" "$APP_HOME/application.yml"
        log_info "配置文件部署完成"
    fi
}

# 安装systemd服务
install_systemd_service() {
    log_info "安装systemd服务..."
    
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    
    if [[ -f "$SCRIPT_DIR/jycb-z.service" ]]; then
        cp "$SCRIPT_DIR/jycb-z.service" "/etc/systemd/system/"
        
        # 重新加载systemd
        systemctl daemon-reload
        
        # 启用服务
        systemctl enable jycb-z.service
        
        log_info "systemd服务安装完成"
        log_info "使用以下命令管理服务:"
        log_info "  启动: sudo systemctl start jycb-z"
        log_info "  停止: sudo systemctl stop jycb-z"
        log_info "  重启: sudo systemctl restart jycb-z"
        log_info "  状态: sudo systemctl status jycb-z"
        log_info "  日志: sudo journalctl -u jycb-z -f"
    else
        log_warn "systemd服务文件不存在: $SCRIPT_DIR/jycb-z.service"
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库..."
    
    # 检查MySQL是否运行
    if ! systemctl is-active --quiet mysql; then
        log_error "MySQL服务未运行，请先启动MySQL"
        return 1
    fi
    
    # 创建数据库（如果不存在）
    mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS jycb_z CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || {
        log_warn "无法创建数据库，请手动创建数据库 'jycb_z'"
        log_info "SQL命令: CREATE DATABASE IF NOT EXISTS jycb_z CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    }
}

# 显示安装完成信息
show_completion_info() {
    log_info "安装完成！"
    echo ""
    echo "=========================================="
    echo "  今夜城堡项目安装完成"
    echo "=========================================="
    echo ""
    echo "应用信息:"
    echo "  应用目录: $APP_HOME"
    echo "  日志目录: $LOG_DIR"
    echo "  应用用户: $APP_USER"
    echo "  服务名称: jycb-z"
    echo ""
    echo "管理命令:"
    echo "  启动应用: sudo systemctl start jycb-z"
    echo "  停止应用: sudo systemctl stop jycb-z"
    echo "  重启应用: sudo systemctl restart jycb-z"
    echo "  查看状态: sudo systemctl status jycb-z"
    echo "  查看日志: sudo journalctl -u jycb-z -f"
    echo ""
    echo "或使用启动脚本:"
    echo "  cd $APP_HOME"
    echo "  sudo -u $APP_USER ./start-jycb-z.sh start"
    echo ""
    echo "访问地址: http://localhost:8080"
    echo ""
    echo "注意事项:"
    echo "1. 请确保MySQL和Redis服务正常运行"
    echo "2. 请根据需要修改配置文件: $APP_HOME/application.yml"
    echo "3. 首次启动前请导入数据库结构"
    echo "4. 建议运行 'sudo mysql_secure_installation' 配置MySQL安全设置"
    echo ""
}

# 主函数
main() {
    log_info "开始安装今夜城堡项目..."
    
    check_root
    update_system
    install_java
    install_mysql
    install_redis
    install_dependencies
    create_app_user
    deploy_app
    install_systemd_service
    configure_firewall
    create_database
    show_completion_info
}

# 执行主函数
main "$@"
