@echo off
echo 正在检查编译错误...

echo.
echo 检查关键文件是否存在...
if not exist "src\main\java\com\jycb\jycbz\modules\shop\controller\ShopDeviceController.java" (
    echo ERROR: ShopDeviceController.java 不存在
    exit /b 1
)

if not exist "src\main\java\com\jycb\jycbz\modules\finance\controller\partner\PartnerFinanceController.java" (
    echo ERROR: PartnerFinanceController.java 不存在
    exit /b 1
)

if not exist "src\main\java\com\jycb\jycbz\common\controller\BaseFinanceController.java" (
    echo ERROR: BaseFinanceController.java 不存在
    exit /b 1
)

echo 所有关键文件都存在
echo.
echo 编译检查完成
