package com.jycb.jycbz.common.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分布式锁服务
 * 用于解决并发处理同一订单分成的问题
 */
@Slf4j
@Service
public class DistributedLockService {
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    // 存储当前线程持有的锁信息
    private static final ThreadLocal<ConcurrentHashMap<String, String>> THREAD_LOCKS = 
        ThreadLocal.withInitial(ConcurrentHashMap::new);
    
    private static final String COMMISSION_LOCK_PREFIX = "order_commission_lock:";
    private static final String DEVICE_OPERATION_LOCK_PREFIX = "device_operation_lock:";
    private static final int DEFAULT_LOCK_EXPIRE_TIME = 30; // 30秒
    
    // Lua脚本：原子性释放锁
    private static final String RELEASE_LOCK_SCRIPT = 
        "if redis.call('get', KEYS[1]) == ARGV[1] then " +
        "    return redis.call('del', KEYS[1]) " +
        "else " +
        "    return 0 " +
        "end";
    
    /**
     * 获取订单分成处理锁
     * 
     * @param orderNo 订单号
     * @return 是否成功获取锁
     */
    public boolean acquireCommissionLock(String orderNo) {
        return acquireLock(COMMISSION_LOCK_PREFIX + orderNo, DEFAULT_LOCK_EXPIRE_TIME);
    }
    
    /**
     * 释放订单分成处理锁
     * 
     * @param orderNo 订单号
     */
    public void releaseCommissionLock(String orderNo) {
        releaseLock(COMMISSION_LOCK_PREFIX + orderNo);
    }
    
    /**
     * 获取设备操作锁
     * 
     * @param deviceId 设备ID
     * @return 是否成功获取锁
     */
    public boolean acquireDeviceOperationLock(Integer deviceId) {
        return acquireLock(DEVICE_OPERATION_LOCK_PREFIX + deviceId, DEFAULT_LOCK_EXPIRE_TIME);
    }
    
    /**
     * 释放设备操作锁
     * 
     * @param deviceId 设备ID
     */
    public void releaseDeviceOperationLock(Integer deviceId) {
        releaseLock(DEVICE_OPERATION_LOCK_PREFIX + deviceId);
    }
    
    /**
     * 通用获取锁方法
     * 
     * @param lockKey 锁的键
     * @param expireSeconds 过期时间（秒）
     * @return 是否成功获取锁
     */
    private boolean acquireLock(String lockKey, int expireSeconds) {
        String lockValue = UUID.randomUUID().toString();
        
        try {
            Boolean result = redisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, Duration.ofSeconds(expireSeconds));
            
            if (Boolean.TRUE.equals(result)) {
                // 记录当前线程持有的锁
                THREAD_LOCKS.get().put(lockKey, lockValue);
                log.debug("成功获取分布式锁: {}", lockKey);
                return true;
            } else {
                log.debug("获取分布式锁失败，锁已被占用: {}", lockKey);
                return false;
            }
        } catch (Exception e) {
            log.error("获取分布式锁异常: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 通用释放锁方法
     * 
     * @param lockKey 锁的键
     */
    private void releaseLock(String lockKey) {
        try {
            String lockValue = THREAD_LOCKS.get().get(lockKey);
            if (lockValue == null) {
                log.warn("尝试释放未持有的锁: {}", lockKey);
                return;
            }
            
            // 使用Lua脚本原子性释放锁
            Long result = redisTemplate.execute(
                new DefaultRedisScript<>(RELEASE_LOCK_SCRIPT, Long.class),
                Collections.singletonList(lockKey),
                lockValue
            );
            
            if (result != null && result == 1) {
                log.debug("成功释放分布式锁: {}", lockKey);
            } else {
                log.warn("释放分布式锁失败，锁可能已过期或被其他线程持有: {}", lockKey);
            }
            
            // 从线程本地存储中移除
            THREAD_LOCKS.get().remove(lockKey);
            
        } catch (Exception e) {
            log.error("释放分布式锁异常: {}", lockKey, e);
        }
    }
    
    /**
     * 释放当前线程持有的所有锁
     * 建议在finally块中调用
     */
    public void releaseAllLocksForCurrentThread() {
        try {
            ConcurrentHashMap<String, String> threadLocks = THREAD_LOCKS.get();
            for (String lockKey : threadLocks.keySet()) {
                releaseLock(lockKey);
            }
            threadLocks.clear();
        } catch (Exception e) {
            log.error("释放当前线程所有锁异常", e);
        }
    }
    
    /**
     * 尝试获取锁，带重试机制
     * 
     * @param lockKey 锁的键
     * @param expireSeconds 过期时间（秒）
     * @param retryTimes 重试次数
     * @param retryIntervalMs 重试间隔（毫秒）
     * @return 是否成功获取锁
     */
    public boolean acquireLockWithRetry(String lockKey, int expireSeconds, 
                                       int retryTimes, long retryIntervalMs) {
        for (int i = 0; i <= retryTimes; i++) {
            if (acquireLock(lockKey, expireSeconds)) {
                return true;
            }
            
            if (i < retryTimes) {
                try {
                    Thread.sleep(retryIntervalMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("获取锁重试被中断: {}", lockKey);
                    return false;
                }
            }
        }
        
        log.warn("获取锁失败，已重试{}次: {}", retryTimes, lockKey);
        return false;
    }
    
    /**
     * 检查锁是否存在
     * 
     * @param lockKey 锁的键
     * @return 锁是否存在
     */
    public boolean isLockExists(String lockKey) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(lockKey));
        } catch (Exception e) {
            log.error("检查锁是否存在异常: {}", lockKey, e);
            return false;
        }
    }
    
    /**
     * 延长锁的过期时间
     * 
     * @param lockKey 锁的键
     * @param expireSeconds 新的过期时间（秒）
     * @return 是否成功延长
     */
    public boolean extendLockExpire(String lockKey, int expireSeconds) {
        try {
            String lockValue = THREAD_LOCKS.get().get(lockKey);
            if (lockValue == null) {
                log.warn("尝试延长未持有的锁: {}", lockKey);
                return false;
            }
            
            // Lua脚本：检查锁的持有者并延长过期时间
            String extendScript = 
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                "    return redis.call('expire', KEYS[1], ARGV[2]) " +
                "else " +
                "    return 0 " +
                "end";
            
            Long result = redisTemplate.execute(
                new DefaultRedisScript<>(extendScript, Long.class),
                Collections.singletonList(lockKey),
                lockValue,
                String.valueOf(expireSeconds)
            );
            
            return result != null && result == 1;
            
        } catch (Exception e) {
            log.error("延长锁过期时间异常: {}", lockKey, e);
            return false;
        }
    }
}
