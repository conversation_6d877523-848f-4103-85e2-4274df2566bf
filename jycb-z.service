[Unit]
Description=今夜城堡后端服务 (JYCB-Z)
Documentation=https://github.com/your-org/jycb-z
After=network.target mysql.service redis-server.service
Wants=mysql.service redis-server.service

[Service]
Type=forking
User=jycb
Group=jycb
WorkingDirectory=/opt/jycb-z
ExecStart=/opt/jycb-z/start-jycb-z.sh start
ExecStop=/opt/jycb-z/start-jycb-z.sh stop
ExecReload=/opt/jycb-z/start-jycb-z.sh restart
PIDFile=/var/run/jycb-z.pid
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=jycb-z

# 环境变量
Environment=JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
Environment=SPRING_PROFILES_ACTIVE=prod
Environment=SERVER_PORT=8080

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/log/jycb-z /var/run /opt/jycb-z/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
