{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__7EFDBD2", "name": "门店管理", "version": {"name": "0.1", "code": 1}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "light", "background": "#121212"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "safearea": {"background": "#121212", "bottom": {"offset": "auto"}}, "distribute": {"icons": {"android": {"hdpi": "unpackage/res/icons/72x72.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}}}, "google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "apple": {"dSYMs": false}, "plugins": {"audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "uni-app": {"compilerVersion": "4.66", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"custom": true, "color": "rgba(255, 255, 255, 0.5)", "selectedColor": "#A875FF", "backgroundColor": "rgba(30, 30, 30, 0.95)", "borderStyle": "rgba(0,0,0,0.4)", "list": [{"pagePath": "pages/dashboard/dashboard", "text": "首页", "iconPath": "static/tabbar/dashboard.png", "selectedIconPath": "static/tabbar/dashboard-active.png"}, {"pagePath": "pages/devices/devices", "text": "设备", "iconPath": "static/tabbar/devices.png", "selectedIconPath": "static/tabbar/devices-active.png"}, {"pagePath": "pages/orders/orders", "text": "订单", "iconPath": "static/tabbar/orders.png", "selectedIconPath": "static/tabbar/orders-active.png"}, {"pagePath": "pages/income/income", "text": "收益", "iconPath": "static/tabbar/income.png", "selectedIconPath": "static/tabbar/income-active.png"}, {"pagePath": "pages/profile/profile", "text": "我的", "iconPath": "static/tabbar/profile.png", "selectedIconPath": "static/tabbar/profile-active.png"}], "height": "50px"}, "launch_path": "__uniappview.html"}}