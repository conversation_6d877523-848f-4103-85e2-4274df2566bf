/* 全局变量 */
:root {
  /* 主色调 */
  --primary: #9527C6;
  --primary-dark: #6E3AD9;
  --primary-light: #A875FF;
  --primary-gradient: linear-gradient(135deg, #A875FF, #9527C6, #6E3AD9);
  
  /* 霓虹色调 */
  --neon-pink: #FF2E93;
  --neon-blue: #00E9FF;
  --neon-green: #00FF85;
  --neon-yellow: #FFDE59;
  
  /* 暗色背景 */
  --bg-dark: #121212;
  --bg-card: #1E1E1E;
  --bg-card-hover: #252525;
  
  /* 文本颜色 */
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);
  --text-disabled: rgba(255, 255, 255, 0.3);
  
  /* 边框与阴影 */
  --border-light: rgba(255, 255, 255, 0.1);
  --border-focus: rgba(168, 117, 255, 0.5);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.25);
  --shadow-neon: 0 0 15px rgba(140, 82, 255, 0.5);
  
  /* 间距 */
  --space-xs: 8rpx;
  --space-sm: 16rpx;
  --space-md: 24rpx;
  --space-lg: 32rpx;
  --space-xl: 48rpx;
  
  /* 圆角 */
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-full: 999rpx;
}

/* 全局样式 */
page {
  background-color: var(--bg-dark);
  color: var(--text-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  box-sizing: border-box;
  min-height: 100%;
  
  /* 防止闪烁 */
  transition: none !important;
  animation: none !important;
}

/* 防止页面闪烁 */
view, text, image, button, input, textarea, scroll-view {
  transition: none !important;
  animation: none !important;
}

/* 容器样式 */
.container {
  padding: 30rpx;
  box-sizing: border-box;
  min-height: 100vh;
  background-color: var(--bg-dark);
  padding-bottom: calc(110rpx + env(safe-area-inset-bottom));
}

/* 波浪装饰 */
.wave-decoration {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 120px;
  background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%239527C6'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' fill='%239527C6'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' fill='%239527C6'%3E%3C/path%3E%3C/svg%3E") no-repeat bottom center;
  background-size: cover;
  z-index: 0;
  opacity: 0.05;
  pointer-events: none;
}

/* 文本颜色工具类 */
.primary {
  color: var(--primary);
}

.primary-light {
  color: var(--primary-light);
}

.primary-dark {
  color: var(--primary-dark);
}

.neon-pink {
  color: var(--neon-pink);
}

.neon-blue {
  color: var(--neon-blue);
}

.neon-green {
  color: var(--neon-green);
}

.neon-yellow {
  color: var(--neon-yellow);
}

/* Material Icons 尺寸类 */
.material-icons.md-18 { font-size: 18rpx; }
.material-icons.md-24 { font-size: 24rpx; }
.material-icons.md-36 { font-size: 36rpx; }
.material-icons.md-48 { font-size: 48rpx; }
.material-icons.md-64 { font-size: 64rpx; }

/* Material Icons 颜色类 */
.material-icons.md-dark { color: rgba(0, 0, 0, 0.54); }
.material-icons.md-dark.md-inactive { color: rgba(0, 0, 0, 0.26); }
.material-icons.md-light { color: rgba(255, 255, 255, 1); }
.material-icons.md-light.md-inactive { color: rgba(255, 255, 255, 0.3); }

/* 状态颜色 */
.status {
  padding: 4rpx 16rpx;
  border-radius: var(--radius-full);
  font-size: 24rpx;
  font-weight: 500;
}

.status.active {
  background-color: rgba(0, 255, 133, 0.15);
  color: var(--neon-green);
}

.status.warning {
  background-color: rgba(255, 222, 89, 0.15);
  color: var(--neon-yellow);
}

.status.inactive {
  background-color: rgba(255, 46, 147, 0.15);
  color: var(--neon-pink);
}

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 28rpx;
  border: none;
  outline: none;
  cursor: pointer;
}

.btn-primary {
  background: var(--primary-gradient);
  color: var(--text-primary);
  box-shadow: 0 8rpx 20rpx rgba(149, 39, 198, 0.3);
}

.btn-outline {
  background: transparent;
  color: var(--primary-light);
  border: 2rpx solid var(--primary-light);
}

.btn-sm {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.btn-lg {
  padding: 30rpx 60rpx;
  font-size: 32rpx;
}

/* 卡片样式 */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-lg);
  border: 1px solid var(--border-light);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  font-size: 28rpx;
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.form-input {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: 20rpx;
  color: var(--text-primary);
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 标题样式 */
.title-lg {
  font-size: 44rpx;
  font-weight: 700;
  margin-bottom: var(--space-md);
}

.title-md {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: var(--space-sm);
}

.title-sm {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: var(--space-xs);
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

.text-disabled {
  color: var(--text-disabled);
}

.text-accent {
  color: var(--primary-light);
}

.text-success {
  color: var(--neon-green);
}

.text-warning {
  color: var(--neon-yellow);
}

.text-error {
  color: var(--neon-pink);
}

/* 布局辅助 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-xs {
  gap: var(--space-xs);
}

.gap-sm {
  gap: var(--space-sm);
}

.gap-md {
  gap: var(--space-md);
}

.mt-sm {
  margin-top: var(--space-sm);
}

.mt-md {
  margin-top: var(--space-md);
}

.mt-lg {
  margin-top: var(--space-lg);
}

.mb-sm {
  margin-bottom: var(--space-sm);
}

.mb-md {
  margin-bottom: var(--space-md);
}

.mb-lg {
  margin-bottom: var(--space-lg);
}

/* 响应式网格 */
.grid {
  display: grid;
  gap: var(--space-md);
}

.grid-cols-1 {
  grid-template-columns: 1fr;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 防止页面闪烁的特殊处理 */
.no-flash {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000;
} 