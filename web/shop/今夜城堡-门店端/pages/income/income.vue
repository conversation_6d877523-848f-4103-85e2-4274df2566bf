<template>
  <view class="container">
    <!-- 头部信息 -->
    <view class="top-spacing"></view>
    <view class="page-header">
      <view class="header-content">
        <view class="page-title purple-title">财务管理</view>
        <view class="page-subtitle">查看您的收益和提现管理</view>
      </view>
      <view>
        <view class="refresh-btn" @tap="refreshData">
          <text class="material-icons" style="font-size: 28rpx; margin-right: 8rpx;">refresh</text>
          <text>刷新</text>
        </view>
      </view>
    </view>
    
    <!-- 财务统计卡片 -->
    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-title">账户余额（元）</view>
        <view class="stat-value">{{ formatAmount(balanceInfo.balance) }}</view>
        <view class="stat-change positive">
          <text class="material-icons neon-green" style="font-size: 28rpx;">account_balance_wallet</text>
          <text>可提现余额</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-title">今日收益（元）</view>
        <view class="stat-value">{{ formatAmount(balanceInfo.todayIncome) }}</view>
        <view class="stat-change" :class="balanceInfo.todayIncomeChange >= 0 ? 'positive' : 'negative'">
          <text class="material-icons" :class="balanceInfo.todayIncomeChange >= 0 ? 'neon-green' : 'neon-pink'" style="font-size: 28rpx;">{{ balanceInfo.todayIncomeChange >= 0 ? 'arrow_upward' : 'arrow_downward' }}</text>
          <text>{{ Math.abs(balanceInfo.todayIncomeChange || 0) }}% 较昨日</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-title">本月收益（元）</view>
        <view class="stat-value">{{ formatAmount(balanceInfo.monthIncome) }}</view>
        <view class="stat-change" :class="balanceInfo.monthIncomeChange >= 0 ? 'positive' : 'negative'">
          <text class="material-icons" :class="balanceInfo.monthIncomeChange >= 0 ? 'neon-green' : 'neon-pink'" style="font-size: 28rpx;">{{ balanceInfo.monthIncomeChange >= 0 ? 'arrow_upward' : 'arrow_downward' }}</text>
          <text>{{ Math.abs(balanceInfo.monthIncomeChange || 0) }}% 较上月</text>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-title">累计收益（元）</view>
        <view class="stat-value">{{ formatAmount(balanceInfo.totalIncome) }}</view>
        <view class="stat-change positive">
          <text class="material-icons neon-blue" style="font-size: 28rpx;">trending_up</text>
          <text>历史总收益</text>
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <view class="action-btn primary" @tap="showWithdrawModal">
        <text class="material-icons">monetization_on</text>
        <text>申请提现</text>
      </view>
      <view class="action-btn secondary" @tap="showTransactionHistory">
        <text class="material-icons">history</text>
        <text>交易记录</text>
      </view>
      <view class="action-btn tertiary" @tap="showBankCardManagement">
        <text class="material-icons">credit_card</text>
        <text>银行卡管理</text>
      </view>
    </view>
    
    <!-- 提现记录 -->
    <view class="section">
      <view class="section-header">
        <view class="section-title">提现记录</view>
        <view class="section-action" @tap="loadWithdrawRecords">
          <text class="material-icons">refresh</text>
        </view>
      </view>
      
      <view class="withdraw-list">
        <!-- 加载中状态 -->
        <view class="loading-state" v-if="loading && withdrawRecords.length === 0">
          <text class="material-icons rotating">sync</text>
          <text>加载中...</text>
        </view>
        
        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && withdrawRecords.length === 0">
          <text class="material-icons">receipt_long</text>
          <text>暂无提现记录</text>
        </view>
        
        <!-- 提现记录列表 -->
        <view 
          class="withdraw-item" 
          v-for="(record, index) in withdrawRecords" 
          :key="record.id || index"
        >
          <view class="withdraw-info">
            <view class="withdraw-amount">¥{{ formatAmount(record.amount) }}</view>
            <view class="withdraw-time">{{ formatDateTime(record.createTime) }}</view>
          </view>
          <view class="withdraw-status" :class="getStatusClass(record.status)">
            {{ getStatusText(record.status) }}
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部导航栏 -->
    <tab-bar current-tab="/pages/income/income"></tab-bar>
    
    <!-- 提现模态框 -->
    <view class="modal" v-if="showModal === 'withdraw'">
      <view class="modal-backdrop" @tap="hideModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">申请提现</view>
          <view class="modal-close" @tap="hideModal">
            <text class="material-icons">close</text>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="form-group">
            <view class="form-label">提现金额</view>
            <view class="form-input-container">
              <input 
                class="form-input" 
                type="digit" 
                placeholder="请输入提现金额" 
                v-model="withdrawAmount"
                @input="validateWithdrawAmount"
              />
              <text class="input-unit">元</text>
            </view>
            <view class="form-hint">可提现余额：¥{{ formatAmount(balanceInfo.balance) }}</view>
          </view>
          
          <view class="form-group">
            <view class="form-label">银行卡</view>
            <view class="bank-card-selector" @tap="showBankCardModal">
              <view class="bank-card-info" v-if="selectedBankCard">
                <view class="bank-name">{{ selectedBankCard.bankName }}</view>
                <view class="bank-account">**** **** **** {{ selectedBankCard.bankAccount.slice(-4) }}</view>
              </view>
              <view class="no-bank-card" v-else>
                <text class="material-icons">credit_card</text>
                <text>请选择银行卡</text>
              </view>
              <text class="material-icons">chevron_right</text>
            </view>
          </view>
          
          <view class="form-group">
            <view class="form-label">提现密码</view>
            <view class="form-input-container">
              <input 
                class="form-input" 
                type="password" 
                placeholder="请输入6位提现密码" 
                v-model="withdrawPassword"
                maxlength="6"
              />
            </view>
            <view class="form-hint">请输入6位数字提现密码</view>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="btn btn-secondary" @tap="hideModal">取消</view>
          <view class="btn btn-primary" @tap="submitWithdraw" :class="{ disabled: submitting }">
            <text v-if="submitting">提交中...</text>
            <text v-else>确认提现</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 银行卡选择模态框 -->
    <view class="modal" v-if="showModal === 'bankCard'">
      <view class="modal-backdrop" @tap="hideModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">选择银行卡</view>
          <view class="modal-close" @tap="hideModal">
            <text class="material-icons">close</text>
          </view>
        </view>
        
        <view class="modal-body">
          <view class="bank-card-list">
            <!-- 加载中 -->
            <view class="loading-state" v-if="loadingBankCards">
              <text class="material-icons rotating">sync</text>
              <text>加载银行卡...</text>
            </view>
            
            <!-- 空状态 -->
            <view class="empty-state" v-if="!loadingBankCards && bankCards.length === 0">
              <text class="material-icons">credit_card</text>
              <text>暂无银行卡</text>
              <view class="add-bank-card-btn" @tap="showAddBankCardModal">
                <text class="material-icons">add</text>
                <text>添加银行卡</text>
              </view>
            </view>
            
            <!-- 银行卡列表 -->
            <view 
              class="bank-card-item" 
              v-for="card in bankCards" 
              :key="card.id"
              :class="{ selected: selectedBankCard && selectedBankCard.id === card.id }"
              @tap="selectBankCard(card)"
            >
              <view class="bank-card-info">
                <view class="bank-name">{{ card.bankName }}</view>
                <view class="bank-account">**** **** **** {{ card.bankAccount.slice(-4) }}</view>
                <view class="account-name">{{ card.accountName }}</view>
              </view>
              <view class="bank-card-action">
                <text class="material-icons" v-if="selectedBankCard && selectedBankCard.id === card.id">check_circle</text>
                <text class="material-icons" v-else>radio_button_unchecked</text>
              </view>
            </view>
            
            <!-- 添加银行卡按钮 -->
            <view class="add-bank-card-btn" @tap="showAddBankCardModal" v-if="bankCards.length > 0">
              <text class="material-icons">add</text>
              <text>添加新银行卡</text>
            </view>
          </view>
        </view>
        
        <view class="modal-footer">
          <view class="btn btn-primary" @tap="confirmBankCard" :class="{ disabled: !selectedBankCard }">
            确认选择
          </view>
        </view>
      </view>
    </view>

    <!-- 银行卡管理模态框 -->
    <view class="modal" v-if="showModal === 'bankCardManagement'">
      <view class="modal-backdrop" @tap="hideModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">银行卡管理</view>
          <view class="modal-close" @tap="hideModal">
            <text class="material-icons">close</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="bank-card-management">
            <!-- 银行卡列表 -->
            <view class="bank-card-list">
              <!-- 加载中 -->
              <view class="loading-state" v-if="loadingBankCards">
                <text class="material-icons rotating">sync</text>
                <text>加载银行卡...</text>
              </view>

              <!-- 空状态 -->
              <view class="empty-state" v-if="!loadingBankCards && bankCards.length === 0">
                <text class="material-icons">credit_card</text>
                <text>暂无银行卡</text>
                <text style="font-size: 24rpx; margin-top: 8rpx;">请添加银行卡以便提现</text>
              </view>

              <!-- 银行卡列表 -->
              <view
                class="bank-card-management-item"
                v-for="card in bankCards"
                :key="card.id"
              >
                <view class="bank-card-info">
                  <view class="bank-card-header">
                    <view class="bank-name">{{ card.bankName }}</view>
                    <view class="bank-default" v-if="card.isDefault">默认</view>
                  </view>
                  <view class="bank-account">**** **** **** {{ card.bankAccount.slice(-4) }}</view>
                  <view class="account-name">{{ card.accountName }}</view>
                </view>
                <view class="bank-card-actions">
                  <view class="action-icon" @tap="setDefaultBankCard(card)" v-if="!card.isDefault">
                    <text class="material-icons">star_border</text>
                  </view>
                  <view class="action-icon" @tap="editBankCard(card)">
                    <text class="material-icons">edit</text>
                  </view>
                  <view class="action-icon danger" @tap="deleteBankCard(card)">
                    <text class="material-icons">delete</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 添加银行卡按钮 -->
            <view class="add-bank-card-btn" @tap="showAddBankCardModal">
              <text class="material-icons">add</text>
              <text>添加新银行卡</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加银行卡模态框 -->
    <view class="modal" v-if="showModal === 'addBankCard'">
      <view class="modal-backdrop" @tap="hideModal"></view>
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title">添加银行卡</view>
          <view class="modal-close" @tap="hideModal">
            <text class="material-icons">close</text>
          </view>
        </view>

        <view class="modal-body">
          <view class="form-group">
            <view class="form-label">银行名称</view>
            <view class="form-input-container">
              <input
                class="form-input"
                type="text"
                placeholder="请输入银行名称"
                v-model="newBankCard.bankName"
              />
            </view>
          </view>

          <view class="form-group">
            <view class="form-label">银行卡号</view>
            <view class="form-input-container">
              <input
                class="form-input"
                type="text"
                placeholder="请输入银行卡号"
                v-model="newBankCard.bankAccount"
              />
            </view>
          </view>

          <view class="form-group">
            <view class="form-label">持卡人姓名</view>
            <view class="form-input-container">
              <input
                class="form-input"
                type="text"
                placeholder="请输入持卡人姓名"
                v-model="newBankCard.accountName"
              />
            </view>
          </view>

          <view class="form-group">
            <view class="form-label">身份证号</view>
            <view class="form-input-container">
              <input
                class="form-input"
                type="text"
                placeholder="请输入身份证号"
                v-model="newBankCard.idCard"
              />
            </view>
          </view>

          <view class="form-group">
            <view class="form-label">手机号</view>
            <view class="form-input-container">
              <input
                class="form-input"
                type="text"
                placeholder="请输入手机号"
                v-model="newBankCard.mobile"
              />
            </view>
          </view>

          <view class="form-group">
            <view class="form-checkbox">
              <checkbox
                :checked="newBankCard.isDefault"
                @change="toggleDefaultCard"
                color="#A875FF"
              />
              <view class="checkbox-label">设为默认银行卡</view>
            </view>
          </view>
        </view>

        <view class="modal-footer">
          <view class="btn btn-secondary" @tap="hideModal">取消</view>
          <view class="btn btn-primary" @tap="addBankCard" :class="{ disabled: submitting }">
            <text v-if="submitting">添加中...</text>
            <text v-else>确认添加</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import TabBar from '../../components/tab-bar.vue'

export default {
  components: {
    TabBar
  },
  data() {
    return {
      // 余额信息
      balanceInfo: {
        balance: 0,
        todayIncome: 0,
        monthIncome: 0,
        totalIncome: 0,
        todayIncomeChange: 0,
        monthIncomeChange: 0
      },
      
      // 提现相关
      withdrawAmount: '',
      withdrawPassword: '',
      selectedBankCard: null,
      bankCards: [],
      withdrawRecords: [],
      
      // 状态控制
      loading: false,
      loadingBankCards: false,
      submitting: false,
      showModal: '',
      
      // 分页信息
      pagination: {
        page: 1,
        size: 10,
        total: 0
      },

      // 新银行卡信息
      newBankCard: {
        bankName: '',
        bankAccount: '',
        accountName: '',
        idCard: '',
        mobile: '',
        isDefault: false
      }
    }
  },
  computed: {
    // 格式化金额
    formatAmount() {
      return (amount) => {
        if (!amount && amount !== 0) return '0.00';
        return parseFloat(amount).toFixed(2);
      }
    }
  },
  onLoad() {
    this.loadData();
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true;
      Promise.all([
        this.loadAccountInfo(),
        this.loadWithdrawRecords(),
        this.loadBankCards()
      ]).finally(() => {
        this.loading = false;
      });
    },

    // 刷新数据
    refreshData() {
      uni.showLoading({ title: '刷新中...' });
      this.loadData();
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: '数据已更新',
          icon: 'success'
        });
      }, 1000);
    },

    // 加载账户信息
    loadAccountInfo() {
      return this.$api.finance.getAccount()
        .then(res => {
          console.log('财务账户API响应:', res);
          const data = res.data || {};

          this.balanceInfo = {
            balance: data.availableBalance || 0,
            frozenBalance: data.frozenBalance || 0,
            todayIncome: data.todayIncome || 0,
            monthIncome: data.monthIncome || 0,
            totalIncome: data.totalRevenue || 0,
            todayIncomeChange: data.todayIncomeChange || 0,
            monthIncomeChange: data.monthIncomeChange || 0,
            totalWithdraw: data.totalWithdraw || 0,
            lastSettlementTime: data.lastSettlementTime
          };
        })
        .catch(err => {
          console.error('获取账户信息失败', err);
          // 使用模拟数据
          this.balanceInfo = {
            balance: 1234.56,
            todayIncome: 89.50,
            monthIncome: 2456.78,
            totalIncome: 15678.90,
            todayIncomeChange: 12.5,
            monthIncomeChange: 8.3
          };
        });
    },

    // 加载提现记录
    loadWithdrawRecords() {
      return this.$api.finance.getWithdrawRecords({
        page: this.pagination.page,
        size: this.pagination.size
      })
        .then(res => {
          const data = res.data || {};
          this.withdrawRecords = data.records || [];
          this.pagination.total = data.total || 0;
        })
        .catch(err => {
          console.error('获取提现记录失败', err);
          this.withdrawRecords = [];
        });
    },

    // 加载银行卡列表
    loadBankCards() {
      this.loadingBankCards = true;
      return this.$api.finance.getBankCards()
        .then(res => {
          this.bankCards = res.data || [];
          // 自动选择默认银行卡
          const defaultCard = this.bankCards.find(card => card.isDefault);
          if (defaultCard) {
            this.selectedBankCard = defaultCard;
          }
        })
        .catch(err => {
          console.error('获取银行卡列表失败', err);
          this.bankCards = [];
        })
        .finally(() => {
          this.loadingBankCards = false;
        });
    },

    // 显示提现模态框
    showWithdrawModal() {
      this.showModal = 'withdraw';
      this.withdrawAmount = '';
      this.withdrawPassword = '';
    },

    // 显示银行卡选择模态框
    showBankCardModal() {
      this.showModal = 'bankCard';
    },

    // 显示交易记录
    showTransactionHistory() {
      uni.navigateTo({
        url: '/pages/income/transaction-history'
      });
    },

    // 显示银行卡管理
    showBankCardManagement() {
      this.showModal = 'bankCardManagement';
    },

    // 隐藏模态框
    hideModal() {
      this.showModal = '';
    },

    // 选择银行卡
    selectBankCard(card) {
      this.selectedBankCard = card;
    },

    // 确认银行卡选择
    confirmBankCard() {
      if (!this.selectedBankCard) {
        uni.showToast({
          title: '请选择银行卡',
          icon: 'none'
        });
        return;
      }
      this.showModal = 'withdraw';
    },

    // 验证提现金额
    validateWithdrawAmount() {
      const amount = parseFloat(this.withdrawAmount);
      const balance = parseFloat(this.balanceInfo.balance);

      if (isNaN(amount) || amount <= 0) {
        return false;
      }

      if (amount > balance) {
        uni.showToast({
          title: '提现金额不能超过余额',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 提交提现申请
    submitWithdraw() {
      if (this.submitting) return;

      // 验证提现金额
      if (!this.validateWithdrawAmount()) {
        uni.showToast({
          title: '请输入有效的提现金额',
          icon: 'none'
        });
        return;
      }

      // 验证银行卡
      if (!this.selectedBankCard) {
        uni.showToast({
          title: '请选择银行卡',
          icon: 'none'
        });
        return;
      }

      // 验证提现密码
      if (!this.withdrawPassword || this.withdrawPassword.length !== 6) {
        uni.showToast({
          title: '请输入6位提现密码',
          icon: 'none'
        });
        return;
      }

      this.submitting = true;

      // 调用提现API
      this.$api.finance.withdraw({
        amount: parseFloat(this.withdrawAmount),
        bankCardId: this.selectedBankCard.id,
        password: this.withdrawPassword,
        remark: '门店提现'
      })
        .then(res => {
          uni.showToast({
            title: '提现申请已提交',
            icon: 'success'
          });

          this.hideModal();
          this.loadData(); // 重新加载数据
        })
        .catch(err => {
          console.error('提现申请失败', err);

          let errorMessage = '提现申请失败';
          if (err.message) {
            errorMessage = err.message;
          } else if (err.data && err.data.message) {
            errorMessage = err.data.message;
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        })
        .finally(() => {
          this.submitting = false;
        });
    },

    // 格式化日期时间
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '暂无记录';

      try {
        const date = new Date(dateTimeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return '时间格式错误';
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待审核',
        'processing': '处理中',
        'success': '成功',
        'failed': '失败'
      };
      return statusMap[status] || '未知状态';
    },

    // 获取状态样式类
    getStatusClass(status) {
      const classMap = {
        'pending': 'status-pending',
        'processing': 'status-processing',
        'success': 'status-success',
        'failed': 'status-failed'
      };
      return classMap[status] || '';
    },

    // 显示添加银行卡模态框
    showAddBankCardModal() {
      this.showModal = 'addBankCard';
      this.resetNewBankCard();
    },

    // 重置新银行卡表单
    resetNewBankCard() {
      this.newBankCard = {
        bankName: '',
        bankAccount: '',
        accountName: '',
        idCard: '',
        mobile: '',
        isDefault: false
      };
    },

    // 切换默认卡设置
    toggleDefaultCard(e) {
      this.newBankCard.isDefault = e.detail.value;
    },

    // 添加银行卡
    addBankCard() {
      if (this.submitting) return;

      // 验证表单
      if (!this.validateBankCardForm()) {
        return;
      }

      this.submitting = true;

      // 调用API添加银行卡
      this.$api.finance.addBankCard(this.newBankCard)
        .then(res => {
          uni.showToast({
            title: '银行卡添加成功',
            icon: 'success'
          });

          this.hideModal();
          this.loadBankCards(); // 重新加载银行卡列表
        })
        .catch(err => {
          console.error('添加银行卡失败', err);

          let errorMessage = '添加银行卡失败';
          if (err.message) {
            errorMessage = err.message;
          } else if (err.data && err.data.message) {
            errorMessage = err.data.message;
          }

          uni.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 3000
          });
        })
        .finally(() => {
          this.submitting = false;
        });
    },

    // 验证银行卡表单
    validateBankCardForm() {
      if (!this.newBankCard.bankName.trim()) {
        uni.showToast({
          title: '请输入银行名称',
          icon: 'none'
        });
        return false;
      }

      if (!this.newBankCard.bankAccount.trim()) {
        uni.showToast({
          title: '请输入银行卡号',
          icon: 'none'
        });
        return false;
      }

      if (!this.newBankCard.accountName.trim()) {
        uni.showToast({
          title: '请输入持卡人姓名',
          icon: 'none'
        });
        return false;
      }

      if (!this.newBankCard.idCard.trim()) {
        uni.showToast({
          title: '请输入身份证号',
          icon: 'none'
        });
        return false;
      }

      if (!this.newBankCard.mobile.trim()) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        });
        return false;
      }

      // 验证身份证号格式
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(this.newBankCard.idCard)) {
        uni.showToast({
          title: '身份证号格式不正确',
          icon: 'none'
        });
        return false;
      }

      // 验证手机号格式
      const mobileRegex = /^1[3-9]\d{9}$/;
      if (!mobileRegex.test(this.newBankCard.mobile)) {
        uni.showToast({
          title: '手机号格式不正确',
          icon: 'none'
        });
        return false;
      }

      return true;
    },

    // 设置默认银行卡
    setDefaultBankCard(card) {
      uni.showModal({
        title: '确认操作',
        content: `确定要将${card.bankName}(${card.bankAccount.slice(-4)})设为默认银行卡吗？`,
        success: (res) => {
          if (res.confirm) {
            this.$api.finance.setDefaultBankCard(card.id)
              .then(() => {
                uni.showToast({
                  title: '设置成功',
                  icon: 'success'
                });
                this.loadBankCards();
              })
              .catch(err => {
                console.error('设置默认银行卡失败', err);
                uni.showToast({
                  title: '设置失败',
                  icon: 'none'
                });
              });
          }
        }
      });
    },

    // 编辑银行卡
    editBankCard(card) {
      uni.showToast({
        title: '编辑功能开发中',
        icon: 'none'
      });
    },

    // 删除银行卡
    deleteBankCard(card) {
      uni.showModal({
        title: '确认删除',
        content: `确定要删除${card.bankName}(${card.bankAccount.slice(-4)})吗？`,
        success: (res) => {
          if (res.confirm) {
            this.$api.finance.deleteBankCard(card.id)
              .then(() => {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                this.loadBankCards();
              })
              .catch(err => {
                console.error('删除银行卡失败', err);
                uni.showToast({
                  title: '删除失败',
                  icon: 'none'
                });
              });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --bg-dark: #121212;
  --bg-card: rgba(255, 255, 255, 0.05);
  --text-primary: #FFFFFF;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-tertiary: rgba(255, 255, 255, 0.5);
  --primary: #9527C6;
  --primary-dark: #6E3AD9;
  --primary-light: #A875FF;
  --border-light: rgba(255, 255, 255, 0.1);
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --space-xs: 8rpx;
  --space-sm: 16rpx;
  --space-md: 24rpx;
  --space-lg: 32rpx;
}

/* 基础容器样式 */
.container {
  min-height: 100vh;
  padding: 30rpx 30rpx 150rpx 30rpx;
  background-color: var(--bg-dark);
  color: var(--text-primary);
  background-image:
    radial-gradient(circle at top right, rgba(167, 86, 229, 0.15) 0%, transparent 35%),
    radial-gradient(circle at center left, rgba(0, 210, 255, 0.08) 0%, transparent 30%),
    radial-gradient(circle at bottom center, rgba(255, 44, 146, 0.1) 0%, transparent 40%);
}

.top-spacing {
  height: 60rpx;
}

/* 页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-lg);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: var(--space-xs);
  background: linear-gradient(to right, #FFFFFF, var(--primary-light));
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.3);
}

.page-subtitle {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.refresh-btn {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.refresh-btn:active {
  transform: scale(0.95);
  background: rgba(168, 117, 255, 0.1);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: var(--space-lg);
}

.stat-card {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: 24rpx;
  border: 1px solid var(--border-light);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(168, 117, 255, 0.2);
}

.stat-title {
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 12rpx;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.stat-change {
  display: flex;
  align-items: center;
  font-size: 20rpx;
  gap: 4rpx;
}

.stat-change.positive {
  color: #4CAF50;
}

.stat-change.negative {
  color: #F44336;
}

/* 操作按钮 */
.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16rpx;
  margin-bottom: 40rpx;
  padding: 0 8rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  padding: 20rpx 16rpx;
  border-radius: var(--radius-md);
  font-size: 24rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  min-height: 120rpx;
}

.action-btn.primary {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: #FFFFFF;
  box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.3);
}

.action-btn.primary:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.4);
}

.action-btn.secondary {
  background: rgba(168, 117, 255, 0.1);
  color: var(--primary-light);
  border: 1px solid rgba(168, 117, 255, 0.3);
}

.action-btn.secondary:active {
  transform: scale(0.95);
  background: rgba(168, 117, 255, 0.2);
}

.action-btn.tertiary {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-primary);
  border: 1px solid var(--border-light);
}

.action-btn.tertiary:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.1);
}

/* 区块样式 */
.section {
  margin-bottom: var(--space-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.section-action {
  padding: 12rpx;
  color: var(--primary-light);
  font-size: 32rpx;
}

/* 提现记录列表 */
.withdraw-list {
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

.withdraw-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1px solid var(--border-light);
}

.withdraw-item:last-child {
  border-bottom: none;
}

.withdraw-info {
  flex: 1;
}

.withdraw-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8rpx;
}

.withdraw-time {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.withdraw-status {
  padding: 8rpx 16rpx;
  border-radius: var(--radius-sm);
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.status-processing {
  background: rgba(33, 150, 243, 0.2);
  color: #2196F3;
}

.status-success {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.status-failed {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

/* 加载和空状态 */
.loading-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;
  color: var(--text-secondary);
  text-align: center;
}

.loading-state .material-icons,
.empty-state .material-icons {
  font-size: 80rpx;
  color: var(--text-tertiary);
  margin-bottom: 16rpx;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 霓虹灯效果 */
.neon-green {
  color: #4CAF50;
  text-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);
}

.neon-pink {
  color: #E91E63;
  text-shadow: 0 0 10rpx rgba(233, 30, 99, 0.5);
}

.neon-blue {
  color: #2196F3;
  text-shadow: 0 0 10rpx rgba(33, 150, 243, 0.5);
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10px);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 580rpx;
  max-height: 85vh;
  background: var(--bg-dark);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 28rpx;
  border-bottom: 1px solid var(--border-light);
}

.modal-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  padding: 8rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.modal-close:active {
  transform: scale(0.9);
  color: var(--text-primary);
}

.modal-body {
  padding: 24rpx 28rpx;
  max-height: 65vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 28rpx 24rpx;
  border-top: 1px solid var(--border-light);
}

/* 表单样式 */
.form-group {
  margin-bottom: 24rpx;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 26rpx;
  color: var(--text-primary);
  margin-bottom: 10rpx;
  font-weight: 500;
  display: block;
}

.form-input-container {
  position: relative;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.form-input-container:focus-within {
  transform: translateY(-1rpx);
}

.form-input {
  flex: 1;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 26rpx;
  line-height: 1.4;
  transition: all 0.3s ease;
  min-height: 44rpx;
}

.form-input:focus {
  border-color: var(--primary-light);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 0 3rpx rgba(168, 117, 255, 0.15);
  outline: none;
}

.form-input::placeholder {
  color: var(--text-tertiary);
  font-size: 24rpx;
}

.input-unit {
  position: absolute;
  right: 20rpx;
  color: var(--text-secondary);
  font-size: 24rpx;
}

.form-hint {
  margin-top: 6rpx;
  font-size: 22rpx;
  color: var(--text-tertiary);
  line-height: 1.3;
}

/* 银行卡选择器 */
.bank-card-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.bank-card-selector:active {
  background: rgba(255, 255, 255, 0.1);
}

.bank-card-info {
  flex: 1;
}

.bank-name {
  font-size: 28rpx;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.bank-account {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.no-bank-card {
  display: flex;
  align-items: center;
  gap: 12rpx;
  color: var(--text-secondary);
  font-size: 28rpx;
}

/* 银行卡列表 */
.bank-card-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.bank-card-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.bank-card-item.selected {
  background: rgba(168, 117, 255, 0.1);
  border-color: var(--primary-light);
}

.bank-card-item:active {
  transform: scale(0.98);
}

.account-name {
  font-size: 24rpx;
  color: var(--text-tertiary);
  margin-top: 4rpx;
}

.bank-card-action .material-icons {
  font-size: 32rpx;
  color: var(--primary-light);
}

.add-bank-card-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  padding: 24rpx;
  margin-top: 20rpx;
  background: rgba(168, 117, 255, 0.1);
  border: 1px dashed var(--primary-light);
  border-radius: var(--radius-md);
  color: var(--primary-light);
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.add-bank-card-btn:active {
  transform: scale(0.95);
  background: rgba(168, 117, 255, 0.2);
}

/* 按钮样式 */
.btn {
  flex: 1;
  padding: 16rpx 24rpx;
  border-radius: var(--radius-md);
  text-align: center;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 80rpx;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-light), var(--primary));
  color: #FFFFFF;
  box-shadow: 0 4rpx 20rpx rgba(168, 117, 255, 0.3);
}

.btn-primary:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 10rpx rgba(168, 117, 255, 0.4);
}

.btn-primary.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.05);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
}

.btn-secondary:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.1);
}

/* 银行卡管理样式 */
.bank-card-management {
  max-height: 70vh;
  overflow-y: auto;
}

.bank-card-management-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.bank-card-management-item:hover {
  background: rgba(255, 255, 255, 0.08);
}

.bank-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.bank-default {
  font-size: 20rpx;
  color: var(--primary-light);
  background: rgba(168, 117, 255, 0.2);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  margin-left: 12rpx;
}

.bank-card-actions {
  display: flex;
  gap: 16rpx;
}

.action-icon {
  padding: 12rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.action-icon:active {
  transform: scale(0.9);
}

.action-icon.danger {
  color: #F44336;
}

.action-icon.danger:active {
  background: rgba(244, 67, 54, 0.2);
}

/* 表单复选框样式 */
.form-checkbox {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--radius-md);
  padding: 16rpx 20rpx;
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.form-checkbox:active {
  background: rgba(255, 255, 255, 0.06);
}

.checkbox-label {
  margin-left: 12rpx;
  font-size: 26rpx;
  color: var(--text-primary);
  flex: 1;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .action-buttons {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .action-btn {
    flex-direction: row;
    min-height: auto;
    padding: 16rpx 20rpx;
  }

  .action-btn .material-icons {
    margin-right: 8rpx;
    margin-bottom: 0;
  }
}
</style>
