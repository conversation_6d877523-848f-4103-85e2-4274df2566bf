# 门店财务数据显示性能优化建议

## 📊 当前性能分析

### 1. 数据库索引状态
- ✅ `jy_commission_detail.order_id` 已有唯一索引 `uk_order_id`
- ✅ `jy_commission_detail.shop_id` 已有索引 `idx_shop_id`
- ✅ `jy_commission_detail.create_time` 已有索引 `idx_create_time`

### 2. 查询性能瓶颈
- **门店订单查询**：每次查询都需要关联 `jy_commission_detail` 表
- **设备统计查询**：需要聚合大量订单数据计算分成收入
- **分页查询**：大数据量时可能出现性能问题

## 🚀 优化方案

### 1. 缓存策略优化

#### Redis 缓存设计
```java
// 门店分成金额缓存
Key: "shop:commission:{orderId}"
Value: BigDecimal (门店分成金额)
TTL: 24小时

// 设备统计数据缓存
Key: "device:stats:{deviceId}"
Value: JSON {totalOrders, totalRevenue, lastUpdateTime}
TTL: 1小时
```

#### 缓存更新策略
- **订单支付完成**：立即更新相关缓存
- **分成处理完成**：更新订单分成缓存和设备统计缓存
- **定时任务**：每小时刷新设备统计缓存

### 2. 数据库查询优化

#### 批量查询优化
```java
// 原始方案：N+1查询问题
for (Order order : orders) {
    CommissionDetail detail = commissionDetailService.getByOrderId(order.getId());
}

// 优化方案：批量查询
List<String> orderIds = orders.stream().map(Order::getId).collect(toList());
Map<String, CommissionDetail> commissionMap = commissionDetailService
    .lambdaQuery()
    .in(CommissionDetail::getOrderId, orderIds)
    .list()
    .stream()
    .collect(toMap(CommissionDetail::getOrderId, Function.identity()));
```

#### 统计查询优化
```sql
-- 优化前：多次查询
SELECT COUNT(*) FROM jy_order WHERE device_id = ?;
SELECT SUM(shop_amount) FROM jy_commission_detail WHERE order_id IN (...);

-- 优化后：一次关联查询
SELECT 
    COUNT(o.id) as total_orders,
    SUM(CASE WHEN o.pay_status = 1 THEN COALESCE(cd.shop_amount, 0) ELSE 0 END) as total_revenue
FROM jy_order o
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id
WHERE o.device_id = ?;
```

### 3. 异步处理优化

#### 统计数据异步更新
```java
@Async("statisticsExecutor")
public void updateDeviceStatistics(Long deviceId) {
    // 异步更新设备统计数据
    DeviceStatistics stats = calculateDeviceStatistics(deviceId);
    redisTemplate.opsForValue().set("device:stats:" + deviceId, stats, 1, TimeUnit.HOURS);
}
```

#### 分成数据预计算
```java
@EventListener
public void onOrderCompleted(OrderCompletedEvent event) {
    // 同步处理分成计算
    commissionService.processOrderCommission(event.getOrderId());
    
    // 异步更新相关缓存
    asyncService.updateRelatedCaches(event.getOrderId());
}
```

## 📈 性能监控建议

### 1. 关键指标监控
- **查询响应时间**：门店订单列表查询 < 500ms
- **设备统计查询**：设备列表查询 < 1000ms
- **缓存命中率**：> 90%
- **数据库连接池**：使用率 < 80%

### 2. 慢查询监控
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- 监控慢查询
SELECT * FROM mysql.slow_log 
WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY query_time DESC;
```

### 3. 应用性能监控
```java
@Component
public class PerformanceMonitor {
    
    @EventListener
    public void onSlowQuery(SlowQueryEvent event) {
        if (event.getExecutionTime() > 1000) {
            log.warn("慢查询检测: SQL={}, 执行时间={}ms", 
                event.getSql(), event.getExecutionTime());
        }
    }
}
```

## 🔧 实施建议

### 1. 分阶段实施
1. **第一阶段**：修复显示问题，添加基础缓存
2. **第二阶段**：优化查询性能，实施批量查询
3. **第三阶段**：完善监控体系，优化缓存策略

### 2. 风险控制
- **灰度发布**：先在测试环境验证，再逐步推广
- **回滚方案**：保留原有查询逻辑作为降级方案
- **数据一致性**：确保缓存与数据库数据一致

### 3. 测试验证
- **功能测试**：验证修复后数据显示正确
- **性能测试**：验证查询响应时间改善
- **压力测试**：验证高并发场景下的稳定性

## 📋 部署检查清单

### 代码部署前
- [ ] 代码审查通过
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过
- [ ] 性能测试通过

### 数据库变更
- [ ] 索引创建完成
- [ ] 数据一致性验证
- [ ] 备份策略确认

### 缓存配置
- [ ] Redis 配置优化
- [ ] 缓存策略验证
- [ ] 缓存预热完成

### 监控配置
- [ ] 慢查询监控启用
- [ ] 应用性能监控配置
- [ ] 告警规则设置

## 🎯 预期效果

### 性能提升
- **查询响应时间**：提升 60-80%
- **数据库负载**：降低 40-60%
- **用户体验**：页面加载速度显著提升

### 数据准确性
- **门店收益显示**：100% 准确显示分成金额
- **设备统计数据**：实时准确的统计信息
- **财务数据一致性**：确保数据完整性
