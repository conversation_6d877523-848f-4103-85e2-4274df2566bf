-- 快速修复：为门店管理员添加财务查看权限
-- 这个脚本可以立即解决当前的权限问题

-- 1. 确保财务查看权限存在
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time)
VALUES ('shop:finance:read', '门店财务信息查看权限', 1, NOW(), NOW());

-- 2. 为门店管理员角色分配财务查看权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_role r
CROSS JOIN jy_permission p
WHERE r.role_code = 'shop_admin'
  AND p.permission = 'shop:finance:read'
  AND p.status = 1;

-- 3. 验证权限是否添加成功
SELECT
    r.role_name,
    p.permission,
    p.description
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.permission = 'shop:finance:read';

-- 执行完成提示
SELECT '财务查看权限已添加，请重新登录以获取最新权限！' AS message;
