@echo off
echo ========================================
echo 立即清理设备缓存 - 修复生效
echo ========================================

echo.
echo 🔧 正在连接Redis清理缓存...
echo.

redis-cli -h ************** -p 6379 -a redis_ZWE7i5 --eval - <<EOF
-- 清理所有设备相关缓存
local patterns = {
    'business:device:*',
    'business:device:detail:*',
    'business:device:mac:*', 
    'business:device:no:*',
    'business:device:bind:*',
    'business:shop:devices:*',
    'device::*',
    'deviceByNo::*'
}

local total_deleted = 0
for i, pattern in ipairs(patterns) do
    local keys = redis.call('KEYS', pattern)
    if #keys > 0 then
        local deleted = redis.call('DEL', unpack(keys))
        total_deleted = total_deleted + deleted
        redis.log(redis.LOG_NOTICE, 'Deleted ' .. deleted .. ' keys matching pattern: ' .. pattern)
    end
end

return total_deleted
EOF

echo.
echo ✅ 缓存清理完成！
echo.
echo 📋 修复内容摘要：
echo   1. ✅ 修复了 getDeviceStatistics() 中的 count() 无条件查询
echo   2. ✅ 所有设备统计查询都增加了 is_bound=1 条件
echo   3. ✅ 修复了地区分布统计查询
echo   4. ✅ 清理了所有设备相关缓存
echo.
echo 🚀 现在应该不再出现以下查询：
echo   SELECT COUNT(*) AS total FROM jy_device
echo.
echo ⚠️  重要提醒：
echo   如果应用正在运行，建议重启以确保修复完全生效
echo.
echo ========================================
pause
