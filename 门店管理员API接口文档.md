# 门店管理员API接口文档

## 项目概述

今夜城堡(JYCB-Z)门店管理员API接口文档，提供门店管理员日常运营管理所需的所有接口功能。

## 基础信息

- **基础URL**: `http://localhost:8081`
- **API版本**: v1.0
- **认证方式**: <PERSON><PERSON> (Sa-Token)
- **数据格式**: JSON

## 统一响应格式

所有接口返回格式统一为：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

**状态码说明**：
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未认证或token过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 认证接口

### 1. 管理员登录

**接口**: `POST /auth/admin/login`

**描述**: 门店管理员登录认证

**请求参数**:

| 参数名   | 类型   | 必填 | 描述   |
|---------|--------|-----|--------|
| username | String | 是   | 用户名 |
| password | String | 是   | 密码   |

**请求示例**:

```json
{
  "username": "shop_admin_001",
  "password": "123456"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenName": "Authorization",
    "tokenTimeout": 86400,
    "userInfo": {
      "id": 10001,
      "username": "shop_admin_001",
      "realName": "张店长",
      "role": "SHOP_ADMIN",
      "shopId": 1001,
      "mobile": "***********",
      "email": "<EMAIL>"
    },
    "permissions": [
      "shop:device:read",
      "shop:order:read",
      "shop:finance:read",
      "shop:finance:write"
    ],
    "menus": [
      {
        "id": 1,
        "name": "设备管理",
        "path": "/device",
        "icon": "device"
      }
    ]
  }
}
```

### 2. 管理员登出

**接口**: `POST /admin/auth/logout`

**描述**: 门店管理员登出

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 3. 检查登录状态

**接口**: `GET /admin/auth/check`

**描述**: 检查当前登录状态

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isLogin": true,
    "loginId": "10001",
    "tokenTimeout": 7200
  }
}
```

### 4. 获取管理员信息

**接口**: `GET /auth/admin/info`

**描述**: 获取当前登录管理员详细信息

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 10001,
    "username": "shop_admin_001",
    "realName": "张店长",
    "mobile": "***********",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "status": 1,
    "createTime": "2024-01-01T10:00:00",
    "lastLoginTime": "2024-01-01T12:00:00",
    "shopInfo": {
      "id": 1001,
      "shopName": "测试门店",
      "address": "北京市朝阳区xxx街道",
      "contactName": "张店长",
      "contactPhone": "***********"
    }
  }
}
```

## 门店管理接口

### 1. 获取当前门店信息

**接口**: `GET /shop/admin/info`

**描述**: 获取当前登录门店管理员所属门店的详细信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "shopName": "测试门店",
    "shopCode": "SHOP001",
    "address": "北京市朝阳区xxx街道",
    "contactName": "张店长",
    "contactPhone": "***********",
    "businessHours": "09:00-22:00",
    "status": 1,
    "statusName": "正常营业",
    "partnerId": 101,
    "partnerName": "测试合作商",
    "entityId": 1,
    "entityName": "测试业务主体",
    "createTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T12:00:00"
  }
}
```

### 2. 获取门店数据统计仪表盘

**接口**: `GET /shop/admin/dashboard`

**描述**: 获取当前门店的统计数据，包括订单数、收入等

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "todayStats": {
      "orderCount": 25,
      "revenue": 1250.00,
      "deviceUsageRate": 0.85
    },
    "monthStats": {
      "orderCount": 680,
      "revenue": 34000.00,
      "avgDailyRevenue": 1100.00
    },
    "deviceStats": {
      "totalCount": 20,
      "onlineCount": 18,
      "maintenanceCount": 1,
      "faultCount": 1
    },
    "revenueChart": [
      {
        "date": "2024-01-01",
        "revenue": 1200.00
      }
    ]
  }
}
```

### 3. 修改门店联系人信息

**接口**: `PUT /shop/admin/contact`

**描述**: 修改当前门店的联系人姓名和电话

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名       | 类型   | 必填 | 描述     |
|-------------|--------|-----|----------|
| contactName | String | 是   | 联系人姓名 |
| contactPhone| String | 是   | 联系人电话 |

**请求示例**:

```json
{
  "contactName": "李店长",
  "contactPhone": "***********"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "联系人信息更新成功",
  "data": true
}
```

### 4. 修改门店银行账户信息

**接口**: `PUT /shop/admin/bank-account`

**描述**: 修改当前门店的银行账户信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型   | 必填 | 描述     |
|------------|--------|-----|----------|
| bankName   | String | 是   | 开户银行  |
| bankAccount| String | 是   | 银行卡号  |
| accountName| String | 是   | 收款人姓名 |

**请求示例**:

```json
{
  "bankName": "中国工商银行",
  "bankAccount": "6222021234567890123",
  "accountName": "张三"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "银行账户信息更新成功",
  "data": true
}
```

## 设备管理接口

### 1. 获取门店设备列表

**接口**: `GET /api/shop/devices`

**描述**: 获取当前门店的设备列表，支持分页和筛选

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名   | 类型    | 必填 | 默认值 | 描述                           |
|---------|---------|-----|-------|--------------------------------|
| page    | Integer | 否   | 1     | 页码                           |
| size    | Integer | 否   | 10    | 每页条数                       |
| deviceNo| String  | 否   | -     | 设备编号                       |
| status  | Integer | 否   | -     | 设备状态：1-正常 2-维护中 3-故障 |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1001,
        "deviceNo": "DEV001",
        "deviceName": "设备001",
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "status": 1,
        "statusName": "正常",
        "batteryLevel": 85,
        "isOnline": true,
        "roomNumber": "101",
        "lastHeartbeat": "2024-01-01T12:00:00",
        "createTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 20,
    "size": 10,
    "current": 1,
    "pages": 2
  }
}
```

### 2. 获取设备详情

**接口**: `GET /api/shop/devices/{id}`

**描述**: 获取指定设备的详细信息

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型    | 必填 | 描述   |
|-------|---------|-----|--------|
| id    | Integer | 是   | 设备ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "bindCode": "BIND123",
    "status": 1,
    "statusName": "正常",
    "batteryLevel": 85,
    "isOnline": true,
    "roomNumber": "101",
    "shopId": 1001,
    "shopName": "测试门店",
    "partnerId": 101,
    "partnerName": "测试合作商",
    "entityId": 1,
    "entityName": "测试业务主体",
    "lastHeartbeat": "2024-01-01T12:00:00",
    "bindTime": "2024-01-01T10:00:00",
    "createTime": "2024-01-01T09:00:00",
    "updateTime": "2024-01-01T12:00:00"
  }
}
```

### 3. 根据MAC地址查询设备

**接口**: `GET /api/shop/devices/mac/{macAddress}`

**描述**: 根据MAC地址查询设备信息

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名     | 类型   | 必填 | 描述      |
|-----------|--------|-----|-----------|
| macAddress| String | 是   | MAC地址   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "status": 1,
    "statusName": "正常",
    "batteryLevel": 85,
    "isOnline": true,
    "roomNumber": "101"
  }
}
```

### 4. 更新设备状态

**接口**: `PUT /api/shop/devices/{id}/status`

**描述**: 更新设备状态，用于报告设备故障或维护需求

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型    | 必填 | 描述   |
|-------|---------|-----|--------|
| id    | Integer | 是   | 设备ID |

**请求参数**:

| 参数名 | 类型    | 必填 | 描述                           |
|-------|---------|-----|--------------------------------|
| status| Integer | 是   | 设备状态：1-正常 2-维护中 3-故障 |

**请求示例**:

```json
{
  "status": 3
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "设备状态更新成功",
  "data": null
}
```

### 5. 获取门店设备统计信息

**接口**: `GET /api/shop/devices/statistics`

**描述**: 获取当前门店的设备统计数据

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 20,
    "onlineCount": 18,
    "offlineCount": 2,
    "normalCount": 17,
    "maintenanceCount": 2,
    "faultCount": 1,
    "batteryLowCount": 3,
    "usageRate": 0.85,
    "avgBatteryLevel": 78.5
  }
}
```

## 订单管理接口

### 1. 分页查询门店订单列表

**接口**: `GET /shop/order/page`

**描述**: 查看当前门店下的订单列表，支持状态、时间、房间号等筛选

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 必填 | 默认值 | 描述                                    |
|-----------|---------|-----|-------|-----------------------------------------|
| current   | Integer | 否   | 1     | 当前页                                  |
| size      | Integer | 否   | 10    | 每页大小                                |
| orderNo   | String  | 否   | -     | 订单号                                  |
| status    | Integer | 否   | -     | 订单状态：0-待支付 1-进行中 2-已完成 3-已取消 |
| startDate | String  | 否   | -     | 开始日期 (yyyy-MM-dd)                   |
| endDate   | String  | 否   | -     | 结束日期 (yyyy-MM-dd)                   |
| roomNumber| String  | 否   | -     | 房间号                                  |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 10001,
        "orderNo": "ORD20240101001",
        "deviceId": 1001,
        "deviceNo": "DEV001",
        "roomNumber": "101",
        "userId": 2001,
        "userName": "张三",
        "userPhone": "***********",
        "amount": 50.00,
        "actualAmount": 45.00,
        "orderStatus": 2,
        "orderStatusName": "已完成",
        "payStatus": 1,
        "payStatusName": "已支付",
        "payType": "WECHAT",
        "startTime": "2024-01-01T10:00:00",
        "endTime": "2024-01-01T11:30:00",
        "duration": 90,
        "createTime": "2024-01-01T09:55:00"
      }
    ],
    "total": 150,
    "size": 10,
    "current": 1,
    "pages": 15
  }
}
```

### 2. 获取订单详情

**接口**: `GET /shop/order/{id}`

**描述**: 获取指定订单的详细信息

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述   |
|-------|------|-----|--------|
| id    | Long | 是   | 订单ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 10001,
    "orderNo": "ORD20240101001",
    "deviceId": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "roomNumber": "101",
    "userId": 2001,
    "userName": "张三",
    "userPhone": "***********",
    "userAvatar": "https://example.com/avatar.jpg",
    "amount": 50.00,
    "actualAmount": 45.00,
    "discountAmount": 5.00,
    "orderStatus": 2,
    "orderStatusName": "已完成",
    "payStatus": 1,
    "payStatusName": "已支付",
    "payType": "WECHAT",
    "payTypeName": "微信支付",
    "transactionId": "wx123456789",
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T11:30:00",
    "duration": 90,
    "remark": "正常使用",
    "createTime": "2024-01-01T09:55:00",
    "payTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T11:30:00"
  }
}
```

### 3. 获取订单统计数据

**接口**: `GET /shop/order/statistics`

**描述**: 获取门店订单统计数据

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名    | 类型   | 必填 | 描述                    |
|----------|--------|-----|-------------------------|
| startDate| String | 否   | 开始日期 (yyyy-MM-dd)   |
| endDate  | String | 否   | 结束日期 (yyyy-MM-dd)   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOrders": 1250,
    "completedOrders": 1180,
    "cancelledOrders": 70,
    "totalRevenue": 62500.00,
    "avgOrderAmount": 50.00,
    "completionRate": 0.944,
    "todayOrders": 25,
    "todayRevenue": 1250.00,
    "monthOrders": 680,
    "monthRevenue": 34000.00,
    "orderTrend": [
      {
        "date": "2024-01-01",
        "orderCount": 25,
        "revenue": 1250.00
      }
    ],
    "hourlyDistribution": [
      {
        "hour": 9,
        "orderCount": 5
      }
    ]
  }
}
```

### 4. 导出订单数据

**接口**: `POST /shop/order/export`

**描述**: 导出门店订单数据为Excel文件

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "status": 2,
  "roomNumber": "101"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "fileName": "门店订单数据_20240101_20240131.xlsx",
    "downloadUrl": "https://example.com/download/orders_20240101.xlsx",
    "fileSize": 1024000,
    "recordCount": 150
  }
}
```

## 财务管理接口

### 1. 获取门店财务账户信息

**接口**: `GET /shop/finance/account`

**描述**: 查看门店的账户余额、收益明细等财务信息

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopId": 1001,
    "shopName": "测试门店",
    "accountId": 5001,
    "totalRevenue": 125000.00,
    "availableBalance": 8500.00,
    "frozenBalance": 1500.00,
    "totalWithdraw": 115000.00,
    "pendingSettlement": 2000.00,
    "commissionRate": 0.15,
    "settlementCycle": "WEEKLY",
    "settlementCycleName": "周结",
    "nextSettlementTime": "2024-01-08T00:00:00",
    "defaultBankCardId": 3001,
    "defaultBankCard": {
      "id": 3001,
      "bankName": "中国工商银行",
      "bankAccount": "****7890",
      "accountName": "张三"
    },
    "bankCardCount": 2,
    "withdrawingAmount": 500.00,
    "todayWithdrawCount": 1,
    "todayWithdrawAmount": 500.00,
    "monthWithdrawCount": 8,
    "monthWithdrawAmount": 15000.00,
    "status": 1,
    "statusName": "正常",
    "hasWithdrawPassword": true
  }
}
```

### 2. 获取门店收益明细

**接口**: `GET /shop/finance/revenue-details`

**描述**: 分页查询门店的收益明细记录

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述                    |
|----------|---------|-----|-------|-------------------------|
| current  | Integer | 否   | 1     | 当前页                  |
| size     | Integer | 否   | 10    | 每页大小                |
| startDate| String  | 否   | -     | 开始日期 (yyyy-MM-dd)   |
| endDate  | String  | 否   | -     | 结束日期 (yyyy-MM-dd)   |
| type     | String  | 否   | -     | 收益类型                |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 20001,
        "orderId": 10001,
        "orderNo": "ORD20240101001",
        "amount": 7.50,
        "type": "ORDER_COMMISSION",
        "typeName": "订单分成",
        "description": "订单分成收益",
        "createTime": "2024-01-01T11:30:00",
        "deviceNo": "DEV001",
        "roomNumber": "101"
      }
    ],
    "total": 500,
    "size": 10,
    "current": 1,
    "pages": 50
  }
}
```

### 3. 获取门店分成配置信息

**接口**: `GET /shop/finance/commission-config`

**描述**: 查看门店的分成比例和结算周期配置（只读）

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopId": 1001,
    "commissionRate": 0.15,
    "commissionRatePercent": "15%",
    "settlementCycle": "WEEKLY",
    "settlementCycleName": "周结",
    "settlementDay": 1,
    "settlementDayName": "周一",
    "minSettlementAmount": 100.00,
    "maxDailyWithdraw": 10000.00,
    "withdrawFeeRate": 0.001,
    "withdrawFeeRatePercent": "0.1%",
    "isAutoSettlement": true,
    "configSource": "PARTNER",
    "configSourceName": "合作商配置",
    "lastUpdateTime": "2024-01-01T10:00:00"
  }
}
```

### 4. 获取银行卡列表

**接口**: `GET /shop/finance/bank-cards`

**描述**: 获取门店的银行卡列表

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 3001,
      "shopId": 1001,
      "bankName": "中国工商银行",
      "bankAccount": "6222021234567890123",
      "accountName": "张三",
      "idCard": "110101199001011234",
      "mobile": "***********",
      "isDefault": true,
      "status": 1,
      "statusName": "正常",
      "createTime": "2024-01-01T10:00:00",
      "updateTime": "2024-01-01T10:00:00"
    }
  ]
}
```

### 5. 添加银行卡

**接口**: `POST /shop/finance/bank-cards`

**描述**: 为门店添加新的银行卡

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型   | 必填 | 描述       |
|------------|--------|-----|------------|
| bankName   | String | 是   | 银行名称   |
| bankAccount| String | 是   | 银行卡号   |
| accountName| String | 是   | 开户人姓名 |
| idCard     | String | 是   | 身份证号   |
| mobile     | String | 是   | 手机号     |
| isDefault  | Boolean| 否   | 是否默认   |

**请求示例**:

```json
{
  "bankName": "中国建设银行",
  "bankAccount": "6227001234567890123",
  "accountName": "李四",
  "idCard": "110101199002021234",
  "mobile": "***********",
  "isDefault": false
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "银行卡添加成功",
  "data": {
    "id": 3002,
    "shopId": 1001,
    "bankName": "中国建设银行",
    "bankAccount": "6227001234567890123",
    "accountName": "李四",
    "idCard": "110101199002021234",
    "mobile": "***********",
    "isDefault": false,
    "status": 1,
    "statusName": "正常",
    "createTime": "2024-01-01T12:00:00"
  }
}
```

### 6. 更新银行卡

**接口**: `PUT /shop/finance/bank-cards/{cardId}`

**描述**: 更新门店银行卡信息

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述     |
|-------|------|-----|----------|
| cardId| Long | 是   | 银行卡ID |

**请求参数**: 同添加银行卡

**响应示例**:

```json
{
  "code": 200,
  "message": "银行卡更新成功",
  "data": {
    "id": 3002,
    "shopId": 1001,
    "bankName": "中国建设银行",
    "bankAccount": "6227001234567890123",
    "accountName": "李四",
    "isDefault": true,
    "status": 1,
    "updateTime": "2024-01-01T13:00:00"
  }
}
```

### 7. 删除银行卡

**接口**: `DELETE /shop/finance/bank-cards/{cardId}`

**描述**: 删除门店银行卡

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述     |
|-------|------|-----|----------|
| cardId| Long | 是   | 银行卡ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "银行卡删除成功",
  "data": true
}
```

### 8. 申请提现

**接口**: `POST /shop/finance/withdraw`

**描述**: 提交门店提现申请

**权限**: `shop:finance:withdraw`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 必填 | 描述       |
|-----------|---------|-----|------------|
| amount    | Decimal | 是   | 提现金额   |
| bankCardId| Long    | 是   | 银行卡ID   |
| password  | String  | 是   | 提现密码   |
| remark    | String  | 否   | 备注       |

**请求示例**:

```json
{
  "amount": 1000.00,
  "bankCardId": 3001,
  "password": "123456",
  "remark": "日常提现"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "提现申请提交成功",
  "data": {
    "withdrawId": 40001,
    "withdrawNo": "WD20240101001",
    "amount": 1000.00,
    "fee": 1.00,
    "actualAmount": 999.00,
    "status": 0,
    "statusName": "待审核",
    "bankCard": {
      "bankName": "中国工商银行",
      "bankAccount": "****7890",
      "accountName": "张三"
    },
    "createTime": "2024-01-01T14:00:00",
    "expectedArrivalTime": "2024-01-02T14:00:00"
  }
}
```

### 9. 获取提现记录

**接口**: `GET /shop/finance/withdraw-records`

**描述**: 分页查询门店提现记录

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名  | 类型    | 必填 | 默认值 | 描述                                    |
|--------|---------|-----|-------|-----------------------------------------|
| current| Integer | 否   | 1     | 当前页                                  |
| size   | Integer | 否   | 10    | 每页大小                                |
| status | Integer | 否   | -     | 提现状态：0-待审核 1-审核通过 2-已到账 3-已拒绝 |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 40001,
        "withdrawNo": "WD20240101001",
        "amount": 1000.00,
        "fee": 1.00,
        "actualAmount": 999.00,
        "status": 2,
        "statusName": "已到账",
        "bankCard": {
          "bankName": "中国工商银行",
          "bankAccount": "****7890",
          "accountName": "张三"
        },
        "remark": "日常提现",
        "createTime": "2024-01-01T14:00:00",
        "approveTime": "2024-01-01T15:00:00",
        "arrivalTime": "2024-01-02T10:00:00"
      }
    ],
    "total": 25,
    "size": 10,
    "current": 1,
    "pages": 3
  }
}
```

## 错误码说明

| 错误码 | 描述           | 解决方案                    |
|-------|----------------|----------------------------|
| 40001 | 参数错误       | 检查请求参数格式和必填项    |
| 40101 | 未认证         | 重新登录获取有效token       |
| 40102 | token过期      | 重新登录获取新token         |
| 40301 | 权限不足       | 联系管理员分配相应权限      |
| 40401 | 资源不存在     | 检查请求的资源ID是否正确    |
| 50001 | 服务器内部错误 | 联系技术支持               |
| 50002 | 数据库连接失败 | 稍后重试或联系技术支持      |

## 注意事项

1. **认证要求**: 除登录接口外，所有接口都需要在请求头中携带有效的token
2. **权限控制**: 门店管理员只能访问自己门店的数据，无法跨门店操作
3. **数据权限**: 所有查询接口都会自动过滤，只返回当前门店的数据
4. **操作限制**: 门店管理员只有查看和基础管理权限，无法进行设备绑定、订单创建等高级操作
5. **审计日志**: 所有操作都会记录审计日志，便于追踪和管理

## 联系支持

如有问题，请联系技术支持：
- 邮箱: <EMAIL>
- 电话: 400-123-4567
