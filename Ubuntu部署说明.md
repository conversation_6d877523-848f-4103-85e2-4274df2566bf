# 今夜城堡项目 Ubuntu 部署说明

## 📋 系统要求

- **操作系统**: Ubuntu 20.04 LTS 或更高版本
- **内存**: 最低 2GB，推荐 4GB 或更多
- **磁盘空间**: 最低 10GB 可用空间
- **网络**: 需要互联网连接用于下载依赖

## 🚀 快速部署

### 1. 下载部署文件

```bash
# 克隆项目或下载部署文件
git clone <your-repository-url>
cd jycb-z

# 或者直接下载部署脚本
wget https://your-domain.com/deploy/start-jycb-z.sh
wget https://your-domain.com/deploy/install-jycb-z.sh
wget https://your-domain.com/deploy/jycb-z.service
```

### 2. 一键安装

```bash
# 给脚本执行权限
chmod +x install-jycb-z.sh

# 运行安装脚本（需要root权限）
sudo ./install-jycb-z.sh
```

### 3. 启动应用

```bash
# 使用systemd服务启动
sudo systemctl start jycb-z

# 或使用启动脚本
cd /opt/jycb-z
sudo -u jycb ./start-jycb-z.sh start
```

## 📁 文件说明

### 1. start-jycb-z.sh
**主要启动脚本**，包含以下功能：
- 环境检查（Java、MySQL、Redis）
- 应用启动/停止/重启
- 状态监控
- 日志查看

```bash
# 使用方法
./start-jycb-z.sh {start|stop|restart|status|logs|help}
```

### 2. install-jycb-z.sh
**一键安装脚本**，自动完成：
- 系统依赖安装（Java 17、MySQL、Redis）
- 用户和目录创建
- 应用部署
- systemd服务配置
- 防火墙设置

### 3. jycb-z.service
**systemd服务配置文件**，用于：
- 系统服务管理
- 开机自启动
- 服务依赖管理
- 资源限制

## 🔧 手动安装步骤

如果需要手动安装，请按以下步骤操作：

### 1. 安装Java 17

```bash
sudo apt update
sudo apt install -y openjdk-17-jdk

# 验证安装
java -version
```

### 2. 安装MySQL

```bash
sudo apt install -y mysql-server

# 启动服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库
mysql -u root -p
CREATE DATABASE jycb_z CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 安装Redis

```bash
sudo apt install -y redis-server

# 启动服务
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 4. 创建应用用户

```bash
sudo useradd -r -m -s /bin/bash -d /opt/jycb-z jycb
sudo mkdir -p /var/log/jycb-z
sudo chown -R jycb:jycb /opt/jycb-z /var/log/jycb-z
```

### 5. 部署应用

```bash
# 复制文件到应用目录
sudo cp jycb-z-*.jar /opt/jycb-z/
sudo cp start-jycb-z.sh /opt/jycb-z/
sudo cp application.yml /opt/jycb-z/

# 设置权限
sudo chown -R jycb:jycb /opt/jycb-z/
sudo chmod +x /opt/jycb-z/start-jycb-z.sh
```

### 6. 配置systemd服务

```bash
# 复制服务文件
sudo cp jycb-z.service /etc/systemd/system/

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable jycb-z
```

## 🎛️ 服务管理

### systemd 命令

```bash
# 启动服务
sudo systemctl start jycb-z

# 停止服务
sudo systemctl stop jycb-z

# 重启服务
sudo systemctl restart jycb-z

# 查看状态
sudo systemctl status jycb-z

# 查看日志
sudo journalctl -u jycb-z -f

# 开机自启
sudo systemctl enable jycb-z

# 禁用自启
sudo systemctl disable jycb-z
```

### 启动脚本命令

```bash
cd /opt/jycb-z

# 启动应用
sudo -u jycb ./start-jycb-z.sh start

# 停止应用
sudo -u jycb ./start-jycb-z.sh stop

# 重启应用
sudo -u jycb ./start-jycb-z.sh restart

# 查看状态
sudo -u jycb ./start-jycb-z.sh status

# 查看日志
sudo -u jycb ./start-jycb-z.sh logs
```

## 📊 监控和日志

### 日志文件位置

```bash
# 应用日志
/var/log/jycb-z/application.log

# systemd日志
sudo journalctl -u jycb-z

# 实时查看日志
tail -f /var/log/jycb-z/application.log
```

### 性能监控

```bash
# 查看进程状态
ps aux | grep jycb-z

# 查看内存使用
free -h

# 查看磁盘使用
df -h

# 查看端口占用
netstat -tuln | grep 8080
```

## 🔧 配置文件

### application.yml 配置示例

```yaml
server:
  port: 8080

spring:
  profiles:
    active: prod
  datasource:
    url: ********************************************************************************************************************
    username: root
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
      database: 0

logging:
  file:
    path: /var/log/jycb-z
  level:
    com.jycb.jycbz: INFO
```

## 🛠️ 故障排除

### 常见问题

1. **端口被占用**
```bash
# 查看端口占用
sudo netstat -tuln | grep 8080
# 杀死占用进程
sudo kill -9 <PID>
```

2. **Java版本问题**
```bash
# 检查Java版本
java -version
# 设置JAVA_HOME
export JAVA_HOME=/usr/lib/jvm/java-17-openjdk-amd64
```

3. **数据库连接失败**
```bash
# 检查MySQL状态
sudo systemctl status mysql
# 重启MySQL
sudo systemctl restart mysql
```

4. **Redis连接失败**
```bash
# 检查Redis状态
sudo systemctl status redis-server
# 重启Redis
sudo systemctl restart redis-server
```

### 日志分析

```bash
# 查看错误日志
grep -i error /var/log/jycb-z/application.log

# 查看启动日志
grep -i "started" /var/log/jycb-z/application.log

# 查看最近的日志
tail -100 /var/log/jycb-z/application.log
```

## 🔒 安全建议

1. **防火墙配置**
```bash
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 8080/tcp
```

2. **用户权限**
- 使用专用用户运行应用
- 避免使用root用户

3. **数据库安全**
```bash
sudo mysql_secure_installation
```

4. **定期更新**
```bash
sudo apt update && sudo apt upgrade
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件
2. 检查系统资源
3. 验证配置文件
4. 联系技术支持团队
