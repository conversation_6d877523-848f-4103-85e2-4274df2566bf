# JYCB项目功能文档

## 1. 项目概述

聚优成本(JYCB)是一个综合性的设备管理与财务结算系统，主要用于管理设备全生命周期、处理订单、财务结算等功能。系统支持多种角色（系统管理员、业务主体、合作商、门店）的业务需求。

**特别说明**：设备本身不联网，用户使用时通过小程序上传设备状态，系统通过设备UUID进行校验、绑定和归属管理。用户使用流程在小程序端完成，包括支付和开锁逻辑。同一设备可以被多个用户同时付款开锁使用，没有设备使用唯一性限制。

## 2. 用户模块

### 2.1 用户体系

系统支持多种用户角色：
- **普通用户**：通过微信小程序登录的终端用户
- **门店管理员**：负责门店日常运营管理
- **合作商/直签门店**：负责区域内设备和门店管理
- **业务主体**：整体业务管理者
- **系统管理员**：系统最高权限管理者

### 2.2 用户认证

#### 2.2.1 微信小程序登录
- 通过微信小程序code进行登录认证
- 无需手机号注册，直接使用微信授权登录
- 返回token用于后续接口认证

#### 2.2.2 账号密码登录
- 支持合作商/直签门店登录
- 支持门店管理员登录
- 支持业务主体登录
- 支持系统管理员登录

#### 2.2.3 用户信息管理
- 获取当前登录用户信息
- 查看用户历史订单
- 查看用户历史消费记录

## 3. 设备模块

### 3.1 设备管理

#### 3.1.1 设备信息
- 设备基本信息管理（设备ID、设备编号、设备名称等）
- 设备归属管理（所属业务主体、合作商、门店）
- 设备绑定码/二维码/开门码管理

#### 3.1.2 设备状态管理
- 设备状态监控（正常、维护中、故障）
- 设备在线状态管理（离线、在线）
- 设备电量管理
- 设备位置信息管理（经纬度、地址）

#### 3.1.3 设备校验与绑定
- 通过设备MAC地址校验设备绑定状态
- 设备状态数据上传
- 设备绑定与解绑操作

### 3.2 设备使用流程

1. 用户扫描设备二维码
2. 系统校验设备状态和绑定情况
3. 用户创建使用订单
4. 用户支付订单
5. 系统发送开锁指令（通过小程序）
6. 用户使用设备
7. 用户结束使用，关闭订单

**注意**：设备本身不联网，所有操作通过小程序与服务端通信，再由小程序控制设备。

## 4. 订单模块

### 4.1 订单管理

#### 4.1.1 订单创建
- 用户选择设备后创建订单
- 系统生成唯一订单号
- 记录订单基本信息（用户ID、设备ID、创建时间等）

#### 4.1.2 订单支付
- 支持微信支付
- 支付成功后更新订单状态
- 支付成功后允许开锁使用设备

#### 4.1.3 订单状态管理
- 未支付状态
- 已支付状态（使用中）
- 已完成状态
- 已取消状态

#### 4.1.4 订单查询
- 获取设备当前订单信息
- 根据订单ID查询订单详情
- 查询用户历史订单列表

### 4.2 订单结算

- 用户结束使用时计算最终费用
- 根据使用时长和计费规则生成最终账单
- 支持订单退款处理

## 5. 财务模块

### 5.1 财务账户管理

- 系统账户管理
- 业务主体账户管理
- 合作商账户管理
- 门店账户管理

### 5.2 佣金管理

#### 5.2.1 佣金配置
- 设置各级分成比例
- 支持不同业务场景的佣金规则

#### 5.2.2 佣金明细
- 记录每笔订单的分成明细
- 支持佣金查询和统计

### 5.3 结算管理

#### 5.3.1 结算配置
- 设置结算周期（日结、周结、月结）
- 设置最小结算金额
- 支持不同级别的结算规则

#### 5.3.2 财务流水
- 记录所有财务相关操作
- 支持财务流水查询和统计

### 5.4 提现管理

- 提现申请处理
- 提现审核流程
- 提现记录管理

## 6. 系统管理模块

### 6.1 权限管理

- 角色管理
- 权限分配
- 菜单权限控制

### 6.2 日志管理

- 操作日志记录
- 审计日志记录
- 设备日志记录

### 6.3 系统配置

- 基础参数配置
- 业务规则配置
- 系统通知配置

## 7. 业务流程

### 7.1 用户使用流程

1. **设备发现**：用户通过小程序扫描设备二维码
2. **设备校验**：系统校验设备UUID和状态
3. **创建订单**：用户创建设备使用订单
4. **支付订单**：用户完成支付
5. **开锁使用**：支付成功后，小程序发送开锁指令
6. **使用中**：用户使用设备，系统记录使用状态
7. **结束使用**：用户通过小程序结束使用，系统计费并关闭订单

### 7.2 多用户并发使用

- 同一设备支持多个用户同时付款开锁使用
- 每个用户创建独立订单，互不影响
- 系统根据每个用户的实际使用情况单独计费

### 7.3 佣金分成流程

1. 用户完成支付，系统记录订单金额
2. 系统根据配置的分成比例，计算各级分成金额
3. 系统将分成金额分别计入各级账户
4. 根据结算配置，定期进行结算

## 8. 技术特点

1. **离线设备管理**：设备本身不联网，所有操作通过小程序中转
2. **UUID设备校验**：通过设备唯一标识进行身份验证
3. **多用户并发使用**：支持同一设备多用户同时使用
4. **灵活的分成机制**：支持多级分成，满足复杂业务需求
5. **完整的财务体系**：从订单创建到最终结算的完整财务闭环

## 9. 安全措施

1. **身份认证**：基于JWT的认证机制
2. **权限控制**：RBAC权限模型
3. **数据加密**：敏感数据加密存储
4. **接口防护**：防SQL注入、XSS攻击、CSRF攻击
5. **日志审计**：操作日志、登录日志记录 