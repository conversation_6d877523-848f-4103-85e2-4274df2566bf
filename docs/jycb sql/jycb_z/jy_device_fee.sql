-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_device_fee 结构
CREATE TABLE IF NOT EXISTS `jy_device_fee` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `fee_name` varchar(50) NOT NULL COMMENT '费用名称',
  `fee_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '费用类型：1-按时间 2-按次数 3-包天',
  `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '门店ID',
  `device_id` int(11) DEFAULT NULL COMMENT '设备ID，为空表示适用于所有设备',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `unit` varchar(10) NOT NULL DEFAULT '分钟' COMMENT '单位：分钟、次、天',
  `min_time` int(11) DEFAULT '0' COMMENT '最小使用时间（分钟）',
  `max_time` int(11) DEFAULT '0' COMMENT '最大使用时间（分钟），0表示不限制',
  `discount_type` tinyint(4) DEFAULT '0' COMMENT '优惠类型：0-无优惠 1-折扣 2-满减',
  `discount_value` decimal(10,2) DEFAULT '0.00' COMMENT '优惠值：折扣为0-1之间小数，满减为具体金额',
  `discount_condition` decimal(10,2) DEFAULT '0.00' COMMENT '优惠条件：满减条件金额',
  `start_time` datetime DEFAULT NULL COMMENT '生效开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '生效结束时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认：1-是 0-否',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_fee_type` (`fee_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备使用费配置表';

-- 正在导出表  jycb_z.jy_device_fee 的数据：~11 rows (大约)
INSERT INTO `jy_device_fee` (`id`, `fee_name`, `fee_type`, `entity_id`, `partner_id`, `shop_id`, `device_id`, `price`, `unit`, `min_time`, `max_time`, `discount_type`, `discount_value`, `discount_condition`, `start_time`, `end_time`, `status`, `is_default`, `remark`, `create_time`, `update_time`) VALUES
	(1, '标准按次收费', 1, NULL, NULL, NULL, NULL, 0.01, '次', 0, 120, 0, 0.00, 0.00, NULL, NULL, 1, 0, '标准按分钟收费', '2025-06-22 01:37:34', '2025-07-23 14:58:00'),
	(2, '包天使用', 3, NULL, NULL, NULL, NULL, 0.01, '天', 0, 1440, 0, 0.00, 0.00, NULL, NULL, 1, 1, '包天使用费', '2025-06-22 01:37:34', '2025-07-23 14:58:00'),
	(3, '会员按次优惠价', 1, NULL, NULL, NULL, NULL, 0.01, '次', 0, 120, 1, 0.80, 0.00, NULL, NULL, 1, 0, '会员8折优惠', '2025-06-22 01:37:34', '2025-07-23 14:58:00'),
	(5, '设备2标准收费', 1, 2, 1, 1, 2, 0.01, '次', 0, 120, 0, 0.00, 0.00, NULL, NULL, 1, 0, NULL, '2025-06-30 05:44:00', '2025-07-23 14:58:00'),
	(8, '设备5按次收费', 1, 2, 1, 1, 5, 0.01, '次', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, NULL, '2025-07-09 05:37:06', '2025-07-23 14:58:00'),
	(9, '设备6按次收费', 1, 2, 1, 1, 6, 0.01, '次', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, NULL, '2025-07-09 05:37:06', '2025-07-23 14:58:00'),
	(10, '设备7按天收费', 3, 2, 1, 1, 7, 0.01, '天', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, '设备7专用包天费率 - 98元/天', '2025-07-09 05:37:06', '2025-07-23 14:58:00'),
	(11, '设备8按次收费', 1, 2, 1, 1, 8, 0.01, '次', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, NULL, '2025-07-09 05:37:06', '2025-07-23 14:58:00'),
	(12, '设备9按次收费', 1, 2, 1, 1, 9, 0.01, '次', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, NULL, '2025-07-09 05:37:06', '2025-07-23 14:58:00'),
	(13, '设备10按次收费', 1, 2, 1, 1, 10, 0.01, '次', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, NULL, '2025-07-09 05:37:06', '2025-07-23 14:58:00'),
	(14, '设备11按次收费', 1, 2, 1, 1, 11, 0.01, '次', 0, 0, 0, 0.00, 0.00, NULL, NULL, 1, 1, NULL, '2025-07-09 05:37:06', '2025-07-23 14:58:00');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
