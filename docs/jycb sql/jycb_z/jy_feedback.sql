-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_feedback 结构
CREATE TABLE IF NOT EXISTS `jy_feedback` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '反馈ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `feedback_type` varchar(20) NOT NULL DEFAULT 'user' COMMENT '反馈类型：user-用户反馈 shop-门店反馈 partner-合作商反馈',
  `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '门店ID',
  `device_id` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID',
  `issue_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '问题类型：1-设备问题 2-订单问题 3-其他问题',
  `content` text NOT NULL COMMENT '反馈内容',
  `images` varchar(1000) DEFAULT NULL COMMENT '图片URL列表，以逗号分隔',
  `contact_info` varchar(100) DEFAULT NULL COMMENT '联系方式',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待处理 1-处理中 2-已处理 3-已关闭',
  `handler_id` bigint(20) DEFAULT NULL COMMENT '处理人ID',
  `handler_name` varchar(50) DEFAULT NULL COMMENT '处理人姓名',
  `result` text COMMENT '处理结果',
  `handle_time` datetime DEFAULT NULL COMMENT '处理时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_feedback_type` (`feedback_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一反馈表';

-- 正在导出表  jycb_z.jy_feedback 的数据：~5 rows (大约)
INSERT INTO `jy_feedback` (`id`, `user_id`, `feedback_type`, `entity_id`, `partner_id`, `shop_id`, `device_id`, `order_id`, `issue_type`, `content`, `images`, `contact_info`, `status`, `handler_id`, `handler_name`, `result`, `handle_time`, `create_time`, `update_time`) VALUES
	(1, 1, 'user', NULL, NULL, NULL, 0, '', 0, '1', '', '', 0, NULL, NULL, NULL, NULL, '2025-06-22 01:36:55', '2025-06-22 01:36:55'),
	(2, 1, 'user', NULL, NULL, NULL, 0, '', 0, '无极', '', '', 0, NULL, NULL, NULL, NULL, '2025-06-22 01:36:55', '2025-06-22 01:36:55'),
	(3, 5, 'user', NULL, NULL, NULL, 2, '53', 1, '问题类型：无法对话、机柜损坏\n\n订单状态：已完成\n\n13我去网上说的、】', NULL, NULL, 0, NULL, NULL, NULL, NULL, '2025-07-07 05:04:03', '2025-07-07 05:04:03'),
	(4, 5, 'user', NULL, NULL, NULL, 2, '53', 1, '问题类型：无法对话、机柜损坏\n\n订单状态：已完成\n\n13我去网上说的、】', NULL, NULL, 0, NULL, NULL, NULL, NULL, '2025-07-07 05:04:03', '2025-07-07 05:04:03'),
	(5, 5, 'user', NULL, NULL, NULL, 2, '53', 1, '问题类型：无法对话、机柜损坏\n\n订单状态：已完成\n\n13我去网上说的、】', NULL, NULL, 0, NULL, NULL, NULL, NULL, '2025-07-07 05:04:03', '2025-07-07 05:04:03');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
