-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_report_template 结构
CREATE TABLE IF NOT EXISTS `jy_report_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_type` varchar(20) NOT NULL COMMENT '模板类型：order-订单报表 finance-财务报表 device-设备报表 other-其他',
  `template_file` varchar(255) DEFAULT NULL COMMENT '模板文件路径',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `params_config` text COMMENT '参数配置JSON',
  `target_type` varchar(50) DEFAULT NULL COMMENT '适用对象：admin-管理员 entity-业务主体 partner-合作商 shop-门店 all-全部',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_code` (`template_code`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_target_type` (`target_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='报表模板表';

-- 正在导出表  jycb_z.jy_report_template 的数据：~3 rows (大约)
INSERT INTO `jy_report_template` (`id`, `template_name`, `template_code`, `template_type`, `template_file`, `description`, `params_config`, `target_type`, `status`, `create_time`, `update_time`) VALUES
	(1, '月度订单统计报表', 'monthly_order_report', 'order', '/templates/reports/monthly_order_report.xlsx', '按月统计订单数量和金额', '{"date_range":{"type":"date_range","required":true},"shop_id":{"type":"number","required":false}}', 'all', 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(2, '设备使用情况报表', 'device_usage_report', 'device', '/templates/reports/device_usage_report.xlsx', '统计设备使用频率和收益', '{"date_range":{"type":"date_range","required":true},"device_id":{"type":"number","required":false}}', 'all', 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(3, '财务收支明细报表', 'finance_detail_report', 'finance', '/templates/reports/finance_detail_report.xlsx', '详细记录财务收支情况', '{"date_range":{"type":"date_range","required":true},"account_type":{"type":"string","required":false}}', 'admin,entity,partner,shop', 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
