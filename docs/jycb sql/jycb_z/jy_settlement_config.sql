-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_settlement_config 结构
CREATE TABLE IF NOT EXISTS `jy_settlement_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` varchar(20) NOT NULL COMMENT '配置类型：system-系统配置，entity-业务主体配置，partner-合作商配置，shop-门店配置',
  `config_id` int(11) DEFAULT NULL COMMENT '配置对象ID（业务主体ID/合作商ID/门店ID，系统配置为NULL）',
  `parent_config_id` int(11) DEFAULT NULL COMMENT '父级配置ID（用于继承关系）',
  `settlement_type` varchar(20) NOT NULL DEFAULT 'daily' COMMENT '结算周期类型：daily-次日结算 weekly-周结算 monthly-月结算',
  `settlement_day` int(11) DEFAULT NULL COMMENT '结算日，当为周结算时表示星期几(1-7)，月结算时表示每月几号(1-31)',
  `min_settlement_amount` decimal(10,2) NOT NULL DEFAULT '100.00' COMMENT '最小结算金额，低于此金额不结算',
  `use_parent_config` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否使用父级配置：1-是 0-否',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_type_id` (`config_type`,`config_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_parent_config_id` (`parent_config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一结算配置表';

-- 正在导出表  jycb_z.jy_settlement_config 的数据：~0 rows (大约)
INSERT INTO `jy_settlement_config` (`id`, `config_type`, `config_id`, `parent_config_id`, `settlement_type`, `settlement_day`, `min_settlement_amount`, `use_parent_config`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
	(1, 'system', NULL, NULL, 'daily', NULL, 100.00, 0, 1, '系统默认结算配置-次日结算', 'system', '2025-06-22 01:35:12', NULL, '2025-06-22 01:35:12');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
