-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_device_transfer_record 结构
CREATE TABLE IF NOT EXISTS `jy_device_transfer_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `device_id` int(11) NOT NULL COMMENT '设备ID',
  `device_no` varchar(50) NOT NULL COMMENT '设备编号',
  `device_name` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `from_entity_id` int(11) DEFAULT NULL COMMENT '源业务主体ID',
  `from_partner_id` int(11) DEFAULT NULL COMMENT '源合作商ID',
  `from_shop_id` int(11) NOT NULL COMMENT '源门店ID',
  `from_shop_name` varchar(100) DEFAULT NULL COMMENT '源门店名称',
  `to_entity_id` int(11) DEFAULT NULL COMMENT '目标业务主体ID',
  `to_partner_id` int(11) DEFAULT NULL COMMENT '目标合作商ID',
  `to_shop_id` int(11) NOT NULL COMMENT '目标门店ID',
  `to_shop_name` varchar(100) DEFAULT NULL COMMENT '目标门店名称',
  `transfer_reason` varchar(500) DEFAULT NULL COMMENT '转移原因',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '转移状态：0-申请中 1-已完成 2-已取消',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `auditor_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_comment` varchar(500) DEFAULT NULL COMMENT '审核意见',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_from_shop_id` (`from_shop_id`),
  KEY `idx_to_shop_id` (`to_shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备转移记录表';

-- 正在导出表  jycb_z.jy_device_transfer_record 的数据：~1 rows (大约)
INSERT INTO `jy_device_transfer_record` (`id`, `device_id`, `device_no`, `device_name`, `from_entity_id`, `from_partner_id`, `from_shop_id`, `from_shop_name`, `to_entity_id`, `to_partner_id`, `to_shop_id`, `to_shop_name`, `transfer_reason`, `remark`, `status`, `operator_id`, `operator_name`, `auditor_id`, `auditor_name`, `audit_time`, `audit_comment`, `create_time`, `update_time`) VALUES
	(1, 2, 'JY20230002', '成人仿真娃娃', 2, 1, 1, '朝阳门店', 2, 1, 2, '海淀门店', '门店调整需要', '测试设备转移功能', 1, 1, '系统管理员', 1, '系统管理员', '2025-07-22 04:17:50', '审核通过，同意转移', '2025-07-22 04:17:12', '2025-07-22 04:17:50');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
