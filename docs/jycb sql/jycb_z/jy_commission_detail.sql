-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_commission_detail 结构
CREATE TABLE IF NOT EXISTS `jy_commission_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单编号',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `partner_id` bigint(20) NOT NULL COMMENT '合作商ID',
  `entity_id` bigint(20) NOT NULL COMMENT '业务主体ID',
  `order_amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `platform_amount` decimal(10,2) NOT NULL COMMENT '平台分成金额',
  `entity_amount` decimal(10,2) NOT NULL COMMENT '业务主体分成金额',
  `partner_amount` decimal(10,2) NOT NULL COMMENT '合作商分成金额',
  `shop_amount` decimal(10,2) NOT NULL COMMENT '门店分成金额',
  `platform_ratio` decimal(5,2) NOT NULL COMMENT '平台分成比例(%)',
  `entity_ratio` decimal(5,2) NOT NULL COMMENT '业务主体分成比例(%)',
  `partner_ratio` decimal(5,2) NOT NULL COMMENT '合作商分成比例(%)',
  `shop_ratio` decimal(5,2) NOT NULL COMMENT '门店分成比例(%)',
  `settlement_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '结算状态：0-未结算 1-已结算',
  `settlement_time` datetime DEFAULT NULL COMMENT '结算时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_settlement_status` (`settlement_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_commission_shop_time` (`shop_id`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一分成明细表';

-- 正在导出表  jycb_z.jy_commission_detail 的数据：~2 rows (大约)
INSERT INTO `jy_commission_detail` (`id`, `order_id`, `order_no`, `shop_id`, `partner_id`, `entity_id`, `order_amount`, `platform_amount`, `entity_amount`, `partner_amount`, `shop_amount`, `platform_ratio`, `entity_ratio`, `partner_ratio`, `shop_ratio`, `settlement_status`, `settlement_time`, `create_time`, `update_time`) VALUES
	(8, '121', 'OD17527353997260d0b20', 1, 1, 2, 98.00, 5.88, 3.92, 49.00, 39.20, 6.00, 4.00, 50.00, 40.00, 0, NULL, '2025-07-17 14:56:56', '2025-07-17 14:56:56'),
	(9, '122', 'TEST_ORDER_TODAY_001', 1, 1, 2, 50.00, 3.00, 2.00, 25.00, 20.00, 6.00, 4.00, 50.00, 40.00, 0, NULL, '2025-07-21 02:58:47', '2025-07-21 02:58:47');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
