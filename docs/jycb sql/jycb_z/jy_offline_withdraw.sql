-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_offline_withdraw 结构
CREATE TABLE IF NOT EXISTS `jy_offline_withdraw` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `withdraw_no` varchar(64) NOT NULL COMMENT '提现单号',
  `withdraw_type` varchar(20) NOT NULL COMMENT '提现类型：entity-业务主体 partner-合作商 shop-门店',
  `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '门店ID',
  `applicant_id` int(11) NOT NULL COMMENT '申请人ID',
  `applicant_name` varchar(50) NOT NULL COMMENT '申请人姓名',
  `amount` decimal(10,2) NOT NULL COMMENT '提现金额',
  `fee` decimal(10,2) DEFAULT '0.00' COMMENT '手续费',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实际到账金额',
  `bank_name` varchar(100) NOT NULL COMMENT '银行名称',
  `bank_account` varchar(50) NOT NULL COMMENT '银行账号',
  `account_name` varchar(50) NOT NULL COMMENT '开户人姓名',
  `withdraw_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-待审核 1-审核通过 2-已打款 3-已拒绝 4-已取消',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `audit_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `audit_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) DEFAULT NULL COMMENT '审核备注',
  `payment_id` int(11) DEFAULT NULL COMMENT '打款人ID',
  `payment_name` varchar(50) DEFAULT NULL COMMENT '打款人姓名',
  `payment_time` datetime DEFAULT NULL COMMENT '打款时间',
  `payment_remark` varchar(255) DEFAULT NULL COMMENT '打款备注',
  `payment_proof` varchar(255) DEFAULT NULL COMMENT '打款凭证',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_withdraw_type` (`withdraw_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_applicant_id` (`applicant_id`),
  KEY `idx_withdraw_status` (`withdraw_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='线下提现记录表';

-- 正在导出表  jycb_z.jy_offline_withdraw 的数据：~2 rows (大约)
INSERT INTO `jy_offline_withdraw` (`id`, `withdraw_no`, `withdraw_type`, `entity_id`, `partner_id`, `shop_id`, `applicant_id`, `applicant_name`, `amount`, `fee`, `actual_amount`, `bank_name`, `bank_account`, `account_name`, `withdraw_status`, `remark`, `audit_id`, `audit_name`, `audit_time`, `audit_remark`, `payment_id`, `payment_name`, `payment_time`, `payment_remark`, `payment_proof`, `create_time`, `update_time`) VALUES
	(1, 'WD202306010001', 'partner', NULL, 1, NULL, 1, '张经理', 5000.00, 0.00, 5000.00, '中国银行', '6222021234567890123', '张经理', 2, '合作商月度分成提现', 1, '管理员', '2025-06-22 01:36:12', '审核通过', 1, '管理员', '2025-06-22 01:36:12', '已打款', '转账凭证.jpg', '2025-06-22 01:36:12', '2025-06-22 01:36:12'),
	(2, 'WD202306010002', 'shop', NULL, NULL, 1, 2, '李店长', 2000.00, 0.00, 2000.00, '中国工商银行', '6222021234567890456', '李店长', 1, '门店月度分成提现', 1, '管理员', '2025-06-22 01:36:12', '审核通过', NULL, NULL, NULL, NULL, NULL, '2025-06-22 01:36:12', '2025-06-22 01:36:12');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
