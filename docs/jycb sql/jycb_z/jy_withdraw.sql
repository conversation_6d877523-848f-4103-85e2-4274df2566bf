-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_withdraw 结构
CREATE TABLE IF NOT EXISTS `jy_withdraw` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '提现ID',
  `withdraw_no` varchar(32) NOT NULL COMMENT '提现单号',
  `withdraw_type` varchar(20) NOT NULL COMMENT '提现类型：shop-门店 partner-合作商',
  `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '门店ID',
  `name` varchar(100) DEFAULT NULL COMMENT '申请方名称（门店名/合作商名）',
  `amount` decimal(12,2) NOT NULL COMMENT '提现金额',
  `actual_amount` decimal(12,2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
  `account_name` varchar(50) NOT NULL COMMENT '收款人姓名',
  `payee_name` varchar(50) DEFAULT NULL COMMENT '收款人姓名',
  `payee_account` varchar(50) DEFAULT NULL COMMENT '收款账号',
  `payee_bank` varchar(100) DEFAULT NULL COMMENT '收款银行',
  `bank_name` varchar(100) NOT NULL COMMENT '开户银行',
  `bank_account` varchar(50) NOT NULL COMMENT '银行账号',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态：0-申请中 1-已打款 2-已拒绝',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `process_time` datetime DEFAULT NULL COMMENT '处理时间',
  `process_by` varchar(50) DEFAULT NULL COMMENT '处理人',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `auditor_id` int(11) DEFAULT NULL COMMENT '审核人ID',
  `auditor_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `payment_no` varchar(50) DEFAULT NULL COMMENT '支付流水号',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_withdraw_no` (`withdraw_no`),
  KEY `idx_withdraw_type` (`withdraw_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一提现表';

-- 正在导出表  jycb_z.jy_withdraw 的数据：~0 rows (大约)
INSERT INTO `jy_withdraw` (`id`, `withdraw_no`, `withdraw_type`, `entity_id`, `partner_id`, `shop_id`, `name`, `amount`, `actual_amount`, `account_name`, `payee_name`, `payee_account`, `payee_bank`, `bank_name`, `bank_account`, `contact_phone`, `status`, `apply_time`, `process_time`, `process_by`, `remark`, `auditor_id`, `auditor_name`, `audit_time`, `payment_time`, `payment_no`, `create_time`, `update_time`) VALUES
	(1, 'WD20250719001', 'shop', NULL, NULL, 1, '测试门店', 50.00, 49.95, '测试门店', NULL, NULL, NULL, '中国银行', '6217000010001234567', NULL, 0, '2025-07-19 03:22:10', NULL, NULL, '测试提现申请', NULL, NULL, NULL, NULL, NULL, '2025-07-19 03:22:10', '2025-07-19 03:22:10');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
