-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_finance_account 结构
CREATE TABLE IF NOT EXISTS `jy_finance_account` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `account_type` varchar(20) NOT NULL COMMENT '账户类型：system-系统 entity-业务主体 partner-合作商 shop-门店 user-用户',
  `account_id` bigint(20) NOT NULL COMMENT '账户所属ID',
  `entity_id` bigint(20) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
  `total_revenue` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总收入金额',
  `available_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
  `frozen_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
  `total_withdraw` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '已提现总额',
  `device_fee` decimal(15,2) DEFAULT '0.00' COMMENT '设备费',
  `system_fee` decimal(15,2) DEFAULT '0.00' COMMENT '系统使用费',
  `last_settlement_time` datetime DEFAULT NULL COMMENT '最后结算时间',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常 0-冻结',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号，用于乐观锁',
  `withdraw_password` varchar(255) DEFAULT NULL COMMENT '提现密码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account` (`account_type`,`account_id`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_account_type_id` (`account_type`,`account_id`),
  KEY `idx_balance` (`available_balance`),
  KEY `idx_status_type` (`status`,`account_type`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一财务账户表';

-- 正在导出表  jycb_z.jy_finance_account 的数据：~5 rows (大约)
INSERT INTO `jy_finance_account` (`id`, `account_type`, `account_id`, `entity_id`, `partner_id`, `shop_id`, `total_revenue`, `available_balance`, `frozen_balance`, `total_withdraw`, `device_fee`, `system_fee`, `last_settlement_time`, `status`, `create_time`, `update_time`, `version`, `withdraw_password`) VALUES
	(1, 'system', 1, NULL, NULL, NULL, 17.56, 17.56, 0.00, 0.00, 0.00, 0.00, NULL, 1, '2025-06-22 01:36:48', '2025-07-24 23:38:52', 8, NULL),
	(2, 'platform', 1, NULL, NULL, NULL, 0.20, 0.20, 0.00, 0.00, 0.00, 0.00, NULL, 1, '2025-06-26 15:42:06', '2025-07-15 18:51:12', 0, NULL),
	(3, 'partner', 1, 2, 1, NULL, 148.00, 148.00, 0.00, 0.00, 0.00, 0.00, NULL, 1, '2025-07-17 13:29:52', '2025-07-24 23:38:52', 8, NULL),
	(4, 'entity', 2, 2, NULL, NULL, 11.84, 11.84, 0.00, 0.00, 0.00, 0.00, NULL, 1, '2025-07-17 14:26:48', '2025-07-24 23:38:52', 7, NULL),
	(5, 'shop', 1, 2, 1, 1, 118.40, 68.40, 50.00, 0.00, 0.00, 0.00, NULL, 1, '2025-07-17 14:26:48', '2025-07-24 23:38:52', 7, NULL);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
