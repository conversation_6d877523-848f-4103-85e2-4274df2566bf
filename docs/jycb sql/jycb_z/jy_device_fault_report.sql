-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_device_fault_report 结构
CREATE TABLE IF NOT EXISTS `jy_device_fault_report` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '故障报告ID',
  `fault_no` varchar(50) NOT NULL COMMENT '故障编号',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `fault_type` int(11) NOT NULL COMMENT '故障类型(1:无法开锁 2:无法关锁 3:电量异常 4:通信异常 5:其他)',
  `description` text COMMENT '故障描述',
  `images` json DEFAULT NULL COMMENT '故障图片URL数组',
  `urgency` int(11) DEFAULT '2' COMMENT '紧急程度(1:低 2:中 3:高)',
  `status` int(11) DEFAULT '0' COMMENT '处理状态(0:待处理 1:处理中 2:已解决 3:已关闭)',
  `report_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '上报时间',
  `assign_time` datetime DEFAULT NULL COMMENT '分配时间',
  `assignee` varchar(100) DEFAULT NULL COMMENT '处理人员',
  `estimated_fix_time` datetime DEFAULT NULL COMMENT '预计修复时间',
  `actual_fix_time` datetime DEFAULT NULL COMMENT '实际修复时间',
  `fix_description` text COMMENT '修复说明',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_fault_no` (`fault_no`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_fault_type` (`fault_type`),
  KEY `idx_report_time` (`report_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备故障报告表';

-- 正在导出表  jycb_z.jy_device_fault_report 的数据：~0 rows (大约)

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
