-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_commission_config 结构
CREATE TABLE IF NOT EXISTS `jy_commission_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_type` varchar(20) NOT NULL COMMENT '配置类型：system-系统配置，entity-业务主体配置，partner-合作商配置，shop-门店配置',
  `config_id` int(11) DEFAULT NULL COMMENT '配置对象ID（业务主体ID/合作商ID/门店ID，系统配置为NULL）',
  `parent_config_id` int(11) DEFAULT NULL COMMENT '父级配置ID（用于继承关系）',
  `platform_ratio` decimal(5,2) NOT NULL DEFAULT '5.00' COMMENT '平台分成比例(%)',
  `entity_ratio` decimal(5,2) NOT NULL DEFAULT '5.00' COMMENT '业务主体分成比例(%)',
  `partner_ratio` decimal(5,2) NOT NULL DEFAULT '50.00' COMMENT '合作商分成比例(%)',
  `shop_ratio` decimal(5,2) NOT NULL DEFAULT '40.00' COMMENT '门店分成比例(%)',
  `use_parent_config` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否使用父级配置：1-是 0-否',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` int(11) DEFAULT NULL COMMENT '创建管理员ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` int(11) DEFAULT NULL COMMENT '最后修改管理员ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `level` tinyint(4) NOT NULL DEFAULT '0' COMMENT '配置层级：0-系统 1-业务主体 2-合作商 3-门店',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_type_id` (`config_type`,`config_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_parent_config_id` (`parent_config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一分成配置表';

-- 正在导出表  jycb_z.jy_commission_config 的数据：~0 rows (大约)
INSERT INTO `jy_commission_config` (`id`, `config_type`, `config_id`, `parent_config_id`, `platform_ratio`, `entity_ratio`, `partner_ratio`, `shop_ratio`, `use_parent_config`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `level`) VALUES
	(1, 'system', NULL, NULL, 6.00, 4.00, 50.00, 40.00, 0, 1, '系统默认分成配置 - 已调整为：平台6%，业务主体4%，合作商50%，门店40%', 1, '2025-06-22 01:37:48', 1, '2025-07-17 14:19:43', 0);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
