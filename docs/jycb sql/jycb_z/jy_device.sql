-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_device 结构
CREATE TABLE IF NOT EXISTS `jy_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `device_no` varchar(50) NOT NULL COMMENT '设备编号',
  `device_name` varchar(50) DEFAULT '成人仿真娃娃' COMMENT '设备名称',
  `entity_id` bigint(20) DEFAULT NULL COMMENT '所属业务主体ID',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '所属合作商ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '所属门店ID',
  `bind_code` varchar(100) NOT NULL COMMENT '绑定码/二维码/开门码',
  `mac_address` varchar(50) DEFAULT NULL COMMENT 'MAC地址',
  `is_bound` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已绑定：0-未绑定 1-已绑定',
  `status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '设备状态：1-正常 2-维护中 3-故障',
  `online_status` tinyint(1) DEFAULT '0' COMMENT '在线状态：0-离线 1-在线',
  `in_use` tinyint(1) DEFAULT '0' COMMENT '是否使用中：0-未使用 1-使用中',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '纬度',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '经度',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `region_id` bigint(20) DEFAULT NULL COMMENT '区域ID',
  `bind_time` datetime DEFAULT NULL COMMENT '绑定时间',
  `last_online_time` datetime DEFAULT NULL COMMENT '最后在线时间',
  `last_location_time` datetime DEFAULT NULL COMMENT '最后定位时间',
  `activate_time` datetime DEFAULT NULL COMMENT '激活时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `sales_id` int(11) DEFAULT NULL COMMENT '归属员工ID',
  `sales_name` varchar(50) DEFAULT NULL COMMENT '归属员工姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `battery_level` int(11) DEFAULT '100' COMMENT '电池电量百分比',
  `current_users` int(11) NOT NULL DEFAULT '0' COMMENT '当前使用人数',
  `max_users` int(11) NOT NULL DEFAULT '1' COMMENT '最大可用人数',
  `device_type` int(2) DEFAULT '1' COMMENT '设备类型',
  `qrcode_url` varchar(255) DEFAULT NULL COMMENT '二维码URL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_device_no` (`device_no`),
  UNIQUE KEY `uk_bind_code` (`bind_code`),
  UNIQUE KEY `uk_mac_address` (`mac_address`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_online_status` (`online_status`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_device_shop_status_time` (`shop_id`,`status`,`create_time` DESC),
  KEY `idx_device_cover_basic` (`shop_id`,`status`,`id`,`device_no`,`device_name`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一设备表';

-- 正在导出表  jycb_z.jy_device 的数据：~11 rows (大约)
INSERT INTO `jy_device` (`id`, `device_no`, `device_name`, `entity_id`, `partner_id`, `shop_id`, `bind_code`, `mac_address`, `is_bound`, `status`, `online_status`, `in_use`, `latitude`, `longitude`, `address`, `province`, `city`, `district`, `region_id`, `bind_time`, `last_online_time`, `last_location_time`, `activate_time`, `remark`, `sales_id`, `sales_name`, `create_time`, `update_time`, `battery_level`, `current_users`, `max_users`, `device_type`, `qrcode_url`) VALUES
	(1, 'TEST001', '测试设备', 1, 1, 1, 'abcdef123456', 'AB:CD:EF:12:34:56', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-27 06:24:37', '2025-06-27 06:47:43', '2025-06-27 06:47:41', '2025-06-27 06:47:44', NULL, NULL, NULL, '2025-06-27 06:22:46', '2025-06-30 03:30:12', 100, 0, 33, 33, 'https://example.com?device=AB:CD:EF:12:34:56'),
	(2, 'JY20230002', '成人仿真娃娃', 2, 1, 2, 'f1a8af4b-552d-11f0-bf3d-08bfb8bb8b89', '4fdcd11a-8acb-76e8-dd10-02979607edaf', 1, 1, 1, 0, 0.000000, 0.000000, NULL, NULL, NULL, NULL, NULL, '2025-06-30 05:14:28', '2025-07-02 17:55:15', NULL, NULL, NULL, NULL, NULL, '2025-06-30 05:13:39', '2025-07-22 04:18:01', 0, 0, 3333, 333, NULL),
	(3, 'JY20230003', '成人仿真娃娃', 2, 1, 1, '8a0afd7c-5537-11f0-bf3d-08bfb8bb8b89', '2aba4802-caeb-dfeb-2127-44a5095bbe3e', 1, 1, 1, 0, 0.000000, 0.000000, NULL, NULL, NULL, NULL, NULL, '2025-06-30 06:24:30', '2025-07-02 17:55:15', NULL, NULL, NULL, NULL, NULL, '2025-06-30 06:22:20', '2025-07-02 17:55:15', 0, 0, 333, 333, 'https://example.com?device=2aba4802-caeb-dfeb-2127-44a5095bbe3e'),
	(4, 'JY20230004', '成人仿真娃娃', 2, 1, 1, 'a72fda88-b1a9-95f7-bea4-e2ffe5b550bf', 'a72fda88-b1a9-95f7-bea4-e2ffe5b550bf', 1, 1, 1, 0, 0.000000, 0.000000, NULL, NULL, NULL, NULL, NULL, '2025-07-02 17:55:13', '2025-07-02 17:55:14', NULL, NULL, NULL, NULL, NULL, '2025-07-02 06:32:23', '2025-07-02 17:55:14', 100, 0, 333, 333, NULL),
	(5, '20000', 'KSJAB5602000000', 2, 1, 1, '020000', 'AB:56:02:00:00:00', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-10 14:12:32', 100, 0, 999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020000'),
	(6, '20001', 'KSJAB5602000100', 2, 1, 1, '020001', 'AB:56:02:00:01:00', 1, 1, 0, 0, 36.672102, 116.917317, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-10 14:12:35', 93, 0, 999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020001'),
	(7, '20002', 'KSJAB5602000200', 2, 1, 1, '020002', 'AB:56:02:00:02:00', 1, 1, 0, 0, 30.453051, 104.095931, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-17 14:56:58', NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-15 02:35:21', 100, 0, 999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020002'),
	(8, '20003', 'KSJAB5602000300', 2, 1, 1, '020003', 'AB:56:02:00:03:00', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-10 14:12:38', 100, 0, 999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020003'),
	(9, '20004', 'KSJAB5602000400', 2, 1, 1, '020004', 'AB:56:02:00:04:00', 1, 1, 0, 0, 30.452843, 104.095825, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-10 14:12:42', 100, 0, 9999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020004'),
	(10, '20005', 'KSJAB5602000500', 2, 1, 1, '020005', 'AB:56:02:00:05:00', 1, 1, 0, 0, 30.452885, 104.095749, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-10 14:12:43', 100, 0, 999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020005'),
	(11, '20006', 'KSJAB5602000600', 2, 1, 1, '020006', 'AB:56:02:00:06:00', 1, 1, 0, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-07-09 05:36:39', '2025-07-10 14:12:45', 100, 0, 1999, 1, 'https://www.jycb888.com/qrcode/blue?jycb=1&id=020006');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
