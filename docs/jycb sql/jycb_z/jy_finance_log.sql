-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_finance_log 结构
CREATE TABLE IF NOT EXISTS `jy_finance_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '流水ID',
  `account_type` varchar(20) NOT NULL COMMENT '账户类型：system-系统 entity-业务主体 partner-合作商 shop-门店 user-用户',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `entity_id` bigint(20) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单号',
  `type` tinyint(2) NOT NULL COMMENT '流水类型：1-收入 2-提现 3-退款 4-系统调整',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `before_balance` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `after_balance` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account` (`account_type`,`account_id`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_type` (`type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_account_type_id_time` (`account_type`,`account_id`,`create_time`),
  KEY `idx_amount` (`amount`),
  KEY `idx_type_time` (`type`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一财务流水表';

-- 正在导出表  jycb_z.jy_finance_log 的数据：~19 rows (大约)
INSERT INTO `jy_finance_log` (`id`, `account_type`, `account_id`, `entity_id`, `partner_id`, `shop_id`, `user_id`, `order_id`, `type`, `amount`, `before_balance`, `after_balance`, `description`, `operator_id`, `operator_name`, `create_time`, `update_time`) VALUES
	(1, 'partner', 1, 2, 1, NULL, NULL, '117', 1, 0.01, 0.00, 0.01, '订单分成收入', NULL, '系统', '2025-07-17 13:29:52', '2025-07-17 13:29:52'),
	(2, 'partner', 1, 2, 1, NULL, NULL, '118', 1, 0.01, 0.01, 0.02, '订单分成收入', NULL, '系统', '2025-07-17 13:47:26', '2025-07-17 13:47:26'),
	(3, 'partner', 1, 2, 1, NULL, NULL, '119', 1, 0.01, 0.02, 0.03, '订单分成收入', NULL, '系统', '2025-07-17 14:09:58', '2025-07-17 14:09:58'),
	(4, 'system', 1, NULL, NULL, NULL, NULL, '120', 1, 6.00, 0.00, 6.00, '订单分成收入(已修正)', NULL, '系统', '2025-07-17 14:26:48', '2025-07-17 14:34:38'),
	(5, 'entity', 2, 2, NULL, NULL, NULL, '120', 1, 4.00, 0.00, 4.00, '订单分成收入(已修正)', NULL, '系统', '2025-07-17 14:26:48', '2025-07-17 14:34:54'),
	(6, 'partner', 1, 2, 1, NULL, NULL, '120', 1, 50.00, 0.03, 50.03, '订单分成收入', NULL, '系统', '2025-07-17 14:26:48', '2025-07-17 14:26:48'),
	(7, 'shop', 1, 2, 1, 1, NULL, '120', 1, 40.00, 0.00, 40.00, '订单分成收入', NULL, '系统', '2025-07-17 14:26:48', '2025-07-17 14:26:48'),
	(8, 'system', 1, NULL, NULL, NULL, NULL, '121', 1, 5.88, 6.00, 11.88, '订单分成收入', NULL, '系统', '2025-07-17 14:56:56', '2025-07-17 14:56:56'),
	(9, 'entity', 2, 2, NULL, NULL, NULL, '121', 1, 3.92, 4.00, 7.92, '订单分成收入', NULL, '系统', '2025-07-17 14:56:56', '2025-07-17 14:56:56'),
	(10, 'partner', 1, 2, 1, NULL, NULL, '121', 1, 49.00, 50.03, 99.03, '订单分成收入', NULL, '系统', '2025-07-17 14:56:56', '2025-07-17 14:56:56'),
	(11, 'shop', 1, 2, 1, 1, NULL, '121', 1, 39.20, 40.00, 79.20, '订单分成收入', NULL, '系统', '2025-07-17 14:56:56', '2025-07-17 14:56:56'),
	(28, 'system', 1, NULL, NULL, NULL, NULL, '121', 1, 5.88, 8.68, 14.56, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(29, 'entity', 2, NULL, NULL, NULL, NULL, '121', 1, 3.92, 5.92, 9.84, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(30, 'partner', 1, NULL, NULL, NULL, NULL, '121', 1, 49.00, 74.00, 123.00, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(31, 'shop', 1, NULL, NULL, NULL, NULL, '121', 1, 39.20, 9.20, 48.40, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(32, 'system', 1, NULL, NULL, NULL, NULL, '122', 1, 3.00, 14.56, 17.56, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(33, 'entity', 2, NULL, NULL, NULL, NULL, '122', 1, 2.00, 9.84, 11.84, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(34, 'partner', 1, NULL, NULL, NULL, NULL, '122', 1, 25.00, 123.00, 148.00, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(35, 'shop', 1, NULL, NULL, NULL, NULL, '122', 1, 20.00, 48.40, 68.40, '订单分成收入', NULL, '系统', '2025-07-24 23:38:52', '2025-07-24 23:38:52');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
