-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_clean_task 结构
CREATE TABLE IF NOT EXISTS `jy_clean_task` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `device_id` int(11) NOT NULL COMMENT '设备ID',
  `device_no` varchar(50) NOT NULL COMMENT '设备编号',
  `cleaner_id` int(11) DEFAULT NULL COMMENT '卫生员ID',
  `cleaner_name` varchar(50) DEFAULT NULL COMMENT '卫生员姓名',
  `task_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '任务类型：1-常规清洁 2-紧急清洁 3-投诉处理',
  `task_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '任务状态：0-待处理 1-处理中 2-已完成 3-已取消',
  `plan_time` datetime DEFAULT NULL COMMENT '计划清洁时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始清洁时间',
  `end_time` datetime DEFAULT NULL COMMENT '完成清洁时间',
  `duration` int(11) DEFAULT NULL COMMENT '清洁耗时（分钟）',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `images_before` varchar(1000) DEFAULT NULL COMMENT '清洁前图片，多张以逗号分隔',
  `images_after` varchar(1000) DEFAULT NULL COMMENT '清洁后图片，多张以逗号分隔',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_cleaner_id` (`cleaner_id`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_plan_time` (`plan_time`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备清洁任务表';

-- 正在导出表  jycb_z.jy_clean_task 的数据：~0 rows (大约)
INSERT INTO `jy_clean_task` (`id`, `device_id`, `device_no`, `cleaner_id`, `cleaner_name`, `task_type`, `task_status`, `plan_time`, `start_time`, `end_time`, `duration`, `remark`, `images_before`, `images_after`, `create_time`, `update_time`) VALUES
	(1, 1, 'DEV20230001', 1, '张三', 1, 2, '2025-06-22 01:37:54', '2025-06-22 01:37:54', '2025-06-22 01:37:54', 30, '常规清洁任务', NULL, NULL, '2025-06-22 01:37:54', '2025-06-22 01:37:54');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
