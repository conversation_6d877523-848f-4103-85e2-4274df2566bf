-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_order 结构
CREATE TABLE IF NOT EXISTS `jy_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(64) NOT NULL COMMENT '订单编号',
  `device_id` int(11) NOT NULL COMMENT '设备ID',
  `device_no` varchar(50) NOT NULL COMMENT '设备编号',
  `entity_id` bigint(20) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `order_status` tinyint(2) NOT NULL DEFAULT '1' COMMENT '订单状态：1-进行中 2-已完成 3-已取消 4-已退款',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `actual_amount` decimal(10,2) DEFAULT NULL COMMENT '实际收费金额',
  `duration` int(11) DEFAULT '0' COMMENT '使用时长(分钟)',
  `actual_duration` int(11) DEFAULT NULL COMMENT '实际使用时长(分钟)',
  `pay_status` tinyint(1) DEFAULT '0' COMMENT '支付状态：0-未支付 1-已支付',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `pay_type` varchar(20) DEFAULT NULL COMMENT '支付方式',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '支付交易号',
  `refund_status` tinyint(1) DEFAULT '0' COMMENT '退款状态：0-未退款 1-已退款',
  `refund_time` datetime DEFAULT NULL COMMENT '退款时间',
  `refund_amount` decimal(10,2) DEFAULT '0.00' COMMENT '退款金额',
  `refund_reason` varchar(255) DEFAULT NULL COMMENT '退款原因',
  `commission_status` tinyint(1) DEFAULT '0' COMMENT '分账状态：0-未分账 1-已分账',
  `commission_time` datetime DEFAULT NULL COMMENT '分账时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_device_no` (`device_no`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_status` (`order_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_pay_status` (`pay_status`),
  KEY `idx_commission_status` (`commission_status`),
  KEY `idx_order_commission_status` (`commission_status`),
  KEY `idx_order_shop_pay_time` (`shop_id`,`pay_status`,`create_time` DESC),
  KEY `idx_order_device_pay` (`device_id`,`pay_status`),
  KEY `idx_order_cover_basic` (`shop_id`,`pay_status`,`id`,`order_no`,`amount`,`actual_amount`,`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=123 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一订单表';

-- 正在导出表  jycb_z.jy_order 的数据：~2 rows (大约)
INSERT INTO `jy_order` (`id`, `order_no`, `device_id`, `device_no`, `entity_id`, `partner_id`, `shop_id`, `user_id`, `order_status`, `start_time`, `end_time`, `amount`, `actual_amount`, `duration`, `actual_duration`, `pay_status`, `pay_time`, `pay_type`, `transaction_id`, `refund_status`, `refund_time`, `refund_amount`, `refund_reason`, `commission_status`, `commission_time`, `remark`, `create_time`, `update_time`) VALUES
	(121, 'OD17527353997260d0b20', 7, '20002', 2, 1, 1, 6, 3, '2025-07-17 14:56:58', '2025-07-24 23:38:52', 98.00, NULL, 0, NULL, 1, '2025-07-17 14:56:58', 'WXPAY', '4200002739202507178596271047', 0, NULL, 0.00, NULL, 1, '2025-07-17 14:56:56', '系统自动完成超过12小时的订单', '2025-07-17 14:56:40', '2025-07-24 23:38:52'),
	(122, 'TEST_ORDER_TODAY_001', 1, 'DEV001', 2, 1, 1, 1, 3, '2025-07-21 02:58:11', '2025-07-24 23:38:52', 50.00, NULL, 0, NULL, 1, '2025-07-21 02:58:11', NULL, NULL, 0, NULL, 0.00, NULL, 1, NULL, NULL, '2025-07-21 02:58:11', '2025-07-24 23:38:52');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
