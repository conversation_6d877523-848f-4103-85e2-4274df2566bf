-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_entity 结构
CREATE TABLE IF NOT EXISTS `jy_entity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '业务主体ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父业务主体ID',
  `entity_name` varchar(100) NOT NULL COMMENT '业务主体名称',
  `entity_code` varchar(50) NOT NULL COMMENT '业务主体编码',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `revenue_ratio` decimal(10,2) DEFAULT '0.00' COMMENT '分成比例(%)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常 0-禁用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `admin_id` bigint(20) DEFAULT NULL COMMENT '关联管理员ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0-未删除，1-已删除',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_entity_code` (`entity_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一业务主体表';

-- 正在导出表  jycb_z.jy_entity 的数据：~2 rows (大约)
INSERT INTO `jy_entity` (`id`, `parent_id`, `entity_name`, `entity_code`, `contact_name`, `contact_phone`, `province`, `city`, `district`, `address`, `id_card`, `bank_name`, `bank_account`, `revenue_ratio`, `status`, `remark`, `admin_id`, `create_time`, `update_time`, `deleted`, `version`, `create_by`, `update_by`) VALUES
	(1, 0, '叁叁肆肆总后台', 'HEAD_3344', '管理员', '***********', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0.00, 1, NULL, 1, '2025-06-22 01:37:16', '2025-06-22 01:37:16', 0, 0, NULL, NULL),
	(2, 1, '北京业务主体', 'BJ_ENTITY', '张经理', '***********', '北京市', '北京市', '海淀区', '北京市海淀区中关村', NULL, NULL, NULL, 20.00, 1, NULL, 2, '2025-06-22 01:37:16', '2025-06-22 01:37:16', 0, 0, NULL, NULL);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
