-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_finance_transaction 结构
CREATE TABLE IF NOT EXISTS `jy_finance_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `account_type` varchar(50) NOT NULL COMMENT '账户类型：platform-平台账户，entity-业务主体账户，partner-合作商账户，shop-门店账户，user-用户账户，system-系统账户',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `transaction_type` varchar(50) NOT NULL COMMENT '交易类型：INCOME-收入，EXPENSE-支出，RECHARGE-充值，WITHDRAW-提现，REFUND-退款，FREEZE-冻结，UNFREEZE-解冻，SETTLEMENT-结算',
  `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
  `balance` decimal(15,2) NOT NULL COMMENT '交易后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` int(11) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_type_id` (`account_type`,`account_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_account_type_id_time` (`account_type`,`account_id`,`create_time`),
  KEY `idx_transaction_type_time` (`transaction_type`,`create_time`),
  KEY `idx_amount` (`amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='财务交易记录表';

-- 正在导出表  jycb_z.jy_finance_transaction 的数据：~0 rows (大约)

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
