-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_faq 结构
CREATE TABLE IF NOT EXISTS `jy_faq` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'FAQ ID',
  `category` varchar(50) NOT NULL COMMENT '分类：system-系统使用 device-设备相关 order-订单相关 finance-财务相关 other-其他',
  `question` varchar(255) NOT NULL COMMENT '问题',
  `answer` text NOT NULL COMMENT '回答',
  `target_type` varchar(50) DEFAULT NULL COMMENT '适用对象：admin-管理员 entity-业务主体 partner-合作商 shop-门店 user-用户 all-全部',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `view_count` int(11) DEFAULT '0' COMMENT '查看次数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-显示 0-隐藏',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_target_type` (`target_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='常见问题表';

-- 正在导出表  jycb_z.jy_faq 的数据：~4 rows (大约)
INSERT INTO `jy_faq` (`id`, `category`, `question`, `answer`, `target_type`, `sort`, `view_count`, `status`, `create_time`, `update_time`) VALUES
	(1, 'system', '如何登录系统？', '1. 打开浏览器访问系统登录地址\n2. 输入您的账号和密码\n3. 点击登录按钮即可进入系统', 'all', 1, 0, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(2, 'device', '设备无法上线怎么办？', '1. 检查设备电源是否接通\n2. 检查网络连接是否正常\n3. 检查设备是否已被激活\n4. 联系技术支持人员处理', 'shop', 2, 0, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(3, 'order', '如何查询订单记录？', '1. 登录系统后，点击左侧菜单"订单管理"\n2. 在订单列表页面，可以通过订单号、时间范围等条件筛选\n3. 点击订单记录可查看详情', 'shop,partner', 3, 0, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(4, 'finance', '如何申请提现？', '1. 登录系统后，点击左侧菜单"财务管理"-"提现申请"\n2. 填写提现金额和银行账户信息\n3. 提交申请后等待审核', 'shop,partner', 4, 0, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
