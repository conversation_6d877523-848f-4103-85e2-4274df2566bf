-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_cleaner 结构
CREATE TABLE IF NOT EXISTS `jy_cleaner` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '卫生员ID',
  `cleaner_name` varchar(50) NOT NULL COMMENT '卫生员姓名',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `gender` tinyint(1) DEFAULT '1' COMMENT '性别：1-男 2-女',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `entity_id` int(11) DEFAULT NULL COMMENT '所属业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '所属合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '所属门店ID',
  `region_id` int(11) DEFAULT NULL COMMENT '负责区域ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常 0-禁用',
  `work_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '工作状态：0-休息中 1-工作中',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `leave_date` date DEFAULT NULL COMMENT '离职日期',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_mobile` (`mobile`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_region_id` (`region_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='卫生员表';

-- 正在导出表  jycb_z.jy_cleaner 的数据：~2 rows (大约)
INSERT INTO `jy_cleaner` (`id`, `cleaner_name`, `mobile`, `gender`, `id_card`, `entity_id`, `partner_id`, `shop_id`, `region_id`, `status`, `work_status`, `entry_date`, `leave_date`, `remark`, `create_time`, `update_time`) VALUES
	(1, '张三', '13800000001', 1, NULL, 1, NULL, NULL, NULL, 1, 1, '2023-01-01', NULL, '负责北京区域设备清洁', '2025-06-22 01:37:54', '2025-06-22 01:37:54'),
	(2, '李四', '13800000002', 2, NULL, 1, NULL, NULL, NULL, 1, 0, '2023-01-15', NULL, '负责上海区域设备清洁', '2025-06-22 01:37:54', '2025-06-22 01:37:54');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
