-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_partner 结构
CREATE TABLE IF NOT EXISTS `jy_partner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '合作商ID',
  `entity_id` bigint(20) NOT NULL COMMENT '所属业务主体ID',
  `partner_name` varchar(100) NOT NULL COMMENT '合作商名称',
  `partner_code` varchar(50) NOT NULL COMMENT '合作商编码',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `district` varchar(50) DEFAULT NULL COMMENT '区县',
  `address` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `account_name` varchar(50) DEFAULT NULL COMMENT '收款人姓名',
  `revenue_ratio` decimal(10,2) DEFAULT '0.00' COMMENT '分成比例(%)',
  `device_fee` decimal(10,2) DEFAULT '0.00' COMMENT '设备费',
  `system_fee` decimal(10,2) DEFAULT '0.00' COMMENT '系统使用费',
  `device_count` int(11) DEFAULT '0' COMMENT '设备数量',
  `cooperation_time` datetime DEFAULT NULL COMMENT '合作时间',
  `salesperson` varchar(50) DEFAULT NULL COMMENT '业务员',
  `operator` varchar(50) DEFAULT NULL COMMENT '运营人员',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常 0-禁用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `admin_id` bigint(20) DEFAULT NULL COMMENT '关联管理员ID',
  `sales_id` bigint(20) DEFAULT NULL COMMENT '归属员工ID',
  `sales_name` varchar(50) DEFAULT NULL COMMENT '归属员工姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志，0-未删除，1-已删除',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_partner_code` (`partner_code`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_admin_id` (`admin_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一合作商表';

-- 正在导出表  jycb_z.jy_partner 的数据：~0 rows (大约)
INSERT INTO `jy_partner` (`id`, `entity_id`, `partner_name`, `partner_code`, `contact_name`, `contact_phone`, `province`, `city`, `district`, `address`, `id_card`, `bank_name`, `bank_account`, `account_name`, `revenue_ratio`, `device_fee`, `system_fee`, `device_count`, `cooperation_time`, `salesperson`, `operator`, `status`, `remark`, `admin_id`, `sales_id`, `sales_name`, `create_time`, `update_time`, `deleted`, `version`) VALUES
	(1, 2, '北京一号合作商', 'BJ_PARTNER_01', '李经理', '***********', '北京市', '北京市', '朝阳区', '北京市朝阳区国贸', NULL, NULL, NULL, NULL, 15.00, 0.00, 0.00, 0, NULL, NULL, NULL, 1, NULL, 3, NULL, NULL, '2025-06-22 01:35:38', '2025-06-22 01:35:38', 0, 0);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
