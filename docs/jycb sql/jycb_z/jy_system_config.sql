-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_system_config 结构
CREATE TABLE IF NOT EXISTS `jy_system_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `description` varchar(200) DEFAULT NULL COMMENT '配置描述',
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT '是否加密',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config` (`config_type`,`config_key`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统配置表';

-- 正在导出表  jycb_z.jy_system_config 的数据：~17 rows (大约)
INSERT INTO `jy_system_config` (`id`, `config_type`, `config_key`, `config_value`, `description`, `is_encrypted`, `create_time`, `update_time`) VALUES
	(1, 'app', 'name', '今夜城堡门店端', '应用名称', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(2, 'app', 'version', '1.0.0', '应用版本', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(3, 'app', 'api_version', 'v1.0', 'API版本', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(4, 'upload', 'max_size', '10485760', '上传文件最大大小(字节)', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(5, 'upload', 'image_types', 'jpg,jpeg,png,gif,bmp,webp', '支持的图片类型', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(6, 'upload', 'doc_types', 'pdf,doc,docx,xls,xlsx,txt', '支持的文档类型', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(7, 'finance', 'withdraw_min_amount', '100.00', '最小提现金额', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(8, 'finance', 'withdraw_max_amount', '10000.00', '最大提现金额', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(9, 'finance', 'withdraw_fee_rate', '0.001', '提现手续费率', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(10, 'finance', 'withdraw_daily_limit', '3', '每日提现次数限制', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(11, 'order', 'max_duration', '480', '最大使用时长(分钟)', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(12, 'order', 'min_duration', '30', '最小使用时长(分钟)', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(13, 'order', 'auto_complete_time', '10', '自动完成时间(分钟)', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(14, 'cos', 'region', 'ap-guangzhou', '腾讯云COS地域', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(15, 'cos', 'bucket_name', 'jycb-files', '腾讯云COS存储桶名称', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(16, 'cos', 'secret_id', '', '腾讯云COS SecretId（需要配置）', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50'),
	(17, 'cos', 'secret_key', '', '腾讯云COS SecretKey（需要配置）', 0, '2025-07-20 02:47:50', '2025-07-20 02:47:50');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
