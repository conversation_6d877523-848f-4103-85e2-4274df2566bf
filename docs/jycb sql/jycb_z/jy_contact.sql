-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_contact 结构
CREATE TABLE IF NOT EXISTS `jy_contact` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '联系人ID',
  `contact_name` varchar(50) NOT NULL COMMENT '联系人姓名',
  `contact_type` varchar(20) NOT NULL COMMENT '联系人类型：service-客服 technical-技术支持 business-商务合作 complaint-投诉建议 other-其他',
  `mobile` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '电子邮箱',
  `wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `qq` varchar(20) DEFAULT NULL COMMENT 'QQ号',
  `work_time` varchar(100) DEFAULT NULL COMMENT '工作时间',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-显示 0-隐藏',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_contact_type` (`contact_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联系人表';

-- 正在导出表  jycb_z.jy_contact 的数据：~3 rows (大约)
INSERT INTO `jy_contact` (`id`, `contact_name`, `contact_type`, `mobile`, `email`, `wechat`, `qq`, `work_time`, `description`, `sort`, `status`, `create_time`, `update_time`) VALUES
	(1, '客户服务中心', 'service', '************', '<EMAIL>', 'jycb_service', '12345678', '周一至周五 9:00-18:00', '提供系统使用咨询和问题解答', 1, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(2, '技术支持团队', 'technical', '************', '<EMAIL>', 'jycb_tech', '87654321', '周一至周日 8:00-22:00', '提供设备和系统技术支持', 2, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01'),
	(3, '商务合作部门', 'business', '************', '<EMAIL>', 'jycb_business', '11223344', '周一至周五 9:00-18:00', '负责商务合作洽谈', 3, 1, '2025-06-22 01:37:01', '2025-06-22 01:37:01');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
