-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_role 结构
CREATE TABLE IF NOT EXISTS `jy_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `role_type` varchar(20) NOT NULL COMMENT '角色类型：admin-系统管理员 entity-业务主体 partner-合作商 shop-门店',
  `description` varchar(255) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-正常 0-禁用',
  `entity_id` int(11) DEFAULT NULL COMMENT '所属业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '所属合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '所属门店ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_role_type` (`role_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色表';

-- 正在导出表  jycb_z.jy_role 的数据：~9 rows (大约)
INSERT INTO `jy_role` (`id`, `role_name`, `role_code`, `role_type`, `description`, `status`, `entity_id`, `partner_id`, `shop_id`, `create_time`, `update_time`) VALUES
	(1, '超级管理员', 'super_admin', 'admin', '系统超级管理员，拥有所有权限', 1, NULL, NULL, NULL, '2025-06-22 01:35:30', '2025-06-22 01:35:30'),
	(2, '业务主体管理员', 'entity_admin', 'entity', '业务主体管理员，管理业务主体下的所有资源', 1, NULL, NULL, NULL, '2025-06-22 01:35:30', '2025-06-22 01:35:30'),
	(3, '合作商管理员', 'partner_admin', 'partner', '合作商管理员，管理合作商下的所有资源', 1, NULL, NULL, NULL, '2025-06-22 01:35:30', '2025-06-22 01:35:30'),
	(4, '门店管理员', 'shop_admin', 'shop', '门店管理员，管理门店下的所有资源', 1, NULL, NULL, NULL, '2025-06-22 01:35:30', '2025-06-22 01:35:30'),
	(5, '财务管理员', 'finance_admin', 'admin', '财务管理员，管理财务相关资源', 1, NULL, NULL, NULL, '2025-06-26 07:35:56', '2025-06-26 07:35:56'),
	(6, '总后台财务管理员', 'system_finance_admin', 'admin', '总后台财务管理员，可查看所有财务数据', 1, NULL, NULL, NULL, '2025-07-18 03:07:53', '2025-07-18 03:07:53'),
	(7, '业务主体财务管理员', 'entity_finance_admin', 'entity', '业务主体财务管理员，只能查看本业务主体下级财务数据', 1, NULL, NULL, NULL, '2025-07-18 03:07:53', '2025-07-18 03:07:53'),
	(8, '合作商财务管理员', 'partner_finance_admin', 'partner', '合作商财务管理员，只能查看本合作商下级财务数据', 1, NULL, NULL, NULL, '2025-07-18 03:07:53', '2025-07-18 03:07:53'),
	(9, '门店财务管理员', 'shop_finance_admin', 'shop', '门店财务管理员，只能查看本门店财务数据', 1, NULL, NULL, NULL, '2025-07-18 03:07:53', '2025-07-18 03:07:53');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
