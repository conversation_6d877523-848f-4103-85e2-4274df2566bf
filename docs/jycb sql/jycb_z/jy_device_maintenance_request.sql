-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_device_maintenance_request 结构
CREATE TABLE IF NOT EXISTS `jy_device_maintenance_request` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '维护申请ID',
  `request_no` varchar(50) NOT NULL COMMENT '申请编号',
  `device_id` bigint(20) NOT NULL COMMENT '设备ID',
  `shop_id` bigint(20) NOT NULL COMMENT '门店ID',
  `maintenance_type` int(11) NOT NULL COMMENT '维护类型(1:定期保养 2:深度清洁 3:部件更换)',
  `description` text COMMENT '维护说明',
  `urgency` int(11) DEFAULT '2' COMMENT '紧急程度(1:低 2:中 3:高)',
  `preferred_time` datetime DEFAULT NULL COMMENT '期望维护时间',
  `status` int(11) DEFAULT '0' COMMENT '申请状态(0:待安排 1:已安排 2:进行中 3:已完成 4:已取消)',
  `assign_time` datetime DEFAULT NULL COMMENT '安排时间',
  `assignee` varchar(100) DEFAULT NULL COMMENT '维护人员',
  `scheduled_time` datetime DEFAULT NULL COMMENT '计划维护时间',
  `actual_start_time` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_time` datetime DEFAULT NULL COMMENT '实际结束时间',
  `maintenance_result` text COMMENT '维护结果',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_no` (`request_no`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`),
  KEY `idx_maintenance_type` (`maintenance_type`),
  KEY `idx_preferred_time` (`preferred_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='设备维护申请表';

-- 正在导出表  jycb_z.jy_device_maintenance_request 的数据：~0 rows (大约)

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
