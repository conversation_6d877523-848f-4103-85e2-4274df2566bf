-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_finance_record 结构
CREATE TABLE IF NOT EXISTS `jy_finance_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `record_no` varchar(64) NOT NULL COMMENT '流水号',
  `account_type` varchar(32) NOT NULL COMMENT '账户类型：system-系统 entity-业务主体 partner-合作商 shop-门店 user-用户',
  `account_id` int(11) NOT NULL COMMENT '账户所属ID',
  `entity_id` int(11) DEFAULT NULL COMMENT '业务主体ID',
  `partner_id` int(11) DEFAULT NULL COMMENT '合作商ID',
  `shop_id` int(11) DEFAULT NULL COMMENT '门店ID',
  `type` tinyint(4) NOT NULL COMMENT '类型：1-收入 2-支出 3-转入 4-转出 5-冻结 6-解冻 7-提现 8-退款 9-分成 10-系统调整',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `fee` decimal(15,2) DEFAULT '0.00' COMMENT '手续费',
  `before_balance` decimal(15,2) NOT NULL COMMENT '操作前余额',
  `after_balance` decimal(15,2) NOT NULL COMMENT '操作后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '关联交易ID',
  `related_record_id` bigint(20) DEFAULT NULL COMMENT '关联流水ID（如转账时的对方流水ID）',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `operator_id` int(11) DEFAULT NULL COMMENT '操作人ID',
  `operator_name` varchar(64) DEFAULT NULL COMMENT '操作人姓名',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-失败 1-成功 2-处理中',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_record_no` (`record_no`),
  KEY `idx_account` (`account_type`,`account_id`),
  KEY `idx_entity` (`entity_id`),
  KEY `idx_partner` (`partner_id`),
  KEY `idx_shop` (`shop_id`),
  KEY `idx_order` (`order_id`),
  KEY `idx_transaction` (`transaction_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_account_type_id_time` (`account_type`,`account_id`,`create_time`),
  KEY `idx_type_status` (`type`,`status`),
  KEY `idx_amount` (`amount`),
  KEY `idx_operator` (`operator_id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='财务流水记录表';

-- 正在导出表  jycb_z.jy_finance_record 的数据：~8 rows (大约)
INSERT INTO `jy_finance_record` (`id`, `record_no`, `account_type`, `account_id`, `entity_id`, `partner_id`, `shop_id`, `type`, `amount`, `fee`, `before_balance`, `after_balance`, `order_id`, `transaction_id`, `related_record_id`, `description`, `operator_id`, `operator_name`, `status`, `create_time`, `update_time`) VALUES
	(17, 'FR17533715324109970fb09', 'system', 1, NULL, NULL, NULL, 9, 5.88, 0.00, 8.68, 14.56, '121', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(18, 'FR17533715324154a740a95', 'entity', 2, 2, NULL, NULL, 9, 3.92, 0.00, 5.92, 9.84, '121', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(19, 'FR1753371532419e24e4e39', 'partner', 1, 2, 1, NULL, 9, 49.00, 0.00, 74.00, 123.00, '121', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(20, '***********************', 'shop', 1, 2, 1, 1, 9, 39.20, 0.00, 9.20, 48.40, '121', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(21, 'FR17533715324341232ef77', 'system', 1, NULL, NULL, NULL, 9, 3.00, 0.00, 14.56, 17.56, '122', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(22, 'FR1753371532439ef3608a1', 'entity', 2, 2, NULL, NULL, 9, 2.00, 0.00, 9.84, 11.84, '122', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(23, 'FR1753371532443805394cb', 'partner', 1, 2, 1, NULL, 9, 25.00, 0.00, 123.00, 148.00, '122', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52'),
	(24, 'FR17533715324474b3e9061', 'shop', 1, 2, 1, 1, 9, 20.00, 0.00, 48.40, 68.40, '122', NULL, NULL, '订单分成收入', NULL, '系统', 1, '2025-07-24 23:38:52', '2025-07-24 23:38:52');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
