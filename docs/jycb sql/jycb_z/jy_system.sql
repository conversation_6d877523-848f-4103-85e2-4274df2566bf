-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_system 结构
CREATE TABLE IF NOT EXISTS `jy_system` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_type` (`config_type`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一系统配置表';

-- 正在导出表  jycb_z.jy_system 的数据：~28 rows (大约)
INSERT INTO `jy_system` (`id`, `config_type`, `config_key`, `config_value`, `remark`, `create_time`, `update_time`) VALUES
	(1, 'system', 'system_name', '成人仿真娃娃管理系统', '系统名称', '2025-06-22 01:34:43', '2025-06-22 01:34:43'),
	(2, 'system', 'system_logo', '/static/logo.png', '系统Logo', '2025-06-22 01:34:43', '2025-06-22 01:34:43'),
	(3, 'wechat', 'appid', 'wx23b7c6a78842ee76', '微信小程序appid', '2025-06-22 01:34:43', '2025-07-01 14:49:09'),
	(4, 'wechat', 'secret', '63374942cd14aa34112354b2999a6ed6', '微信小程序secret', '2025-06-22 01:34:43', '2025-07-01 17:16:36'),
	(5, 'cos', 'secret_id', 'your-secret-id', '腾讯云API密钥的SecretId', '2025-06-23 01:15:59', '2025-06-23 01:15:59'),
	(6, 'cos', 'secret_key', 'your-secret-key', '腾讯云API密钥的SecretKey', '2025-06-23 01:15:59', '2025-06-23 01:15:59'),
	(7, 'cos', 'region', 'ap-guangzhou', '腾讯云COS地域', '2025-06-23 01:15:59', '2025-06-23 01:15:59'),
	(8, 'cos', 'bucket_name', 'your-bucket-name', '腾讯云COS存储桶名称', '2025-06-23 01:15:59', '2025-06-23 01:15:59'),
	(11, 'wechat', 'msgDataFormat', 'JSON', '微信小程序消息格式', '2025-06-23 01:15:59', '2025-06-23 01:15:59'),
	(12, 'finance', 'withdraw_min_amount', '1.00', '最小提现金额(元)', '2025-06-24 03:44:29', '2025-06-24 03:44:29'),
	(13, 'finance', 'withdraw_fee_rate', '0.006', '提现手续费率(千分之六)', '2025-06-24 03:44:29', '2025-06-24 03:44:29'),
	(57, 'device', 'default_max_users', '3', '设备默认最大同时使用人数', '2025-06-27 02:18:16', '2025-06-27 02:18:16'),
	(58, 'order', 'order_timeout_minutes', '120', '订单超时时间（分钟）', '2025-06-27 02:18:16', '2025-06-27 02:18:16'),
	(81, 'wechat', 'aesKey', '', '微信小程序 aesKey 配置项', '2025-07-01 22:15:42', '2025-07-01 22:15:42'),
	(82, 'wechat', 'token', '', '微信小程序 token 配置项', '2025-07-01 22:15:42', '2025-07-01 22:15:42'),
	(83, 'wx_miniapp', 'wx_miniapp_msgDataFormat', 'JSON', '微信小程序(兼容) msgDataFormat 配置项 (自动生成的组合键)', '2025-07-01 22:15:42', '2025-07-01 22:15:42'),
	(84, 'wx_miniapp', 'wx_miniapp_appid', 'wx23b7c6a78842ee76', '微信小程序(兼容) appid 配置项 (自动生成的组合键)', '2025-07-01 22:15:42', '2025-07-01 17:16:51'),
	(85, 'wx_miniapp', 'wx_miniapp_aesKey', '', '微信小程序(兼容) aesKey 配置项 (自动生成的组合键)', '2025-07-01 22:15:42', '2025-07-01 22:15:42'),
	(86, 'wx_miniapp', 'wx_miniapp_secret', '63374942cd14aa34112354b2999a6ed6', '微信小程序(兼容) secret 配置项 (自动生成的组合键)', '2025-07-01 22:15:42', '2025-07-01 17:16:41'),
	(87, 'wx_miniapp', 'wx_miniapp_token', '', '微信小程序(兼容) token 配置项 (自动生成的组合键)', '2025-07-01 22:15:42', '2025-07-01 22:15:42'),
	(88, 'wx_pay', 'mchId', '1721424072', '微信支付 mchId 配置项', '2025-07-01 22:15:42', '2025-07-15 15:36:41'),
	(89, 'wx_pay', 'wx_pay_appId', 'wx23b7c6a78842ee76', '微信支付 appId 配置项 (自动生成的组合键)', '2025-07-01 22:15:42', '2025-07-01 14:35:34'),
	(90, 'wx_pay', 'notifyUrl', 'https://api.example.com/api/wx/pay/callback/notify', '微信支付 notifyUrl 配置项', '2025-07-01 22:15:42', '2025-07-01 14:49:09'),
	(91, 'wx_pay', 'key', '5bKMsetvmbbsaPCUY8QrtyAEx3aZDuB7', '微信支付 key 配置项', '2025-07-01 22:15:42', '2025-07-15 15:35:39'),
	(92, 'wx_pay', 'merchantSerialNumber', '15878634CBF0536B67E993E13AFE7C6A6739131E', '微信支付商户API证书序列号', '2025-07-01 14:44:00', '2025-07-15 15:37:25'),
	(93, 'wx_pay', 'apiV3Key', '5bKMsetvmbbsaPCUY8QrtyAEx3aZDuB7', '微信支付APIv3密钥', '2025-07-01 14:44:00', '2025-07-15 15:35:11'),
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
	(104, 'wechat', 'wechat_aesKey', '', '微信小程序 aesKey 配置项 (自动生成的组合键)', '2025-07-02 00:29:16', '2025-07-02 00:29:16'),
	(105, 'wechat', 'wechat_token', '', '微信小程序 token 配置项 (自动生成的组合键)', '2025-07-02 00:29:16', '2025-07-02 00:29:16');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
