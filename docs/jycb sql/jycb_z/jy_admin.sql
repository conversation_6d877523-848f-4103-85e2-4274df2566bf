-- --------------------------------------------------------
-- 主机:                           127.0.0.1
-- 服务器版本:                        8.0.11 - MySQL Community Server - GPL
-- 服务器操作系统:                      Win64
-- HeidiS<PERSON> 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- 导出  表 jycb_z.jy_admin 结构
CREATE TABLE IF NOT EXISTS `jy_admin` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `admin_type` varchar(20) NOT NULL DEFAULT 'system' COMMENT '管理员类型：system-系统 entity-业务主体 partner-合作商 shop-门店',
  `entity_id` bigint(20) DEFAULT NULL COMMENT '所属业务主体ID',
  `partner_id` bigint(20) DEFAULT NULL COMMENT '所属合作商ID',
  `shop_id` bigint(20) DEFAULT NULL COMMENT '所属门店ID',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  `role_id` int(11) DEFAULT NULL COMMENT '角色ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` int(11) DEFAULT NULL COMMENT '创建人ID',
  `update_by` int(11) DEFAULT NULL COMMENT '更新人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除：0-未删除 1-已删除',
  `version` int(11) NOT NULL DEFAULT '0' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_admin_type` (`admin_type`),
  KEY `idx_entity_id` (`entity_id`),
  KEY `idx_partner_id` (`partner_id`),
  KEY `idx_shop_id` (`shop_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一管理员表';

-- 正在导出表  jycb_z.jy_admin 的数据：~5 rows (大约)
INSERT INTO `jy_admin` (`id`, `username`, `password`, `real_name`, `avatar`, `email`, `mobile`, `admin_type`, `entity_id`, `partner_id`, `shop_id`, `status`, `role_id`, `remark`, `last_login_ip`, `last_login_time`, `create_by`, `update_by`, `create_time`, `update_time`, `deleted`, `version`) VALUES
	(1, 'admin', 'e10adc3949ba59abbe56e057f20f883e', '系统管理员', NULL, NULL, NULL, 'system', NULL, NULL, NULL, 1, NULL, NULL, '0:0:0:0:0:0:0:1', '2025-07-18 02:08:47', NULL, NULL, '2025-06-22 01:38:07', '2025-07-18 02:08:46', 0, 0),
	(2, 'entity_admin', 'e10adc3949ba59abbe56e057f20f883e', '业务主体管理员', NULL, NULL, '13800000001', 'entity', 2, NULL, NULL, 1, NULL, NULL, '0:0:0:0:0:0:0:1', '2025-07-18 03:01:15', NULL, NULL, '2025-06-22 01:38:07', '2025-07-18 03:01:14', 0, 0),
	(3, 'partner_admin', 'e10adc3949ba59abbe56e057f20f883e', '合作商管理员', NULL, NULL, '13800000002', 'partner', 2, 1, NULL, 1, NULL, NULL, '0:0:0:0:0:0:0:1', '2025-06-26 14:29:01', NULL, NULL, '2025-06-22 01:38:07', '2025-06-26 14:29:00', 0, 0),
	(4, 'shop_admin', 'e10adc3949ba59abbe56e057f20f883e', '门店管理员', NULL, NULL, '13800000003', 'shop', 2, 1, 1, 1, NULL, NULL, '************4', '2025-07-21 01:47:44', NULL, NULL, '2025-06-22 01:38:07', '2025-07-21 01:47:44', 0, 0),
	(5, 'finance_admin', 'e10adc3949ba59abbe56e057f20f883e', '财务管理员', NULL, NULL, '13800000004', 'system', NULL, NULL, NULL, 1, NULL, NULL, '0:0:0:0:0:0:0:1', '2025-06-23 11:26:30', NULL, NULL, '2025-06-22 01:38:07', '2025-06-22 01:38:07', 0, 0);

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
