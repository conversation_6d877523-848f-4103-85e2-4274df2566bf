# 审计日志系统使用说明

## 1. 概述

审计日志系统用于记录系统中的操作行为，包括用户登录、数据增删改查等操作，有助于系统安全审计、问题排查和用户行为分析。

## 2. 数据库设计

审计日志表 `audit_log` 包含以下主要字段：

- `id`：审计日志ID
- `user_id`：用户ID
- `user_name`：用户名称
- `user_role`：用户角色
- `module`：操作模块
- `operation`：操作类型
- `target_type`：操作对象类型
- `target_id`：操作对象ID
- `description`：操作描述
- `request_method`：HTTP请求方法
- `request_url`：请求URL
- `request_params`：请求参数
- `response_code`：响应状态码
- `response_data`：响应数据
- `execution_time`：执行时长(毫秒)
- `ip_address`：操作IP
- `user_agent`：用户代理
- `status`：操作状态(成功/失败)
- `error_message`：错误信息
- `entity_id`：业务主体ID
- `partner_id`：合作商ID
- `shop_id`：门店ID
- `create_time`：操作时间

## 3. 使用方法

### 3.1 在控制器方法上添加 `@Auditable` 注解

```java
@GetMapping("/{id}")
@PreAuthorize("hasRole('ADMIN')")
@ApiOperation("根据ID查询用户")
@Auditable(
    module = AuditConstants.Module.USER,
    operation = AuditConstants.Operation.READ,
    description = "查询用户详情",
    targetType = AuditConstants.TargetType.USER,
    targetIdParam = "id"
)
public Result<User> getUserById(@PathVariable Long id) {
    // 业务逻辑
}
```

### 3.2 注解参数说明

- `module`：操作模块，必填，使用 `AuditConstants.Module` 中的常量
- `operation`：操作类型，必填，使用 `AuditConstants.Operation` 中的常量
- `description`：操作描述，选填，默认为空
- `targetType`：操作对象类型，选填，使用 `AuditConstants.TargetType` 中的常量
- `targetIdParam`：操作对象ID的参数名称，选填，默认为空
- `logRequestParams`：是否记录请求参数，选填，默认为 `true`
- `logResponseData`：是否记录响应数据，选填，默认为 `false`

### 3.3 常用模块和操作类型

模块类型 (`AuditConstants.Module`)：

- `AUTH`：认证授权
- `USER`：用户管理
- `PARTNER`：合作商管理
- `SHOP`：门店管理
- `ENTITY`：业务主体管理
- `DEVICE`：设备管理
- `ORDER`：订单管理
- `COMMISSION`：佣金管理
- `FINANCE`：财务管理
- `SYSTEM`：系统管理
- `REPORT`：报表管理
- `FEEDBACK`：反馈管理

操作类型 (`AuditConstants.Operation`)：

- 通用操作：`CREATE`、`READ`、`UPDATE`、`DELETE`、`QUERY`、`EXPORT`、`IMPORT`
- 认证授权操作：`LOGIN`、`LOGOUT`、`REGISTER`、`CHANGE_PASSWORD`、`RESET_PASSWORD`
- 佣金操作：`SET_COMMISSION`、`CALCULATE_COMMISSION`、`VIEW_COMMISSION`
- 财务操作：`WITHDRAW`、`DEPOSIT`、`AUDIT`
- 设备操作：`ACTIVATE`、`DEACTIVATE`、`MAINTAIN`、`CLEAN`
- 权限操作：`GRANT`、`REVOKE`

## 4. 审计日志查询

系统提供了以下审计日志查询接口：

- `GET /api/audit-logs/{id}`：根据ID查询审计日志
- `GET /api/audit-logs`：分页查询审计日志
- `GET /api/audit-logs/statistics/module`：按模块统计操作次数
- `GET /api/audit-logs/statistics/user`：按用户统计操作次数
- `GET /api/audit-logs/statistics/status`：按状态统计操作次数
- `DELETE /api/audit-logs/cleanup`：清理指定日期之前的审计日志

## 5. 示例

### 5.1 基本用法

```java
@GetMapping("/basic")
@ApiOperation("基本审计日志示例")
@Auditable(
    module = AuditConstants.Module.SYSTEM,
    operation = AuditConstants.Operation.READ,
    description = "基本审计日志示例"
)
public Result<String> basicExample() {
    return Result.success("基本审计日志示例");
}
```

### 5.2 完整用法

```java
@GetMapping("/complete/{id}")
@ApiOperation("完整审计日志示例")
@Auditable(
    module = AuditConstants.Module.USER,
    operation = AuditConstants.Operation.READ,
    description = "查询用户信息",
    targetType = AuditConstants.TargetType.USER,
    targetIdParam = "id",
    logRequestParams = true,
    logResponseData = true
)
public Result<String> completeExample(@PathVariable Long id) {
    return Result.success("用户ID: " + id);
}
```

## 6. 注意事项

1. 审计日志的记录是异步进行的，不会影响业务方法的执行性能。
2. 对于敏感信息（如密码、身份证号等），建议在记录请求参数前进行脱敏处理。
3. 响应数据可能较大，默认不记录，如需记录，请设置 `logResponseData = true`。
4. 定期清理历史审计日志，避免数据量过大影响系统性能。
5. 审计日志的查询接口仅对管理员开放，普通用户无权访问。 