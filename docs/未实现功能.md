今夜城堡系统未实现功能清单及优先级分析
未实现功能详细分析
1. 订单模块
订单定时任务
未支付订单超时处理：系统需要定期检查超过支付时限（如30分钟）的未支付订单，并自动取消。这对于释放设备资源至关重要。
进行中订单自动完成：长时间处于使用中状态的订单需要有自动结束机制，防止用户忘记结束使用。
已完成订单自动结算：订单完成后应触发佣金分成计算和账户余额更新。
历史订单数据清理：定期归档或清理过期订单数据，优化数据库性能。
订单事件监听器
订单创建后：需要实现设备状态更新、用户通知等逻辑。
订单支付后：需要触发设备开锁指令、更新设备状态、记录财务流水等。
订单完成后：需要计算最终费用、更新设备状态、生成佣金明细等。
订单取消后：需要释放设备资源、恢复设备状态等。
订单退款后：需要处理退款财务记录、更新账户余额等。
订单导出功能
各级管理员（系统管理员、业务主体、合作商、门店）需要能导出订单数据进行分析和对账。
2. 财务模块
结算系统
待结算列表查询：显示符合结算条件但尚未结算的佣金明细。
结算申请列表：合作商和门店发起的结算申请记录。
结算记录查询：已完成结算的历史记录。
结算详情查询：单次结算的详细信息，包括结算金额、时间等。
结算申请审批：管理员审核结算申请的流程。
手动结算：管理员手动触发结算操作。
结算统计查询：按时间、区域等维度统计结算数据。
结算记录导出：导出结算数据进行对账和分析。
佣金明细导出
导出佣金明细数据，便于各级管理员进行分析和对账。
财务账户管理
银行卡信息管理：合作商和门店需要管理提现银行卡信息。
提现记录查询：查询历史提现申请和状态。
门店提现记录分页查询：分页查询门店的提现记录。
提现记录更新：更新提现状态和相关信息。
系统配置
提现金额配置：设置最小和最大提现金额限制。
手续费配置：设置提现手续费率和最小、最大手续费限制。
3. 设备模块
门店设备管理
故障报告：门店报告设备故障的流程。
维护申请：门店申请设备维护的流程。
清洁申请：门店申请设备清洁的流程。
4. 反馈模块
反馈统计分析：对用户反馈进行分类统计和分析，提供决策支持。
5. 支付模块
支付配置优化：简化微信支付配置加载和私钥处理逻辑。
支付回调完善：完善支付回调处理，确保稳定性和安全性。
数据库表分析与功能关联
通过检查数据库表结构，我发现：
结算系统相关表：
缺少专门的结算记录表，可能需要新建jy_settlement_record表
jy_settlement_config表已存在，可用于配置结算规则
银行卡信息管理：
jy_partner_bank_card表已存在，可用于合作商银行卡管理
缺少门店银行卡表，可能需要新建jy_shop_bank_card表
设备管理相关表：
jy_device_log表可用于记录设备状态变更
缺少专门的设备故障报告表，可能需要新建jy_device_fault_report表
缺少设备维护申请表，可能需要新建jy_device_maintenance表
jy_clean_task表已存在，可用于清洁申请管理
订单事件处理：
jy_order表已存在，包含订单状态等信息
jy_commission_detail表已存在，可用于记录订单分成
jy_finance_log表已存在，可用于记录财务流水
优先级建议及实现思路
最高优先级（核心业务功能）
订单定时任务
实现思路：使用Spring的@Scheduled注解创建定时任务
关键点：处理并发问题，使用分布式锁确保任务不重复执行
相关表：jy_order
订单事件监听器
实现思路：完善OrderEventListener中的各个事件处理方法
关键点：确保事件处理的原子性和一致性
相关表：jy_order, jy_device, jy_finance_log, jy_commission_detail
结算系统基础功能
实现思路：完善SettlementController中的各个方法
关键点：结算规则的灵活配置和准确计算
相关表：jy_settlement_config, jy_commission_detail, jy_finance_account
高优先级（重要业务功能）
银行卡信息管理
实现思路：完善FinanceAccountServiceImpl中的银行卡相关方法
关键点：银行卡信息的安全存储和验证
相关表：jy_partner_bank_card
门店设备管理
实现思路：完善ShopDeviceController中的故障报告、维护申请和清洁申请方法
关键点：确保设备状态的准确更新和通知机制
相关表：jy_device, jy_device_log, jy_clean_task
中优先级（提升体验功能）
订单导出功能
实现思路：使用POI或EasyExcel库实现Excel生成
关键点：大数据量导出的性能优化
相关表：jy_order
佣金明细导出
实现思路：使用POI或EasyExcel库实现Excel生成
关键点：数据格式的规范和导出性能
相关表：jy_commission_detail
系统配置完善
实现思路：从jy_system表中读取配置参数
关键点：配置的动态更新和缓存机制
相关表：jy_system
低优先级（辅助功能）
反馈统计功能
实现思路：完善AdminFeedbackController中的统计方法
关键点：多维度的数据分析和可视化
相关表：jy_feedback
支付模块优化
实现思路：重构WxPayServiceImpl中的配置加载和私钥处理逻辑
关键点：简化代码，提高可读性和可维护性
相关表：jy_system（存储支付配置）