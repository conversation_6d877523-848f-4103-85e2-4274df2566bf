-- =====================================================
-- 今夜城堡(JYCB)系统管理员权限初始化脚本
-- 创建时间: 2025-07-29
-- 用途: 初始化系统管理员的完整权限体系
-- =====================================================

-- 1. 创建系统管理员角色
INSERT INTO jy_role (role_name, role_code, role_type, description, status, create_time, update_time) VALUES
('系统超级管理员', 'SYSTEM_SUPER_ADMIN', 'admin', '系统最高权限管理员，拥有所有功能权限', 1, NOW(), NOW()),
('系统管理员', 'SYSTEM_ADMIN', 'admin', '系统管理员，拥有大部分管理权限', 1, NOW(), NOW()),
('系统监控员', 'SYSTEM_MONITOR', 'admin', '系统监控专员，负责系统监控和维护', 1, NOW(), NOW());

-- 2. 创建菜单结构
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
-- 一级菜单
(NULL, '系统管理', 'SYSTEM', 1, '/system', 'Layout', NULL, 'system', 1, 1, 1, 1, NOW(), NOW()),
(NULL, '用户管理', 'USER', 1, '/user', 'Layout', NULL, 'user', 2, 1, 1, 1, NOW(), NOW()),
(NULL, '设备管理', 'DEVICE', 1, '/device', 'Layout', NULL, 'device', 3, 1, 1, 1, NOW(), NOW()),
(NULL, '订单管理', 'ORDER', 1, '/order', 'Layout', NULL, 'order', 4, 1, 1, 1, NOW(), NOW()),
(NULL, '财务管理', 'FINANCE', 1, '/finance', 'Layout', NULL, 'finance', 5, 1, 1, 1, NOW(), NOW()),
(NULL, '清洁管理', 'CLEAN', 1, '/clean', 'Layout', NULL, 'clean', 6, 1, 1, 1, NOW(), NOW()),
(NULL, '统计分析', 'STATISTICS', 1, '/statistics', 'Layout', NULL, 'chart', 7, 1, 1, 1, NOW(), NOW()),
(NULL, '系统监控', 'MONITOR', 1, '/monitor', 'Layout', NULL, 'monitor', 8, 1, 1, 1, NOW(), NOW());

-- 获取一级菜单ID（假设从1开始）
SET @system_id = 1;
SET @user_id = 2;
SET @device_id = 3;
SET @order_id = 4;
SET @finance_id = 5;
SET @clean_id = 6;
SET @statistics_id = 7;
SET @monitor_id = 8;

-- 二级菜单 - 系统管理
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@system_id, '管理员管理', 'ADMIN_MANAGE', 2, '/system/admin', 'system/admin/index', 'admin:admin:list', 'admin', 1, 1, 1, 2, NOW(), NOW()),
(@system_id, '角色管理', 'ROLE_MANAGE', 2, '/system/role', 'system/role/index', 'admin:role:list', 'role', 2, 1, 1, 2, NOW(), NOW()),
(@system_id, '权限管理', 'PERMISSION_MANAGE', 2, '/system/permission', 'system/permission/index', 'admin:permission:list', 'permission', 3, 1, 1, 2, NOW(), NOW()),
(@system_id, '菜单管理', 'MENU_MANAGE', 2, '/system/menu', 'system/menu/index', 'admin:menu:list', 'menu', 4, 1, 1, 2, NOW(), NOW()),
(@system_id, '系统配置', 'SYSTEM_CONFIG', 2, '/system/config', 'system/config/index', 'admin:system:config', 'config', 5, 1, 1, 2, NOW(), NOW()),
(@system_id, '操作日志', 'OPERATION_LOG', 2, '/system/log', 'system/log/index', 'admin:system:log', 'log', 6, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 用户管理
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@user_id, '用户列表', 'USER_LIST', 2, '/user/list', 'user/list/index', 'admin:user:list', 'user-list', 1, 1, 1, 2, NOW(), NOW()),
(@user_id, '用户统计', 'USER_STATISTICS', 2, '/user/statistics', 'user/statistics/index', 'admin:user:statistics', 'user-chart', 2, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 设备管理
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@device_id, '设备列表', 'DEVICE_LIST', 2, '/device/list', 'device/list/index', 'admin:device:list', 'device-list', 1, 1, 1, 2, NOW(), NOW()),
(@device_id, '设备监控', 'DEVICE_MONITOR', 2, '/device/monitor', 'device/monitor/index', 'admin:device:monitor', 'device-monitor', 2, 1, 1, 2, NOW(), NOW()),
(@device_id, '设备维护', 'DEVICE_MAINTENANCE', 2, '/device/maintenance', 'device/maintenance/index', 'admin:device:maintenance', 'device-repair', 3, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 订单管理
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@order_id, '订单列表', 'ORDER_LIST', 2, '/order/list', 'order/list/index', 'admin:order:list', 'order-list', 1, 1, 1, 2, NOW(), NOW()),
(@order_id, '订单统计', 'ORDER_STATISTICS', 2, '/order/statistics', 'order/statistics/index', 'admin:order:statistics', 'order-chart', 2, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 财务管理
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@finance_id, '财务概览', 'FINANCE_OVERVIEW', 2, '/finance/overview', 'finance/overview/index', 'admin:finance:overview', 'finance-overview', 1, 1, 1, 2, NOW(), NOW()),
(@finance_id, '账户管理', 'FINANCE_ACCOUNT', 2, '/finance/account', 'finance/account/index', 'admin:finance:account', 'finance-account', 2, 1, 1, 2, NOW(), NOW()),
(@finance_id, '分成配置', 'FINANCE_COMMISSION', 2, '/finance/commission', 'finance/commission/index', 'admin:finance:commission', 'finance-commission', 3, 1, 1, 2, NOW(), NOW()),
(@finance_id, '提现管理', 'FINANCE_WITHDRAW', 2, '/finance/withdraw', 'finance/withdraw/index', 'admin:finance:withdraw', 'finance-withdraw', 4, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 清洁管理
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@clean_id, '清洁任务', 'CLEAN_TASK', 2, '/clean/task', 'clean/task/index', 'admin:clean:task:list', 'clean-task', 1, 1, 1, 2, NOW(), NOW()),
(@clean_id, '清洁人员', 'CLEAN_CLEANER', 2, '/clean/cleaner', 'clean/cleaner/index', 'admin:clean:cleaner:list', 'clean-cleaner', 2, 1, 1, 2, NOW(), NOW()),
(@clean_id, '清洁统计', 'CLEAN_STATISTICS', 2, '/clean/statistics', 'clean/statistics/index', 'admin:clean:statistics', 'clean-chart', 3, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 统计分析
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@statistics_id, '数据概览', 'STATISTICS_OVERVIEW', 2, '/statistics/overview', 'statistics/overview/index', 'admin:statistics:overview', 'statistics-overview', 1, 1, 1, 2, NOW(), NOW()),
(@statistics_id, '用户分析', 'STATISTICS_USER', 2, '/statistics/user', 'statistics/user/index', 'admin:statistics:users', 'statistics-user', 2, 1, 1, 2, NOW(), NOW()),
(@statistics_id, '设备分析', 'STATISTICS_DEVICE', 2, '/statistics/device', 'statistics/device/index', 'admin:statistics:devices', 'statistics-device', 3, 1, 1, 2, NOW(), NOW()),
(@statistics_id, '财务分析', 'STATISTICS_FINANCE', 2, '/statistics/finance', 'statistics/finance/index', 'admin:statistics:finance', 'statistics-finance', 4, 1, 1, 2, NOW(), NOW());

-- 二级菜单 - 系统监控
INSERT INTO jy_menu (parent_id, menu_name, menu_code, menu_type, path, component, permission, icon, sort, visible, status, level, create_time, update_time) VALUES
(@monitor_id, '系统概览', 'MONITOR_OVERVIEW', 2, '/monitor/overview', 'monitor/overview/index', 'admin:system:monitor:overview', 'monitor-overview', 1, 1, 1, 2, NOW(), NOW()),
(@monitor_id, '服务器监控', 'MONITOR_SERVER', 2, '/monitor/server', 'monitor/server/index', 'admin:system:monitor:server', 'monitor-server', 2, 1, 1, 2, NOW(), NOW()),
(@monitor_id, '数据库监控', 'MONITOR_DATABASE', 2, '/monitor/database', 'monitor/database/index', 'admin:system:monitor:database', 'monitor-database', 3, 1, 1, 2, NOW(), NOW()),
(@monitor_id, '告警管理', 'MONITOR_ALERT', 2, '/monitor/alert', 'monitor/alert/index', 'admin:system:monitor:alerts', 'monitor-alert', 4, 1, 1, 2, NOW(), NOW());

-- 3. 创建权限点
INSERT INTO jy_permission (permission_code, permission_name, permission_type, description, status, create_time, update_time) VALUES
-- 管理员管理权限
('admin:admin:list', '查看管理员列表', 'menu', '查看管理员列表权限', 1, NOW(), NOW()),
('admin:admin:create', '创建管理员', 'button', '创建管理员权限', 1, NOW(), NOW()),
('admin:admin:update', '更新管理员', 'button', '更新管理员权限', 1, NOW(), NOW()),
('admin:admin:delete', '删除管理员', 'button', '删除管理员权限', 1, NOW(), NOW()),
('admin:admin:detail', '查看管理员详情', 'button', '查看管理员详情权限', 1, NOW(), NOW()),
('admin:admin:assign-role', '分配角色', 'button', '分配角色权限', 1, NOW(), NOW()),

-- 角色管理权限
('admin:role:list', '查看角色列表', 'menu', '查看角色列表权限', 1, NOW(), NOW()),
('admin:role:create', '创建角色', 'button', '创建角色权限', 1, NOW(), NOW()),
('admin:role:update', '更新角色', 'button', '更新角色权限', 1, NOW(), NOW()),
('admin:role:delete', '删除角色', 'button', '删除角色权限', 1, NOW(), NOW()),

-- 权限管理权限
('admin:permission:list', '查看权限列表', 'menu', '查看权限列表权限', 1, NOW(), NOW()),
('admin:permission:create', '创建权限', 'button', '创建权限权限', 1, NOW(), NOW()),
('admin:permission:update', '更新权限', 'button', '更新权限权限', 1, NOW(), NOW()),
('admin:permission:delete', '删除权限', 'button', '删除权限权限', 1, NOW(), NOW()),

-- 菜单管理权限
('admin:menu:list', '查看菜单列表', 'menu', '查看菜单列表权限', 1, NOW(), NOW()),
('admin:menu:create', '创建菜单', 'button', '创建菜单权限', 1, NOW(), NOW()),
('admin:menu:update', '更新菜单', 'button', '更新菜单权限', 1, NOW(), NOW()),
('admin:menu:delete', '删除菜单', 'button', '删除菜单权限', 1, NOW(), NOW()),
('admin:menu:assign', '分配菜单权限', 'button', '分配菜单权限', 1, NOW(), NOW()),

-- 用户管理权限
('admin:user:list', '查看用户列表', 'menu', '查看用户列表权限', 1, NOW(), NOW()),
('admin:user:detail', '查看用户详情', 'button', '查看用户详情权限', 1, NOW(), NOW()),
('admin:user:update', '更新用户信息', 'button', '更新用户信息权限', 1, NOW(), NOW()),
('admin:user:status', '更新用户状态', 'button', '更新用户状态权限', 1, NOW(), NOW()),
('admin:user:statistics', '查看用户统计', 'menu', '查看用户统计权限', 1, NOW(), NOW()),

-- 设备管理权限
('admin:device:list', '查看设备列表', 'menu', '查看设备列表权限', 1, NOW(), NOW()),
('admin:device:create', '创建设备', 'button', '创建设备权限', 1, NOW(), NOW()),
('admin:device:update', '更新设备', 'button', '更新设备权限', 1, NOW(), NOW()),
('admin:device:delete', '删除设备', 'button', '删除设备权限', 1, NOW(), NOW()),
('admin:device:monitor', '设备监控', 'menu', '设备监控权限', 1, NOW(), NOW()),
('admin:device:maintenance', '设备维护', 'menu', '设备维护权限', 1, NOW(), NOW()),

-- 订单管理权限
('admin:order:list', '查看订单列表', 'menu', '查看订单列表权限', 1, NOW(), NOW()),
('admin:order:detail', '查看订单详情', 'button', '查看订单详情权限', 1, NOW(), NOW()),
('admin:order:update', '更新订单', 'button', '更新订单权限', 1, NOW(), NOW()),
('admin:order:refund', '订单退款', 'button', '订单退款权限', 1, NOW(), NOW()),
('admin:order:statistics', '查看订单统计', 'menu', '查看订单统计权限', 1, NOW(), NOW()),

-- 财务管理权限
('admin:finance:overview', '财务概览', 'menu', '财务概览权限', 1, NOW(), NOW()),
('admin:finance:account', '账户管理', 'menu', '账户管理权限', 1, NOW(), NOW()),
('admin:finance:commission', '分成配置', 'menu', '分成配置权限', 1, NOW(), NOW()),
('admin:finance:withdraw', '提现管理', 'menu', '提现管理权限', 1, NOW(), NOW()),

-- 清洁管理权限
('admin:clean:task:list', '查看清洁任务', 'menu', '查看清洁任务权限', 1, NOW(), NOW()),
('admin:clean:task:create', '创建清洁任务', 'button', '创建清洁任务权限', 1, NOW(), NOW()),
('admin:clean:task:update', '更新清洁任务', 'button', '更新清洁任务权限', 1, NOW(), NOW()),
('admin:clean:task:delete', '删除清洁任务', 'button', '删除清洁任务权限', 1, NOW(), NOW()),
('admin:clean:cleaner:list', '查看清洁人员', 'menu', '查看清洁人员权限', 1, NOW(), NOW()),
('admin:clean:cleaner:create', '创建清洁人员', 'button', '创建清洁人员权限', 1, NOW(), NOW()),
('admin:clean:cleaner:update', '更新清洁人员', 'button', '更新清洁人员权限', 1, NOW(), NOW()),
('admin:clean:cleaner:delete', '删除清洁人员', 'button', '删除清洁人员权限', 1, NOW(), NOW()),
('admin:clean:statistics', '清洁统计', 'menu', '清洁统计权限', 1, NOW(), NOW()),

-- 统计分析权限
('admin:statistics:overview', '数据概览', 'menu', '数据概览权限', 1, NOW(), NOW()),
('admin:statistics:users', '用户统计', 'menu', '用户统计权限', 1, NOW(), NOW()),
('admin:statistics:devices', '设备统计', 'menu', '设备统计权限', 1, NOW(), NOW()),
('admin:statistics:orders', '订单统计', 'menu', '订单统计权限', 1, NOW(), NOW()),
('admin:statistics:finance', '财务统计', 'menu', '财务统计权限', 1, NOW(), NOW()),
('admin:statistics:export', '导出统计', 'button', '导出统计权限', 1, NOW(), NOW()),

-- 系统监控权限
('admin:system:monitor:overview', '系统概览', 'menu', '系统概览权限', 1, NOW(), NOW()),
('admin:system:monitor:server', '服务器监控', 'menu', '服务器监控权限', 1, NOW(), NOW()),
('admin:system:monitor:database', '数据库监控', 'menu', '数据库监控权限', 1, NOW(), NOW()),
('admin:system:monitor:performance', '性能监控', 'menu', '性能监控权限', 1, NOW(), NOW()),
('admin:system:monitor:alerts', '告警管理', 'menu', '告警管理权限', 1, NOW(), NOW()),

-- 系统配置权限
('admin:system:config', '系统配置', 'menu', '系统配置权限', 1, NOW(), NOW()),
('admin:system:log', '操作日志', 'menu', '操作日志权限', 1, NOW(), NOW());

-- 4. 为系统超级管理员角色分配所有权限
INSERT INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    (SELECT id FROM jy_role WHERE role_code = 'SYSTEM_SUPER_ADMIN'),
    id,
    NOW()
FROM jy_permission;

-- 5. 为系统超级管理员角色分配所有菜单
INSERT INTO jy_role_menu (role_id, menu_id, create_time)
SELECT 
    (SELECT id FROM jy_role WHERE role_code = 'SYSTEM_SUPER_ADMIN'),
    id,
    NOW()
FROM jy_menu;

-- 6. 为现有的系统管理员分配超级管理员角色
INSERT INTO jy_admin_role (admin_id, role_id, create_time)
SELECT 
    id,
    (SELECT id FROM jy_role WHERE role_code = 'SYSTEM_SUPER_ADMIN'),
    NOW()
FROM jy_admin 
WHERE admin_type = 'system';

-- 7. 更新菜单层级和路径信息
UPDATE jy_menu SET 
    menu_path = CONCAT('/', id),
    is_leaf = CASE WHEN id IN (SELECT DISTINCT parent_id FROM jy_menu WHERE parent_id IS NOT NULL) THEN 0 ELSE 1 END,
    child_count = (SELECT COUNT(*) FROM jy_menu m2 WHERE m2.parent_id = jy_menu.id)
WHERE parent_id IS NULL;

UPDATE jy_menu SET 
    menu_path = CONCAT((SELECT menu_path FROM jy_menu p WHERE p.id = jy_menu.parent_id), '/', jy_menu.id),
    is_leaf = CASE WHEN id IN (SELECT DISTINCT parent_id FROM jy_menu WHERE parent_id IS NOT NULL) THEN 0 ELSE 1 END,
    child_count = (SELECT COUNT(*) FROM jy_menu m2 WHERE m2.parent_id = jy_menu.id)
WHERE parent_id IS NOT NULL;

-- 8. 初始化系统配置
INSERT INTO jy_system_config (config_key, config_value, config_name, config_desc, config_type, status, create_time, update_time) VALUES
('system.admin.session.timeout', '7200', '管理员会话超时时间', '管理员登录会话超时时间（秒）', 'system', 1, NOW(), NOW()),
('system.admin.password.policy', 'strong', '密码策略', '管理员密码强度策略：weak/medium/strong', 'security', 1, NOW(), NOW()),
('system.admin.login.max.attempts', '5', '最大登录尝试次数', '管理员登录最大失败尝试次数', 'security', 1, NOW(), NOW()),
('system.admin.permission.cache.timeout', '3600', '权限缓存超时时间', '管理员权限缓存超时时间（秒）', 'performance', 1, NOW(), NOW()),
('system.monitor.alert.enabled', 'true', '系统告警开关', '是否启用系统监控告警', 'monitor', 1, NOW(), NOW()),
('system.statistics.realtime.enabled', 'true', '实时统计开关', '是否启用实时统计功能', 'statistics', 1, NOW(), NOW());

-- 执行完成提示
SELECT '系统管理员权限初始化完成！' as message,
       (SELECT COUNT(*) FROM jy_role WHERE role_type = 'admin') as admin_roles,
       (SELECT COUNT(*) FROM jy_menu) as total_menus,
       (SELECT COUNT(*) FROM jy_permission) as total_permissions,
       (SELECT COUNT(*) FROM jy_admin WHERE admin_type = 'system') as system_admins;
