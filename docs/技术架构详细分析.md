# 今夜城堡(JYCB)技术架构详细分析

## 🏗️ 整体技术架构

### 架构概览图
```mermaid
graph TB
    subgraph "前端层"
        A[微信小程序] 
        B[管理后台Web]
    end
    
    subgraph "网关层"
        C[Nginx反向代理]
        D[SSL证书]
    end
    
    subgraph "应用层"
        E[Spring Boot 3.5.3]
        F[Sa-Token认证]
        G[MyBatis-Plus]
    end
    
    subgraph "服务层"
        H[业务服务]
        I[定时任务]
        J[事件处理]
    end
    
    subgraph "数据层"
        K[MySQL 8.4.5]
        L[Redis缓存]
    end
    
    subgraph "外部服务"
        M[微信支付API]
        N[腾讯云COS]
        O[微信小程序API]
    end
    
    A --> C
    B --> C
    C --> E
    E --> F
    E --> G
    G --> K
    F --> L
    E --> M
    E --> N
    E --> O
    H --> I
    H --> J
```

## 🔧 技术栈详细分析

### 1. 核心框架

#### 1.1 Spring Boot 3.5.3
```yaml
特性分析:
  版本: 3.5.3 (最新稳定版)
  Java版本: JDK 17
  优势:
    - 自动配置简化开发
    - 内嵌Tomcat容器
    - 生产就绪特性
    - 丰富的生态系统
  
配置文件结构:
  - application.yml (主配置)
  - application-system.yml (系统模块配置)
  - 支持多环境配置
```

#### 1.2 MyBatis-Plus 3.5.5
```yaml
ORM特性:
  - 代码生成器
  - 条件构造器
  - 分页插件
  - 逻辑删除
  - 乐观锁
  - 多租户
  
配置示例:
  mybatis-plus:
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        id-type: AUTO
        logic-delete-field: deleted
        logic-delete-value: 1
        logic-not-delete-value: 0
```

#### 1.3 Sa-Token 1.37.0
```yaml
认证授权特性:
  - 登录认证
  - 权限验证
  - Session管理
  - 单点登录
  - OAuth2
  - 微服务网关鉴权
  
配置示例:
  sa-token:
    token-name: Authorization
    timeout: 2592000
    active-timeout: -1
    is-concurrent: true
    token-style: uuid
```

### 2. 数据存储

#### 2.1 MySQL 8.4.5
```sql
-- 数据库特性
版本: 8.4.5
字符集: utf8mb4_0900_ai_ci
存储引擎: InnoDB
连接池: HikariCP

-- 性能优化配置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
sync_binlog = 0
```

#### 2.2 Redis缓存
```yaml
使用场景:
  - Sa-Token会话存储
  - 热点数据缓存
  - 分布式锁
  - 消息队列
  
配置:
  spring:
    redis:
      host: localhost
      port: 6379
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
```

### 3. 第三方集成

#### 3.1 微信生态集成
```java
// 微信小程序配置
@Configuration
public class WxMiniAppConfig {
    @Bean
    public WxMaService wxMaService() {
        WxMaDefaultServiceImpl service = new WxMaDefaultServiceImpl();
        service.setWxMaConfig(wxMaConfig());
        return service;
    }
}

// 微信支付集成
@Service
public class WxPayService {
    // 统一下单
    // 支付回调处理
    // 退款处理
}
```

#### 3.2 腾讯云COS存储
```java
@Configuration
public class CosConfig {
    @Bean
    public COSClient cosClient() {
        COSCredentials cred = new BasicCOSCredentials(secretId, secretKey);
        Region region = new Region(regionName);
        ClientConfig clientConfig = new ClientConfig(region);
        return new COSClient(cred, clientConfig);
    }
}
```

## 🏛️ 分层架构设计

### 1. 控制层 (Controller)

#### 1.1 API设计规范
```java
@RestController
@RequestMapping("/api/device")
@RequiredArgsConstructor
@Tag(name = "设备接口", description = "设备相关接口")
public class DeviceController {
    
    @GetMapping("/scan/{bindCode}")
    @Operation(summary = "扫码获取设备信息")
    public CommonResult<DeviceVO> scanDevice(@PathVariable String bindCode) {
        // 业务逻辑
    }
    
    @PostMapping("/{id}/unlock")
    @SaCheckLogin
    @Operation(summary = "设备开锁")
    public CommonResult<Void> unlockDevice(@PathVariable Long id) {
        // 业务逻辑
    }
}
```

#### 1.2 统一响应格式
```java
@Data
public class CommonResult<T> {
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
    
    public static <T> CommonResult<T> success(T data) {
        return new CommonResult<>(200, "操作成功", data);
    }
    
    public static <T> CommonResult<T> failed(String message) {
        return new CommonResult<>(500, message, null);
    }
}
```

### 2. 业务层 (Service)

#### 2.1 服务接口设计
```java
public interface OrderService extends IService<Order> {
    /**
     * 创建订单
     */
    Order createOrder(Integer deviceId, String deviceNo, Integer userId, BigDecimal amount);
    
    /**
     * 支付订单
     */
    boolean payOrder(String orderNo, String payType, String transactionId);
    
    /**
     * 结束订单
     */
    boolean endOrder(String orderNo);
}
```

#### 2.2 事务管理
```java
@Service
@Transactional(rollbackFor = Exception.class)
public class OrderServiceImpl implements OrderService {
    
    @Override
    public Order createOrder(Integer deviceId, String deviceNo, Integer userId, BigDecimal amount) {
        // 使用分布式锁确保原子性
        return distributedLock.executeWithLock("order:device:" + deviceId, () -> {
            // 业务逻辑
        });
    }
}
```

### 3. 数据访问层 (Mapper)

#### 3.1 MyBatis-Plus使用
```java
@Mapper
public interface OrderMapper extends BaseMapper<Order> {
    
    /**
     * 自定义查询方法
     */
    @Select("SELECT * FROM jy_order WHERE shop_id = #{shopId} AND pay_status = 1")
    List<Order> selectPaidOrdersByShop(@Param("shopId") Long shopId);
    
    /**
     * 复杂统计查询
     */
    Map<String, Object> getOrderStatistics(@Param("shopId") Long shopId, 
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime);
}
```

#### 3.2 动态SQL使用
```xml
<select id="getOrderStatistics" resultType="map">
    SELECT 
        COUNT(*) as total_orders,
        SUM(amount) as total_amount,
        AVG(actual_duration) as avg_duration
    FROM jy_order 
    WHERE 1=1
    <if test="shopId != null">
        AND shop_id = #{shopId}
    </if>
    <if test="startTime != null">
        AND create_time >= #{startTime}
    </if>
    <if test="endTime != null">
        AND create_time <= #{endTime}
    </if>
</select>
```

## 🔐 安全架构

### 1. 认证授权

#### 1.1 Sa-Token认证流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as 网关
    participant Auth as 认证服务
    participant Redis as Redis
    
    Client->>Gateway: 登录请求
    Gateway->>Auth: 验证用户信息
    Auth->>Redis: 生成Token并缓存
    Redis-->>Auth: 缓存成功
    Auth-->>Gateway: 返回Token
    Gateway-->>Client: 登录成功
    
    Client->>Gateway: 业务请求(携带Token)
    Gateway->>Redis: 验证Token
    Redis-->>Gateway: Token有效
    Gateway->>Auth: 获取用户权限
    Auth-->>Gateway: 返回权限信息
    Gateway-->>Client: 请求成功
```

#### 1.2 权限注解使用
```java
@RestController
public class AdminController {
    
    @SaCheckLogin
    @SaCheckPermission("admin:user:list")
    @GetMapping("/admin/user/page")
    public CommonResult<Page<User>> getUserPage() {
        // 需要登录且有用户列表权限
    }
    
    @SaCheckRole("system")
    @PostMapping("/admin/user")
    public CommonResult<Void> createUser() {
        // 需要系统管理员角色
    }
}
```

### 2. 数据权限

#### 2.2 数据权限过滤
```java
@Component
public class DataPermissionInterceptor implements Interceptor {
    
    @Override
    public void beforeQuery(Invocation invocation) {
        // 获取当前用户信息
        AdminVO currentAdmin = getCurrentAdmin();
        
        // 根据管理员类型添加数据过滤条件
        switch (currentAdmin.getAdminType()) {
            case "entity":
                addEntityFilter(currentAdmin.getEntityId());
                break;
            case "partner":
                addPartnerFilter(currentAdmin.getPartnerId());
                break;
            case "shop":
                addShopFilter(currentAdmin.getShopId());
                break;
        }
    }
}
```

## ⚡ 性能优化

### 1. 缓存策略

#### 1.1 多级缓存架构
```java
@Service
public class DeviceService {
    
    @Cacheable(value = "device", key = "#deviceId")
    public Device getDeviceById(Long deviceId) {
        return deviceMapper.selectById(deviceId);
    }
    
    @CacheEvict(value = "device", key = "#device.id")
    public void updateDevice(Device device) {
        deviceMapper.updateById(device);
    }
}
```

#### 1.2 分布式锁
```java
@Component
public class DistributedLock {
    
    public <T> T executeWithLock(String lockKey, Supplier<T> supplier) {
        String requestId = UUID.randomUUID().toString();
        try {
            if (tryLock(lockKey, requestId, 30)) {
                return supplier.get();
            } else {
                throw new BusinessException("获取锁失败");
            }
        } finally {
            releaseLock(lockKey, requestId);
        }
    }
}
```

### 2. 数据库优化

#### 2.1 连接池配置
```yaml
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
```

#### 2.2 SQL优化
```java
// 使用MyBatis-Plus条件构造器
LambdaQueryWrapper<Order> queryWrapper = new LambdaQueryWrapper<Order>()
    .eq(Order::getShopId, shopId)
    .eq(Order::getPayStatus, 1)
    .ge(Order::getCreateTime, startTime)
    .le(Order::getCreateTime, endTime)
    .orderByDesc(Order::getCreateTime);

// 分页查询
Page<Order> page = new Page<>(pageNum, pageSize);
Page<Order> result = orderMapper.selectPage(page, queryWrapper);
```

## 🔄 异步处理

### 1. 事件驱动架构

#### 1.1 订单状态变更事件
```java
@Component
public class OrderEventListener {
    
    @EventListener
    @Async
    public void handleOrderPaid(OrderPaidEvent event) {
        // 处理订单支付成功事件
        // 1. 发送开锁指令
        // 2. 触发分账处理
        // 3. 发送通知消息
    }
    
    @EventListener
    @Async
    public void handleOrderCompleted(OrderCompletedEvent event) {
        // 处理订单完成事件
        // 1. 计算实际费用
        // 2. 处理退款
        // 3. 更新统计数据
    }
}
```

### 2. 定时任务

#### 2.1 系统定时任务
```java
@Component
@EnableScheduling
public class SystemScheduledTasks {
    
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupLogs() {
        // 每天凌晨2点清理日志
    }
    
    @Scheduled(cron = "0 */5 * * * ?")
    public void checkDeviceStatus() {
        // 每5分钟检查设备状态
    }
    
    @Scheduled(cron = "0 0 1 * * ?")
    public void generateDailyReport() {
        // 每天凌晨1点生成日报
    }
}
```

## 📊 监控和运维

### 1. 应用监控

#### 1.1 健康检查
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    
    @Override
    public Health health() {
        // 检查数据库连接
        // 检查Redis连接
        // 检查外部服务
        return Health.up()
            .withDetail("database", "UP")
            .withDetail("redis", "UP")
            .build();
    }
}
```

#### 1.2 性能指标
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

### 2. 日志管理

#### 2.1 日志配置
```yaml
logging:
  level:
    root: info
    com.jycb.jycbz: debug
  file:
    name: ./logs/jycb-z.log
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

---

**文档版本**: v1.0  
**更新时间**: 2025-07-29
