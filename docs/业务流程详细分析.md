# 今夜城堡(JYCB)业务流程详细分析

## 🎯 业务模式概述

今夜城堡(JYCB)是一个B2B2C的成人用品设备租赁平台，采用多级代理商模式，通过微信小程序为C端用户提供设备租赁服务。

### 业务参与方
- **平台方**: 系统运营商，提供技术平台和管理服务
- **业务主体**: 一级代理商，负责区域业务拓展
- **合作商**: 二级代理商，负责具体区域运营
- **门店**: 三级代理商，负责设备投放和维护
- **用户**: 终端消费者，通过微信小程序使用设备

## 🏗️ 组织架构体系

```mermaid
graph TD
    A[平台方] --> B[业务主体1]
    A --> C[业务主体2]
    B --> D[合作商1-1]
    B --> E[合作商1-2]
    C --> F[合作商2-1]
    D --> G[门店1-1-1]
    D --> H[门店1-1-2]
    E --> I[门店1-2-1]
    F --> J[门店2-1-1]
    
    G --> K[设备群1]
    H --> L[设备群2]
    I --> M[设备群3]
    J --> N[设备群4]
    
    O[用户] --> K
    O --> L
    O --> M
    O --> N
```

## 📱 核心业务流程

### 1. 用户使用流程

#### 1.1 完整使用流程图
```mermaid
sequenceDiagram
    participant U as 用户
    participant MP as 微信小程序
    participant API as 后端API
    participant WX as 微信支付
    participant DB as 数据库
    participant Device as 设备
    
    Note over U,Device: 设备发现和验证
    U->>MP: 扫描设备二维码
    MP->>API: 获取设备信息
    API->>DB: 查询设备状态
    DB-->>API: 返回设备信息
    
    alt 设备可用
        API-->>MP: 设备信息 + 费用
        MP-->>U: 显示设备详情
    else 设备不可用
        API-->>MP: 设备不可用
        MP-->>U: 提示设备状态
    end
    
    Note over U,Device: 订单创建和支付
    U->>MP: 点击开始使用
    MP->>API: 创建订单
    API->>DB: 生成订单记录
    DB-->>API: 订单创建成功
    API-->>MP: 返回订单信息
    
    U->>MP: 确认支付
    MP->>WX: 调用微信支付
    WX-->>MP: 支付成功回调
    MP->>API: 支付成功通知
    
    Note over U,Device: 设备开锁和使用
    API->>DB: 更新订单支付状态
    API->>API: 触发分账处理
    API->>Device: 发送开锁指令
    Device-->>API: 开锁成功确认
    API-->>MP: 开锁成功
    MP-->>U: 设备已开锁，可以使用
    
    Note over U,Device: 使用过程监控
    loop 使用期间
        Device->>API: 上报设备状态
        API->>DB: 更新设备使用状态
        U->>MP: 查看使用状态
        MP->>API: 获取订单状态
        API-->>MP: 返回使用信息
    end
    
    Note over U,Device: 结束使用和结算
    U->>MP: 点击结束使用
    MP->>API: 结束订单请求
    API->>Device: 发送关锁指令
    Device-->>API: 关锁成功确认
    API->>DB: 计算实际费用
    API->>DB: 更新订单状态
    API->>API: 处理退款(如有)
    API-->>MP: 订单完成
    MP-->>U: 显示使用总结
```

#### 1.2 关键业务节点

**1. 设备扫码验证**
- 验证设备是否存在且可用
- 检查设备当前状态(在线、故障、维护)
- 验证设备是否达到最大并发用户数
- 获取设备费用配置

**2. 订单创建**
- 生成唯一订单号
- 记录设备和用户信息
- 设置订单初始状态(待支付)
- 预估使用费用

**3. 支付处理**
- 调用微信支付API
- 处理支付回调
- 更新订单支付状态
- 触发自动分账

**4. 设备控制**
- 发送开锁/关锁指令
- 监控设备响应
- 更新设备使用状态
- 记录操作日志

**5. 费用结算**
- 计算实际使用时长
- 按时长计算实际费用
- 处理多退少补
- 生成财务流水

### 2. 财务分成流程

#### 2.1 分成配置体系
```mermaid
graph TD
    A[订单收入 100%] --> B[平台分成 5%]
    A --> C[业务主体分成 5%]
    A --> D[合作商分成 50%]
    A --> E[门店分成 40%]
    
    F[分成配置层级] --> G[系统级配置]
    F --> H[业务主体级配置]
    F --> I[合作商级配置]
    F --> J[门店级配置]
    
    G --> H
    H --> I
    I --> J
```

#### 2.2 自动分账流程
```mermaid
sequenceDiagram
    participant Order as 订单系统
    participant Config as 分成配置
    participant Finance as 财务系统
    participant Account as 账户系统
    
    Order->>Config: 获取分成配置
    Config-->>Order: 返回分成比例
    
    Order->>Finance: 触发分账处理
    Finance->>Finance: 计算各方分成金额
    
    par 平台分成
        Finance->>Account: 增加平台账户余额
    and 业务主体分成
        Finance->>Account: 增加业务主体账户余额
    and 合作商分成
        Finance->>Account: 增加合作商账户余额
    and 门店分成
        Finance->>Account: 增加门店账户余额
    end
    
    Finance->>Finance: 记录分成明细
    Finance->>Order: 更新订单分账状态
```

### 3. 设备管理流程

#### 3.1 设备生命周期
```mermaid
stateDiagram-v2
    [*] --> 未绑定: 设备入库
    未绑定 --> 已绑定: 绑定到门店
    已绑定 --> 正常: 激活设备
    正常 --> 使用中: 用户开始使用
    使用中 --> 正常: 用户结束使用
    正常 --> 维护中: 设备维护
    维护中 --> 正常: 维护完成
    正常 --> 故障: 设备故障
    故障 --> 维护中: 开始维修
    故障 --> 报废: 无法修复
    维护中 --> 报废: 维护失败
    报废 --> [*]: 设备下线
```

#### 3.2 设备状态监控
- **在线状态**: 设备网络连接状态
- **使用状态**: 当前是否有用户在使用
- **电池状态**: 电池电量百分比
- **位置状态**: GPS定位信息
- **故障状态**: 设备自检结果

### 4. 权限管理流程

#### 4.1 多级权限体系
```mermaid
graph TD
    A[系统管理员] --> B[全局权限]
    A --> C[管理所有业务主体]
    
    D[业务主体管理员] --> E[业务主体权限]
    D --> F[管理下属合作商]
    
    G[合作商管理员] --> H[合作商权限]
    G --> I[管理下属门店]
    
    J[门店管理员] --> K[门店权限]
    J --> L[管理门店设备]
```

#### 4.2 数据权限过滤
- **系统级**: 可查看所有数据
- **业务主体级**: 只能查看本业务主体及下属数据
- **合作商级**: 只能查看本合作商及下属数据
- **门店级**: 只能查看本门店数据

## 💰 收费模式分析

### 1. 计费方式
- **按时计费**: 按实际使用时长收费
- **起步价**: 最低消费金额
- **时长阶梯**: 不同时长段不同单价

### 2. 费用构成
```
用户支付金额
├── 设备使用费 (主要收入)
├── 清洁费 (可选)
├── 服务费 (平台费用)
└── 其他费用 (保险等)
```

### 3. 退款机制
- **未使用退款**: 支付后未开锁可全额退款
- **部分退款**: 实际使用时长少于预付时长
- **故障退款**: 设备故障导致的使用中断

## 🔄 异常处理流程

### 1. 支付异常
- 支付超时处理
- 支付失败重试
- 重复支付检测

### 2. 设备异常
- 设备离线处理
- 开锁失败处理
- 设备故障上报

### 3. 订单异常
- 订单超时处理
- 异常订单人工介入
- 争议订单处理

## 📊 数据统计分析

### 1. 运营数据
- 设备使用率统计
- 用户活跃度分析
- 收入趋势分析
- 故障率统计

### 2. 财务数据
- 各级分成统计
- 提现申请处理
- 账户余额监控
- 财务流水分析

### 3. 用户数据
- 用户注册趋势
- 用户使用习惯
- 用户满意度调查
- 用户流失分析

---

**文档版本**: v1.0  
**更新时间**: 2025-07-29
