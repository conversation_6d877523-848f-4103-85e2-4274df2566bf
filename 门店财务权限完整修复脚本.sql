-- =====================================================
-- 门店财务权限完整修复脚本
-- 解决门店管理员无法访问财务功能的问题
-- =====================================================

-- 1. 添加门店财务相关权限
-- =====================================================
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) VALUES
('shop:finance:manage', '门店财务管理权限', 1, NOW(), NOW()),
('shop:finance:read', '门店财务信息查看权限', 1, NOW(), NOW()),
('shop:finance:write', '门店财务操作权限', 1, NOW(), NOW()),
('shop:finance:withdraw', '门店提现权限', 1, NOW(), NOW()),
('shop:finance:bank-card:list', '门店银行卡列表查看权限', 1, NOW(), NOW()),
('shop:finance:bank-card:add', '门店银行卡添加权限', 1, NOW(), NOW()),
('shop:finance:bank-card:update', '门店银行卡修改权限', 1, NOW(), NOW()),
('shop:finance:bank-card:delete', '门店银行卡删除权限', 1, NOW(), NOW()),
('shop:finance:revenue:list', '门店收益明细查看权限', 1, NOW(), NOW()),
('shop:finance:commission:read', '门店分成配置查看权限', 1, NOW(), NOW()),
('shop:finance:withdraw:apply', '门店申请提现权限', 1, NOW(), NOW()),
('shop:finance:withdraw:list', '门店提现记录查看权限', 1, NOW(), NOW()),
('shop:finance:withdraw:cancel', '门店取消提现权限', 1, NOW(), NOW());

-- 2. 为门店管理员角色分配财务权限
-- =====================================================

-- 获取门店管理员角色ID
SET @shop_admin_role_id = (SELECT id FROM jy_role WHERE role_code = 'shop_admin' LIMIT 1);

-- 分配核心财务权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    @shop_admin_role_id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_permission p
WHERE p.permission IN (
    'shop:finance:manage',
    'shop:finance:read', 
    'shop:finance:write',
    'shop:finance:withdraw'
) AND @shop_admin_role_id IS NOT NULL;

-- 分配银行卡管理权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    @shop_admin_role_id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_permission p
WHERE p.permission IN (
    'shop:finance:bank-card:list',
    'shop:finance:bank-card:add',
    'shop:finance:bank-card:update',
    'shop:finance:bank-card:delete'
) AND @shop_admin_role_id IS NOT NULL;

-- 分配收益和提现权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    @shop_admin_role_id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_permission p
WHERE p.permission IN (
    'shop:finance:revenue:list',
    'shop:finance:commission:read',
    'shop:finance:withdraw:apply',
    'shop:finance:withdraw:list',
    'shop:finance:withdraw:cancel'
) AND @shop_admin_role_id IS NOT NULL;

-- 3. 验证权限分配结果
-- =====================================================

-- 查看门店管理员角色的所有财务权限
SELECT 
    '门店管理员财务权限列表' as check_type,
    r.role_name,
    p.permission,
    p.description,
    p.status
FROM jy_role r
JOIN jy_role_permission rp ON r.id = rp.role_id
JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin' 
  AND p.permission LIKE '%finance%'
ORDER BY p.permission;

-- 检查特定管理员的财务权限
SELECT 
    '管理员ID4财务权限检查' as check_type,
    a.id as admin_id,
    a.username,
    a.real_name,
    a.shop_id,
    p.permission,
    p.description
FROM jy_admin a
JOIN jy_admin_role ar ON a.id = ar.admin_id
JOIN jy_role_permission rp ON ar.role_id = rp.role_id
JOIN jy_permission p ON rp.permission_id = p.id
WHERE a.id = 4 AND p.permission LIKE '%finance%'
ORDER BY p.permission;

-- 4. 添加其他门店相关权限（可选）
-- =====================================================

-- 门店基础权限
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) VALUES
('shop:order:read', '门店订单查看权限', 1, NOW(), NOW()),
('shop:device:read', '门店设备查看权限', 1, NOW(), NOW()),
('shop:statistics:read', '门店统计数据查看权限', 1, NOW(), NOW()),
('shop:profile:read', '门店信息查看权限', 1, NOW(), NOW()),
('shop:profile:write', '门店信息修改权限', 1, NOW(), NOW());

-- 为门店管理员分配基础权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    @shop_admin_role_id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_permission p
WHERE p.permission IN (
    'shop:order:read',
    'shop:device:read',
    'shop:statistics:read',
    'shop:profile:read',
    'shop:profile:write'
) AND @shop_admin_role_id IS NOT NULL;

-- 5. 最终验证
-- =====================================================

-- 统计门店管理员的权限总数
SELECT 
    '权限统计' as check_type,
    r.role_name,
    COUNT(p.id) as total_permissions,
    COUNT(CASE WHEN p.permission LIKE '%finance%' THEN 1 END) as finance_permissions,
    COUNT(CASE WHEN p.permission LIKE '%shop%' THEN 1 END) as shop_permissions
FROM jy_role r
JOIN jy_role_permission rp ON r.id = rp.role_id
JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
GROUP BY r.id, r.role_name;

-- 检查权限是否生效（需要重新登录或刷新权限缓存）
SELECT 
    '权限生效检查' as check_type,
    'shop:finance:read' as required_permission,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM jy_admin a
            JOIN jy_admin_role ar ON a.id = ar.admin_id
            JOIN jy_role_permission rp ON ar.role_id = rp.role_id
            JOIN jy_permission p ON rp.permission_id = p.id
            WHERE a.id = 4 AND p.permission = 'shop:finance:read'
        ) THEN '已分配' 
        ELSE '未分配' 
    END as permission_status;

-- =====================================================
-- 执行完成提示
-- =====================================================
SELECT 
    '修复完成' as status,
    '门店财务权限已修复，请重新登录或刷新权限缓存' as message,
    NOW() as completion_time;
