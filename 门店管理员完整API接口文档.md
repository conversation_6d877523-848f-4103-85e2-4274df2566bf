# 门店管理员完整API接口文档

## 项目概述

今夜城堡(JYCB-Z)门店管理员完整API接口文档，涵盖门店管理员日常运营管理所需的所有接口功能。

## 基础信息

- **基础URL**: `http://localhost:8081`
- **API版本**: v1.0
- **认证方式**: <PERSON><PERSON> (Sa-Token)
- **数据格式**: JSON

## 统一响应格式

所有接口返回格式统一为：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T12:00:00"
}
```

**状态码说明**：
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未认证或token过期
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 1. 认证管理接口

### 1.1 管理员登录

**接口**: `POST /auth/admin/login`

**描述**: 门店管理员登录认证

**请求参数**:

| 参数名   | 类型   | 必填 | 描述   |
|---------|--------|-----|--------|
| username | String | 是   | 用户名 |
| password | String | 是   | 密码   |

**请求示例**:

```json
{
  "username": "shop_admin_001",
  "password": "123456"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenName": "Authorization",
    "tokenTimeout": 86400,
    "userInfo": {
      "id": 10001,
      "username": "shop_admin_001",
      "realName": "张店长",
      "role": "SHOP_ADMIN",
      "shopId": 1001,
      "mobile": "***********",
      "email": "<EMAIL>"
    },
    "permissions": [
      "shop:device:read",
      "shop:order:read",
      "shop:finance:read",
      "shop:statistics:read"
    ],
    "menus": [
      {
        "id": 1,
        "name": "设备管理",
        "path": "/device",
        "icon": "device"
      }
    ]
  }
}
```

### 1.2 管理员登出

**接口**: `POST /admin/auth/logout`

**描述**: 门店管理员登出

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

### 1.3 检查登录状态

**接口**: `GET /admin/auth/check`

**描述**: 检查当前登录状态

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isLogin": true,
    "loginId": "10001",
    "tokenTimeout": 7200
  }
}
```

### 1.4 获取管理员信息

**接口**: `GET /auth/admin/info`

**描述**: 获取当前登录管理员详细信息

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 10001,
    "username": "shop_admin_001",
    "realName": "张店长",
    "mobile": "***********",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "status": 1,
    "createTime": "2024-01-01T10:00:00",
    "lastLoginTime": "2024-01-01T12:00:00",
    "shopInfo": {
      "id": 1001,
      "shopName": "测试门店",
      "address": "北京市朝阳区xxx街道",
      "contactName": "张店长",
      "contactPhone": "***********"
    }
  }
}
```

### 1.5 获取个人详细信息

**接口**: `GET /auth/admin/profile`

**描述**: 获取当前登录管理员的详细个人信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 10001,
    "username": "shop_admin_001",
    "realName": "张店长",
    "mobile": "***********",
    "email": "<EMAIL>",
    "avatar": "https://example.com/avatar.jpg",
    "gender": 1,
    "birthday": "1990-01-01",
    "address": "北京市朝阳区xxx街道",
    "status": 1,
    "createTime": "2024-01-01T10:00:00",
    "lastLoginTime": "2024-01-01T12:00:00",
    "loginCount": 156,
    "shopInfo": {
      "id": 1001,
      "shopName": "测试门店",
      "address": "北京市朝阳区xxx街道",
      "contactName": "张店长",
      "contactPhone": "***********"
    }
  }
}
```

### 1.6 修改密码

**接口**: `PUT /auth/admin/password`

**描述**: 门店管理员修改登录密码

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型   | 必填 | 描述   |
|------------|--------|-----|--------|
| oldPassword| String | 是   | 旧密码 |
| newPassword| String | 是   | 新密码 |

**请求示例**:

```json
{
  "oldPassword": "123456",
  "newPassword": "newPassword123"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "密码修改成功",
  "data": true
}
```

### 1.7 修改个人信息

**接口**: `PUT /auth/admin/profile`

**描述**: 修改当前登录管理员的个人信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名   | 类型   | 必填 | 描述     |
|---------|--------|-----|----------|
| realName| String | 否   | 真实姓名 |
| mobile  | String | 否   | 手机号   |
| email   | String | 否   | 邮箱     |
| avatar  | String | 否   | 头像URL  |
| gender  | Integer| 否   | 性别(1男2女) |
| birthday| String | 否   | 生日     |
| address | String | 否   | 地址     |

**请求示例**:

```json
{
  "realName": "李店长",
  "mobile": "***********",
  "email": "<EMAIL>"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "个人信息更新成功",
  "data": true
}
```

## 2. 门店管理接口

### 2.1 获取当前门店信息

**接口**: `GET /shop/admin/info`

**描述**: 获取当前登录门店管理员所属门店的详细信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "shopName": "测试门店",
    "shopCode": "SHOP001",
    "address": "北京市朝阳区xxx街道",
    "contactName": "张店长",
    "contactPhone": "***********",
    "businessHours": "09:00-22:00",
    "status": 1,
    "statusName": "正常营业",
    "partnerId": 101,
    "partnerName": "测试合作商",
    "entityId": 1,
    "entityName": "测试业务主体",
    "createTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T12:00:00"
  }
}
```

### 2.2 获取门店数据统计仪表盘

**接口**: `GET /shop/admin/dashboard`

**描述**: 获取当前门店的统计数据，包括订单数、收入等

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "todayStats": {
      "orderCount": 25,
      "revenue": 1250.00,
      "deviceUsageRate": 0.85
    },
    "monthStats": {
      "orderCount": 680,
      "revenue": 34000.00,
      "avgDailyRevenue": 1100.00
    },
    "deviceStats": {
      "totalCount": 20,
      "onlineCount": 18,
      "maintenanceCount": 1,
      "faultCount": 1
    },
    "revenueChart": [
      {
        "date": "2024-01-01",
        "revenue": 1200.00
      }
    ]
  }
}
```

### 2.3 修改门店联系人信息

**接口**: `PUT /shop/admin/contact`

**描述**: 修改当前门店的联系人姓名和电话

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名       | 类型   | 必填 | 描述     |
|-------------|--------|-----|----------|
| contactName | String | 是   | 联系人姓名 |
| contactPhone| String | 是   | 联系人电话 |

**请求示例**:

```json
{
  "contactName": "李店长",
  "contactPhone": "***********"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "联系人信息更新成功",
  "data": true
}
```

### 2.4 修改门店银行账户信息

**接口**: `PUT /shop/admin/bank-account`

**描述**: 修改当前门店的银行账户信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型   | 必填 | 描述     |
|------------|--------|-----|----------|
| bankName   | String | 是   | 开户银行  |
| bankAccount| String | 是   | 银行卡号  |
| accountName| String | 是   | 收款人姓名 |

**请求示例**:

```json
{
  "bankName": "中国工商银行",
  "bankAccount": "6222021234567890123",
  "accountName": "张三"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "银行账户信息更新成功",
  "data": true
}
```

## 3. 设备管理接口

### 3.1 获取门店设备列表

**接口**: `GET /api/shop/devices`

**描述**: 获取当前门店的设备列表，支持分页和筛选

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名   | 类型    | 必填 | 默认值 | 描述                           |
|---------|---------|-----|-------|--------------------------------|
| page    | Integer | 否   | 1     | 页码                           |
| size    | Integer | 否   | 10    | 每页条数                       |
| deviceNo| String  | 否   | -     | 设备编号                       |
| status  | Integer | 否   | -     | 设备状态：1-正常 2-维护中 3-故障 |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 1001,
        "deviceNo": "DEV001",
        "deviceName": "设备001",
        "macAddress": "AA:BB:CC:DD:EE:FF",
        "status": 1,
        "statusName": "正常",
        "batteryLevel": 85,
        "isOnline": true,
        "roomNumber": "101",
        "lastHeartbeat": "2024-01-01T12:00:00",
        "createTime": "2024-01-01T10:00:00"
      }
    ],
    "total": 20,
    "size": 10,
    "current": 1,
    "pages": 2
  }
}
```

### 3.2 获取设备详情

**接口**: `GET /api/shop/devices/{id}`

**描述**: 获取指定设备的详细信息

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型    | 必填 | 描述   |
|-------|---------|-----|--------|
| id    | Integer | 是   | 设备ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "bindCode": "BIND123",
    "status": 1,
    "statusName": "正常",
    "batteryLevel": 85,
    "isOnline": true,
    "roomNumber": "101",
    "shopId": 1001,
    "shopName": "测试门店",
    "partnerId": 101,
    "partnerName": "测试合作商",
    "entityId": 1,
    "entityName": "测试业务主体",
    "lastHeartbeat": "2024-01-01T12:00:00",
    "bindTime": "2024-01-01T10:00:00",
    "createTime": "2024-01-01T09:00:00",
    "updateTime": "2024-01-01T12:00:00"
  }
}
```

### 3.3 根据MAC地址查询设备

**接口**: `GET /api/shop/devices/mac/{macAddress}`

**描述**: 根据MAC地址查询设备信息

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名     | 类型   | 必填 | 描述      |
|-----------|--------|-----|-----------|
| macAddress| String | 是   | MAC地址   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "status": 1,
    "statusName": "正常",
    "batteryLevel": 85,
    "isOnline": true,
    "roomNumber": "101"
  }
}
```

### 3.4 更新设备状态

**接口**: `PUT /api/shop/devices/{id}/status`

**描述**: 更新设备状态，用于报告设备故障或维护需求

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型    | 必填 | 描述   |
|-------|---------|-----|--------|
| id    | Integer | 是   | 设备ID |

**请求参数**:

| 参数名 | 类型    | 必填 | 描述                           |
|-------|---------|-----|--------------------------------|
| status| Integer | 是   | 设备状态：1-正常 2-维护中 3-故障 |

**请求示例**:

```json
{
  "status": 3
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "设备状态更新成功",
  "data": null
}
```

### 3.5 获取门店设备统计信息

**接口**: `GET /api/shop/devices/statistics`

**描述**: 获取当前门店的设备统计数据

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalCount": 20,
    "onlineCount": 18,
    "offlineCount": 2,
    "normalCount": 17,
    "maintenanceCount": 2,
    "faultCount": 1,
    "batteryLowCount": 3,
    "usageRate": 0.85,
    "avgBatteryLevel": 78.5
  }
}
```

### 3.6 上报设备故障

**接口**: `POST /api/shop/devices/{deviceId}/fault`

**描述**: 门店管理员上报设备故障

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名   | 类型    | 必填 | 描述   |
|---------|---------|-----|--------|
| deviceId| Integer | 是   | 设备ID |

**请求参数**:

| 参数名      | 类型    | 必填 | 描述                                    |
|------------|---------|-----|----------------------------------------|
| faultType  | Integer | 是   | 故障类型(1:无法开锁 2:无法关锁 3:电量异常 4:通信异常 5:其他) |
| description| String  | 是   | 故障描述                               |
| images     | Array   | 否   | 故障图片URL数组                        |
| urgency    | Integer | 否   | 紧急程度(1:低 2:中 3:高)               |

**请求示例**:

```json
{
  "faultType": 1,
  "description": "设备无法正常开锁，用户反馈多次尝试失败",
  "images": [
    "https://example.com/fault1.jpg",
    "https://example.com/fault2.jpg"
  ],
  "urgency": 3
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "故障上报成功",
  "data": {
    "faultId": 50001,
    "faultNo": "FAULT20240101001",
    "deviceId": 1001,
    "deviceNo": "DEV001",
    "faultType": 1,
    "faultTypeName": "无法开锁",
    "description": "设备无法正常开锁，用户反馈多次尝试失败",
    "urgency": 3,
    "urgencyName": "高",
    "status": 0,
    "statusName": "待处理",
    "createTime": "2024-01-01T14:00:00"
  }
}
```

### 3.7 查看设备故障处理进度

**接口**: `GET /api/shop/devices/faults`

**描述**: 分页查询门店设备故障记录和处理进度

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名   | 类型    | 必填 | 默认值 | 描述                                    |
|---------|---------|-----|-------|-----------------------------------------|
| page    | Integer | 否   | 1     | 页码                                    |
| size    | Integer | 否   | 10    | 每页条数                                |
| deviceId| Integer | 否   | -     | 设备ID                                  |
| status  | Integer | 否   | -     | 处理状态(0:待处理 1:处理中 2:已解决 3:已关闭) |
| faultType| Integer| 否   | -     | 故障类型                                |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 50001,
        "faultNo": "FAULT20240101001",
        "deviceId": 1001,
        "deviceNo": "DEV001",
        "deviceName": "设备001",
        "roomNumber": "101",
        "faultType": 1,
        "faultTypeName": "无法开锁",
        "description": "设备无法正常开锁，用户反馈多次尝试失败",
        "urgency": 3,
        "urgencyName": "高",
        "status": 1,
        "statusName": "处理中",
        "images": [
          "https://example.com/fault1.jpg"
        ],
        "reportTime": "2024-01-01T14:00:00",
        "assignTime": "2024-01-01T14:30:00",
        "assignee": "维修员张三",
        "estimatedFixTime": "2024-01-01T18:00:00"
      }
    ],
    "total": 15,
    "size": 10,
    "current": 1,
    "pages": 2
  }
}
```

### 3.8 刷新设备状态

**接口**: `POST /api/shop/devices/{deviceId}/refresh`

**描述**: 手动刷新指定设备的状态信息

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名   | 类型    | 必填 | 描述   |
|---------|---------|-----|--------|
| deviceId| Integer | 是   | 设备ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "设备状态刷新成功",
  "data": {
    "deviceId": 1001,
    "deviceNo": "DEV001",
    "status": 1,
    "statusName": "正常",
    "batteryLevel": 85,
    "isOnline": true,
    "lastHeartbeat": "2024-01-01T15:00:00",
    "refreshTime": "2024-01-01T15:00:00"
  }
}
```

### 3.9 扫描设备二维码

**接口**: `GET /api/shop/devices/scan/{macAddress}`

**描述**: 通过扫描设备二维码获取设备信息

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名     | 类型   | 必填 | 描述      |
|-----------|--------|-----|-----------|
| macAddress| String | 是   | MAC地址   |

**响应示例**:

```json
{
  "code": 200,
  "message": "设备扫描成功",
  "data": {
    "id": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "macAddress": "AA:BB:CC:DD:EE:FF",
    "status": 1,
    "statusName": "正常",
    "batteryLevel": 85,
    "isOnline": true,
    "roomNumber": "101",
    "isBound": true,
    "shopId": 1001,
    "shopName": "测试门店"
  }
}
```

### 3.10 申请设备维护

**接口**: `POST /api/shop/devices/request-maintenance`

**描述**: 申请设备维护服务

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型    | 必填 | 描述                           |
|------------|---------|-----|--------------------------------|
| deviceId   | Integer | 是   | 设备ID                         |
| maintenanceType| Integer | 是   | 维护类型(1:定期保养 2:深度清洁 3:部件更换) |
| description| String  | 是   | 维护说明                       |
| urgency    | Integer | 否   | 紧急程度(1:低 2:中 3:高)       |
| preferredTime| String | 否   | 期望维护时间                   |

**请求示例**:

```json
{
  "deviceId": 1001,
  "maintenanceType": 1,
  "description": "设备运行3个月，需要进行定期保养",
  "urgency": 2,
  "preferredTime": "2024-01-02T10:00:00"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "维护申请提交成功",
  "data": {
    "requestId": 60001,
    "requestNo": "MAINT20240101001",
    "deviceId": 1001,
    "deviceNo": "DEV001",
    "maintenanceType": 1,
    "maintenanceTypeName": "定期保养",
    "description": "设备运行3个月，需要进行定期保养",
    "urgency": 2,
    "urgencyName": "中",
    "status": 0,
    "statusName": "待安排",
    "preferredTime": "2024-01-02T10:00:00",
    "createTime": "2024-01-01T15:00:00"
  }
}
```

### 3.11 申请设备清洁

**接口**: `POST /api/shop/devices/request-cleaning`

**描述**: 申请设备清洁服务

**权限**: `shop_admin`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型    | 必填 | 描述                           |
|------------|---------|-----|--------------------------------|
| deviceId   | Integer | 是   | 设备ID                         |
| cleaningType| Integer | 是   | 清洁类型(1:日常清洁 2:深度清洁 3:消毒处理) |
| description| String  | 是   | 清洁说明                       |
| urgency    | Integer | 否   | 紧急程度(1:低 2:中 3:高)       |
| preferredTime| String | 否   | 期望清洁时间                   |

**请求示例**:

```json
{
  "deviceId": 1001,
  "cleaningType": 2,
  "description": "设备表面有污渍，需要深度清洁",
  "urgency": 2,
  "preferredTime": "2024-01-02T14:00:00"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "清洁申请提交成功",
  "data": {
    "requestId": 70001,
    "requestNo": "CLEAN20240101001",
    "deviceId": 1001,
    "deviceNo": "DEV001",
    "cleaningType": 2,
    "cleaningTypeName": "深度清洁",
    "description": "设备表面有污渍，需要深度清洁",
    "urgency": 2,
    "urgencyName": "中",
    "status": 0,
    "statusName": "待安排",
    "preferredTime": "2024-01-02T14:00:00",
    "createTime": "2024-01-01T15:00:00"
  }
}
```

## 4. 订单管理接口

### 4.1 分页查询门店订单列表

**接口**: `GET /shop/order/page`

**描述**: 查看当前门店下的订单列表，支持状态、时间、房间号等筛选

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 必填 | 默认值 | 描述                                    |
|-----------|---------|-----|-------|-----------------------------------------|
| current   | Integer | 否   | 1     | 当前页                                  |
| size      | Integer | 否   | 10    | 每页大小                                |
| orderNo   | String  | 否   | -     | 订单号                                  |
| status    | Integer | 否   | -     | 订单状态：0-待支付 1-进行中 2-已完成 3-已取消 |
| startDate | String  | 否   | -     | 开始日期 (yyyy-MM-dd)                   |
| endDate   | String  | 否   | -     | 结束日期 (yyyy-MM-dd)                   |
| roomNumber| String  | 否   | -     | 房间号                                  |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 10001,
        "orderNo": "ORD20240101001",
        "deviceId": 1001,
        "deviceNo": "DEV001",
        "roomNumber": "101",
        "userId": 2001,
        "userName": "张三",
        "userPhone": "***********",
        "amount": 50.00,
        "actualAmount": 45.00,
        "orderStatus": 2,
        "orderStatusName": "已完成",
        "payStatus": 1,
        "payStatusName": "已支付",
        "payType": "WECHAT",
        "startTime": "2024-01-01T10:00:00",
        "endTime": "2024-01-01T11:30:00",
        "duration": 90,
        "createTime": "2024-01-01T09:55:00"
      }
    ],
    "total": 150,
    "size": 10,
    "current": 1,
    "pages": 15
  }
}
```

### 4.2 获取订单详情

**接口**: `GET /shop/order/{id}`

**描述**: 获取指定订单的详细信息

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述   |
|-------|------|-----|--------|
| id    | Long | 是   | 订单ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 10001,
    "orderNo": "ORD20240101001",
    "deviceId": 1001,
    "deviceNo": "DEV001",
    "deviceName": "设备001",
    "roomNumber": "101",
    "userId": 2001,
    "userName": "张三",
    "userPhone": "***********",
    "userAvatar": "https://example.com/avatar.jpg",
    "amount": 50.00,
    "actualAmount": 45.00,
    "discountAmount": 5.00,
    "orderStatus": 2,
    "orderStatusName": "已完成",
    "payStatus": 1,
    "payStatusName": "已支付",
    "payType": "WECHAT",
    "payTypeName": "微信支付",
    "transactionId": "wx123456789",
    "startTime": "2024-01-01T10:00:00",
    "endTime": "2024-01-01T11:30:00",
    "duration": 90,
    "remark": "正常使用",
    "createTime": "2024-01-01T09:55:00",
    "payTime": "2024-01-01T10:00:00",
    "updateTime": "2024-01-01T11:30:00"
  }
}
```

### 4.3 获取订单统计数据

**接口**: `GET /shop/order/statistics`

**描述**: 获取门店订单统计数据

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名    | 类型   | 必填 | 描述                    |
|----------|--------|-----|-------------------------|
| startDate| String | 否   | 开始日期 (yyyy-MM-dd)   |
| endDate  | String | 否   | 结束日期 (yyyy-MM-dd)   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalOrders": 1250,
    "completedOrders": 1180,
    "cancelledOrders": 70,
    "totalRevenue": 62500.00,
    "avgOrderAmount": 50.00,
    "completionRate": 0.944,
    "todayOrders": 25,
    "todayRevenue": 1250.00,
    "monthOrders": 680,
    "monthRevenue": 34000.00,
    "orderTrend": [
      {
        "date": "2024-01-01",
        "orderCount": 25,
        "revenue": 1250.00
      }
    ],
    "hourlyDistribution": [
      {
        "hour": 9,
        "orderCount": 5
      }
    ]
  }
}
```

### 4.4 导出订单数据

**接口**: `POST /shop/order/export`

**描述**: 导出门店订单数据为Excel文件

**权限**: `shop:order:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

```json
{
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "status": 2,
  "roomNumber": "101"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "导出成功",
  "data": {
    "fileName": "门店订单数据_20240101_20240131.xlsx",
    "downloadUrl": "https://example.com/download/orders_20240101.xlsx",
    "fileSize": 1024000,
    "recordCount": 150
  }
}
```

### 4.5 手动结束订单

**接口**: `POST /api/shop/orders/{orderId}/complete`

**描述**: 门店管理员手动结束正在进行的订单

**权限**: `shop:order:write`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名  | 类型   | 必填 | 描述   |
|--------|--------|-----|--------|
| orderId| String | 是   | 订单ID |

**请求参数**:

| 参数名 | 类型   | 必填 | 描述     |
|-------|--------|-----|----------|
| reason| String | 否   | 结束原因 |

**请求示例**:

```json
{
  "reason": "用户提前结束使用"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "订单结束成功",
  "data": {
    "orderId": 10001,
    "orderNo": "ORD20240101001",
    "originalEndTime": "2024-01-01T12:00:00",
    "actualEndTime": "2024-01-01T11:30:00",
    "originalAmount": 50.00,
    "actualAmount": 37.50,
    "refundAmount": 12.50,
    "status": 2,
    "statusName": "已完成"
  }
}
```

## 5. 财务管理接口

### 5.1 获取门店财务账户信息

**接口**: `GET /shop/finance/account`

**描述**: 查看门店的账户余额、收益明细等财务信息

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopId": 1001,
    "shopName": "测试门店",
    "accountId": 5001,
    "totalRevenue": 125000.00,
    "availableBalance": 8500.00,
    "frozenBalance": 1500.00,
    "totalWithdraw": 115000.00,
    "pendingSettlement": 2000.00,
    "commissionRate": 0.15,
    "settlementCycle": "WEEKLY",
    "settlementCycleName": "周结",
    "nextSettlementTime": "2024-01-08T00:00:00",
    "defaultBankCardId": 3001,
    "defaultBankCard": {
      "id": 3001,
      "bankName": "中国工商银行",
      "bankAccount": "****7890",
      "accountName": "张三"
    },
    "bankCardCount": 2,
    "withdrawingAmount": 500.00,
    "todayWithdrawCount": 1,
    "todayWithdrawAmount": 500.00,
    "monthWithdrawCount": 8,
    "monthWithdrawAmount": 15000.00,
    "status": 1,
    "statusName": "正常",
    "hasWithdrawPassword": true
  }
}
```

### 5.2 获取门店收益明细

**接口**: `GET /shop/finance/revenue-details`

**描述**: 分页查询门店的收益明细记录

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述                    |
|----------|---------|-----|-------|-------------------------|
| current  | Integer | 否   | 1     | 当前页                  |
| size     | Integer | 否   | 10    | 每页大小                |
| startDate| String  | 否   | -     | 开始日期 (yyyy-MM-dd)   |
| endDate  | String  | 否   | -     | 结束日期 (yyyy-MM-dd)   |
| type     | String  | 否   | -     | 收益类型                |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 20001,
        "orderId": 10001,
        "orderNo": "ORD20240101001",
        "amount": 7.50,
        "type": "ORDER_COMMISSION",
        "typeName": "订单分成",
        "description": "订单分成收益",
        "createTime": "2024-01-01T11:30:00",
        "deviceNo": "DEV001",
        "roomNumber": "101"
      }
    ],
    "total": 500,
    "size": 10,
    "current": 1,
    "pages": 50
  }
}
```

### 5.3 获取收入明细（路径修正）

**接口**: `GET /shop/finance/income-details`

**描述**: 分页查询门店的收入明细记录（与前端路径保持一致）

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**: 同上

**响应示例**: 同上

### 5.4 获取门店分成配置信息

**接口**: `GET /shop/finance/commission-config`

**描述**: 查看门店的分成比例和结算周期配置（只读）

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "shopId": 1001,
    "commissionRate": 0.15,
    "commissionRatePercent": "15%",
    "settlementCycle": "WEEKLY",
    "settlementCycleName": "周结",
    "settlementDay": 1,
    "settlementDayName": "周一",
    "minSettlementAmount": 100.00,
    "maxDailyWithdraw": 10000.00,
    "withdrawFeeRate": 0.001,
    "withdrawFeeRatePercent": "0.1%",
    "isAutoSettlement": true,
    "configSource": "PARTNER",
    "configSourceName": "合作商配置",
    "lastUpdateTime": "2024-01-01T10:00:00"
  }
}
```

### 5.5 获取银行卡列表

**接口**: `GET /shop/finance/bank-cards`

**描述**: 获取门店的银行卡列表

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 3001,
      "shopId": 1001,
      "bankName": "中国工商银行",
      "bankAccount": "6222021234567890123",
      "accountName": "张三",
      "idCard": "110101199001011234",
      "mobile": "***********",
      "isDefault": true,
      "status": 1,
      "statusName": "正常",
      "createTime": "2024-01-01T10:00:00",
      "updateTime": "2024-01-01T10:00:00"
    }
  ]
}
```

### 5.6 添加银行卡

**接口**: `POST /shop/finance/bank-cards`

**描述**: 为门店添加新的银行卡

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名      | 类型   | 必填 | 描述       |
|------------|--------|-----|------------|
| bankName   | String | 是   | 银行名称   |
| bankAccount| String | 是   | 银行卡号   |
| accountName| String | 是   | 开户人姓名 |
| idCard     | String | 是   | 身份证号   |
| mobile     | String | 是   | 手机号     |
| isDefault  | Boolean| 否   | 是否默认   |

**请求示例**:

```json
{
  "bankName": "中国建设银行",
  "bankAccount": "6227001234567890123",
  "accountName": "李四",
  "idCard": "110101199002021234",
  "mobile": "***********",
  "isDefault": false
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "银行卡添加成功",
  "data": {
    "id": 3002,
    "shopId": 1001,
    "bankName": "中国建设银行",
    "bankAccount": "6227001234567890123",
    "accountName": "李四",
    "isDefault": false,
    "status": 1,
    "statusName": "正常",
    "createTime": "2024-01-01T12:00:00"
  }
}
```

### 5.7 更新银行卡

**接口**: `PUT /shop/finance/bank-cards/{cardId}`

**描述**: 更新门店银行卡信息

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述     |
|-------|------|-----|----------|
| cardId| Long | 是   | 银行卡ID |

**请求参数**: 同添加银行卡

**响应示例**:

```json
{
  "code": 200,
  "message": "银行卡更新成功",
  "data": true
}
```

### 5.8 删除银行卡

**接口**: `DELETE /shop/finance/bank-cards/{cardId}`

**描述**: 删除门店银行卡

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述     |
|-------|------|-----|----------|
| cardId| Long | 是   | 银行卡ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "银行卡删除成功",
  "data": true
}
```

### 5.9 申请提现

**接口**: `POST /shop/finance/withdraw`

**描述**: 提交门店提现申请

**权限**: `shop:finance:withdraw`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型    | 必填 | 描述       |
|-----------|---------|-----|------------|
| amount    | Decimal | 是   | 提现金额   |
| bankCardId| Long    | 是   | 银行卡ID   |
| password  | String  | 是   | 提现密码   |
| remark    | String  | 否   | 备注       |

**请求示例**:

```json
{
  "amount": 1000.00,
  "bankCardId": 3001,
  "password": "123456",
  "remark": "日常提现"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "提现申请提交成功",
  "data": {
    "withdrawId": 40001,
    "withdrawNo": "WD20240101001",
    "amount": 1000.00,
    "fee": 1.00,
    "actualAmount": 999.00,
    "status": 0,
    "statusName": "待审核",
    "bankCard": {
      "bankName": "中国工商银行",
      "bankAccount": "****7890",
      "accountName": "张三"
    },
    "createTime": "2024-01-01T14:00:00",
    "expectedArrivalTime": "2024-01-02T14:00:00"
  }
}
```

### 5.10 获取提现记录

**接口**: `GET /shop/finance/withdraw-records`

**描述**: 分页查询门店提现记录

**权限**: `shop:finance:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名  | 类型    | 必填 | 默认值 | 描述                                    |
|--------|---------|-----|-------|-----------------------------------------|
| current| Integer | 否   | 1     | 当前页                                  |
| size   | Integer | 否   | 10    | 每页大小                                |
| status | Integer | 否   | -     | 提现状态：0-待审核 1-审核通过 2-已到账 3-已拒绝 |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 40001,
        "withdrawNo": "WD20240101001",
        "amount": 1000.00,
        "fee": 1.00,
        "actualAmount": 999.00,
        "status": 2,
        "statusName": "已到账",
        "bankCard": {
          "bankName": "中国工商银行",
          "bankAccount": "****7890",
          "accountName": "张三"
        },
        "remark": "日常提现",
        "createTime": "2024-01-01T14:00:00",
        "approveTime": "2024-01-01T15:00:00",
        "arrivalTime": "2024-01-02T10:00:00"
      }
    ],
    "total": 25,
    "size": 10,
    "current": 1,
    "pages": 3
  }
}
```

### 5.11 取消提现申请

**接口**: `POST /shop/finance/withdraw/{withdrawId}/cancel`

**描述**: 取消待审核状态的提现申请

**权限**: `shop:finance:write`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名    | 类型 | 必填 | 描述     |
|----------|------|-----|----------|
| withdrawId| Long | 是   | 提现ID   |

**请求参数**:

| 参数名 | 类型   | 必填 | 描述     |
|-------|--------|-----|----------|
| reason| String | 否   | 取消原因 |

**请求示例**:

```json
{
  "reason": "暂时不需要提现"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "提现申请取消成功",
  "data": {
    "withdrawId": 40001,
    "withdrawNo": "WD20240101001",
    "amount": 1000.00,
    "status": 4,
    "statusName": "已取消",
    "cancelTime": "2024-01-01T16:00:00",
    "cancelReason": "暂时不需要提现"
  }
}
```

## 6. 统计分析接口

### 6.1 获取门店总览数据

**接口**: `GET /api/shop/statistics/overview`

**描述**: 获取门店的总体统计数据概览

**权限**: `shop:statistics:read`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalRevenue": 125000.00,
    "totalOrders": 2500,
    "totalDevices": 20,
    "avgOrderAmount": 50.00,
    "todayStats": {
      "revenue": 1250.00,
      "orders": 25,
      "activeDevices": 18,
      "avgUsageTime": 90
    },
    "monthStats": {
      "revenue": 34000.00,
      "orders": 680,
      "completionRate": 0.95,
      "avgDailyRevenue": 1100.00
    },
    "yearStats": {
      "revenue": 125000.00,
      "orders": 2500,
      "growth": 0.25,
      "bestMonth": "2024-01"
    },
    "deviceUtilization": {
      "totalDevices": 20,
      "activeDevices": 18,
      "utilizationRate": 0.85,
      "avgBatteryLevel": 78.5
    }
  }
}
```

### 6.2 获取日营业额统计

**接口**: `GET /api/shop/statistics/daily`

**描述**: 获取指定时间段的日营业额统计数据

**权限**: `shop:statistics:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名    | 类型   | 必填 | 描述                    |
|----------|--------|-----|-------------------------|
| startDate| String | 是   | 开始日期 (yyyy-MM-dd)   |
| endDate  | String | 是   | 结束日期 (yyyy-MM-dd)   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-07"
    },
    "summary": {
      "totalRevenue": 8750.00,
      "totalOrders": 175,
      "avgDailyRevenue": 1250.00,
      "avgDailyOrders": 25,
      "maxDailyRevenue": 1500.00,
      "minDailyRevenue": 800.00
    },
    "dailyData": [
      {
        "date": "2024-01-01",
        "revenue": 1250.00,
        "orders": 25,
        "avgOrderAmount": 50.00,
        "completedOrders": 24,
        "cancelledOrders": 1,
        "completionRate": 0.96,
        "peakHour": 14,
        "deviceUsageRate": 0.85
      }
    ]
  }
}
```

### 6.3 获取月营业额统计

**接口**: `GET /api/shop/statistics/monthly`

**描述**: 获取指定年份的月营业额统计数据

**权限**: `shop:statistics:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型    | 必填 | 描述                           |
|-------|---------|-----|--------------------------------|
| year  | Integer | 是   | 年份                           |
| month | Integer | 否   | 月份（不提供则返回整年数据）   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "year": 2024,
    "month": null,
    "summary": {
      "totalRevenue": 125000.00,
      "totalOrders": 2500,
      "avgMonthlyRevenue": 10416.67,
      "avgMonthlyOrders": 208,
      "bestMonth": {
        "month": 1,
        "revenue": 15000.00,
        "orders": 300
      },
      "worstMonth": {
        "month": 2,
        "revenue": 8000.00,
        "orders": 160
      }
    },
    "monthlyData": [
      {
        "month": 1,
        "revenue": 15000.00,
        "orders": 300,
        "avgOrderAmount": 50.00,
        "completionRate": 0.95,
        "growth": 0.25,
        "daysInMonth": 31,
        "avgDailyRevenue": 483.87
      }
    ]
  }
}
```

### 6.4 获取设备营收统计

**接口**: `GET /api/shop/statistics/device-revenue`

**描述**: 获取各设备的营收统计数据

**权限**: `shop:statistics:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名    | 类型   | 必填 | 描述                    |
|----------|--------|-----|-------------------------|
| startDate| String | 是   | 开始日期 (yyyy-MM-dd)   |
| endDate  | String | 是   | 结束日期 (yyyy-MM-dd)   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "dateRange": {
      "startDate": "2024-01-01",
      "endDate": "2024-01-31"
    },
    "summary": {
      "totalRevenue": 34000.00,
      "totalOrders": 680,
      "avgRevenuePerDevice": 1700.00,
      "avgOrdersPerDevice": 34,
      "topDevice": {
        "deviceNo": "DEV001",
        "revenue": 2500.00
      }
    },
    "deviceData": [
      {
        "deviceId": 1001,
        "deviceNo": "DEV001",
        "deviceName": "设备001",
        "roomNumber": "101",
        "revenue": 2500.00,
        "orders": 50,
        "avgOrderAmount": 50.00,
        "usageHours": 150,
        "utilizationRate": 0.85,
        "batteryLevel": 85,
        "faultCount": 0,
        "maintenanceCount": 1,
        "ranking": 1
      }
    ]
  }
}
```

### 6.5 导出统计报表

**接口**: `GET /api/shop/statistics/export`

**描述**: 导出统计报表为Excel文件

**权限**: `shop:statistics:read`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名     | 类型   | 必填 | 默认值 | 描述                                    |
|-----------|--------|-----|-------|-----------------------------------------|
| reportType| String | 是   | -     | 报表类型(daily:日报表 monthly:月报表 yearly:年报表 device:设备报表) |
| startDate | String | 是   | -     | 开始日期 (yyyy-MM-dd)                   |
| endDate   | String | 是   | -     | 结束日期 (yyyy-MM-dd)                   |
| format    | String | 否   | excel | 导出格式(excel:Excel pdf:PDF)           |

**响应示例**:

```json
{
  "code": 200,
  "message": "报表导出成功",
  "data": {
    "fileName": "门店统计报表_20240101_20240131.xlsx",
    "downloadUrl": "https://example.com/download/statistics_20240101.xlsx",
    "fileSize": 2048000,
    "reportType": "daily",
    "dateRange": "2024-01-01 至 2024-01-31",
    "recordCount": 680,
    "createTime": "2024-01-01T15:00:00",
    "expireTime": "2024-01-02T15:00:00"
  }
}
```

## 7. 通用功能接口

### 7.1 文件上传

**接口**: `POST /api/common/upload`

**描述**: 上传文件（支持图片、文档等）

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数**:

| 参数名 | 类型         | 必填 | 默认值 | 描述                           |
|-------|-------------|-----|-------|--------------------------------|
| file  | MultipartFile| 是   | -     | 上传的文件                     |
| type  | String      | 否   | image | 文件类型(image:图片 document:文档) |

**响应示例**:

```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileId": "FILE20240101001",
    "fileName": "fault_image.jpg",
    "originalName": "设备故障图片.jpg",
    "fileUrl": "https://example.com/uploads/fault_image.jpg",
    "fileSize": 1024000,
    "fileType": "image/jpeg",
    "uploadTime": "2024-01-01T15:00:00"
  }
}
```

### 7.2 获取系统配置

**接口**: `GET /api/common/system/config`

**描述**: 获取系统配置信息

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "appName": "今夜城堡门店端",
    "appVersion": "1.0.0",
    "apiVersion": "v1.0",
    "uploadMaxSize": 10485760,
    "supportedImageTypes": ["jpg", "jpeg", "png", "gif", "bmp", "webp"],
    "supportedDocTypes": ["pdf", "doc", "docx", "xls", "xlsx", "txt"],
    "withdrawConfig": {
      "minAmount": 100.00,
      "maxAmount": 10000.00,
      "feeRate": 0.001,
      "dailyLimit": 3
    },
    "orderConfig": {
      "maxDuration": 480,
      "minDuration": 30,
      "autoCompleteTime": 10
    }
  }
}
```

### 7.3 版本检查

**接口**: `GET /api/common/system/version`

**描述**: 检查应用版本更新

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名        | 类型   | 必填 | 描述                    |
|--------------|--------|-----|-------------------------|
| currentVersion| String | 是   | 当前版本号              |
| platform     | String | 是   | 平台(android/ios/h5)   |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "hasUpdate": true,
    "latestVersion": "1.1.0",
    "currentVersion": "1.0.0",
    "isForceUpdate": false,
    "updateInfo": {
      "title": "版本更新",
      "description": "1. 新增设备故障上报功能\n2. 优化统计报表\n3. 修复已知问题",
      "downloadUrl": "https://example.com/download/app_v1.1.0.apk",
      "fileSize": 52428800,
      "releaseTime": "2024-01-01T10:00:00"
    }
  }
}
```

## 8. 消息通知接口

### 8.1 获取消息通知列表

**接口**: `GET /api/shop/notifications`

**描述**: 分页获取门店的消息通知列表

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名  | 类型    | 必填 | 默认值 | 描述                           |
|--------|---------|-----|-------|--------------------------------|
| page   | Integer | 否   | 1     | 页码                           |
| size   | Integer | 否   | 10    | 每页条数                       |
| type   | String  | 否   | -     | 通知类型                       |
| isRead | Boolean | 否   | -     | 是否已读                       |

**响应示例**:

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 80001,
        "shopId": 1001,
        "title": "设备故障通知",
        "content": "设备DEV001出现故障，请及时处理",
        "type": "DEVICE_FAULT",
        "typeName": "设备故障",
        "priority": 3,
        "priorityName": "高",
        "isRead": false,
        "relatedId": 50001,
        "relatedType": "DEVICE_FAULT",
        "createTime": "2024-01-01T14:00:00",
        "readTime": null
      },
      {
        "id": 80002,
        "shopId": 1001,
        "title": "提现到账通知",
        "content": "您的提现申请已到账，金额：999.00元",
        "type": "WITHDRAW_SUCCESS",
        "typeName": "提现成功",
        "priority": 1,
        "priorityName": "低",
        "isRead": true,
        "relatedId": 40001,
        "relatedType": "WITHDRAW",
        "createTime": "2024-01-01T10:00:00",
        "readTime": "2024-01-01T10:30:00"
      }
    ],
    "total": 25,
    "size": 10,
    "current": 1,
    "pages": 3,
    "unreadCount": 5
  }
}
```

### 8.2 标记消息为已读

**接口**: `POST /api/shop/notifications/{id}/read`

**描述**: 标记指定消息为已读状态

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**路径参数**:

| 参数名 | 类型 | 必填 | 描述     |
|-------|------|-----|----------|
| id    | Long | 是   | 通知ID   |

**响应示例**:

```json
{
  "code": 200,
  "message": "消息已标记为已读",
  "data": {
    "id": 80001,
    "isRead": true,
    "readTime": "2024-01-01T15:00:00"
  }
}
```

### 8.3 批量标记消息为已读

**接口**: `POST /api/shop/notifications/batch-read`

**描述**: 批量标记多个消息为已读状态

**权限**: `SHOP_ADMIN`

**请求头**:
```
Authorization: Bearer {token}
```

**请求参数**:

| 参数名 | 类型  | 必填 | 描述       |
|-------|-------|-----|------------|
| ids   | Array | 是   | 通知ID数组 |

**请求示例**:

```json
{
  "ids": [80001, 80002, 80003]
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "批量标记成功",
  "data": {
    "successCount": 3,
    "failCount": 0,
    "readTime": "2024-01-01T15:00:00"
  }
}
```

## 9. 错误码说明

| 错误码 | 描述           | 解决方案                    |
|-------|----------------|----------------------------|
| 40001 | 参数错误       | 检查请求参数格式和必填项    |
| 40002 | 参数验证失败   | 检查参数类型和格式要求      |
| 40101 | 未认证         | 重新登录获取有效token       |
| 40102 | token过期      | 重新登录获取新token         |
| 40103 | token无效      | 检查token格式或重新登录     |
| 40301 | 权限不足       | 联系管理员分配相应权限      |
| 40302 | 数据权限不足   | 只能访问自己门店的数据      |
| 40401 | 资源不存在     | 检查请求的资源ID是否正确    |
| 40402 | 设备不存在     | 检查设备ID或MAC地址        |
| 40403 | 订单不存在     | 检查订单ID是否正确         |
| 40404 | 银行卡不存在   | 检查银行卡ID是否正确       |
| 50001 | 服务器内部错误 | 联系技术支持               |
| 50002 | 数据库连接失败 | 稍后重试或联系技术支持      |
| 50003 | 文件上传失败   | 检查文件格式和大小限制      |
| 50004 | 外部服务异常   | 稍后重试或联系技术支持      |

## 10. 业务错误码

| 错误码 | 描述               | 解决方案                    |
|-------|-------------------|----------------------------|
| 60001 | 密码错误           | 检查旧密码是否正确          |
| 60002 | 账户余额不足       | 检查可提现余额              |
| 60003 | 提现金额超限       | 检查提现金额限制            |
| 60004 | 银行卡信息错误     | 检查银行卡号和开户人信息    |
| 60005 | 设备状态异常       | 检查设备当前状态            |
| 60006 | 订单状态异常       | 检查订单当前状态            |
| 60007 | 文件类型不支持     | 检查文件格式是否在允许范围内 |
| 60008 | 文件大小超限       | 检查文件大小是否超过10MB    |
| 60009 | 操作频率过高       | 稍后再试                   |
| 60010 | 数据已存在         | 检查是否重复提交            |

## 11. 注意事项

### 11.1 认证要求
- 除登录接口外，所有接口都需要在请求头中携带有效的token
- Token格式：`Authorization: Bearer {token}`
- Token有效期：24小时（86400秒）
- Token过期后需要重新登录

### 11.2 权限控制
- 门店管理员只能访问自己门店的数据，无法跨门店操作
- 不同功能模块需要相应的权限：
  - `shop:device:read` - 设备查看权限
  - `shop:order:read` - 订单查看权限
  - `shop:finance:read` - 财务查看权限
  - `shop:finance:write` - 财务操作权限
  - `shop:statistics:read` - 统计查看权限

### 11.3 数据权限
- 所有查询接口都会自动过滤，只返回当前门店的数据
- 设备、订单、财务等数据都有门店级别的隔离
- 统计数据只包含当前门店的业务数据

### 11.4 操作限制
- 门店管理员只有查看和基础管理权限
- 无法进行设备绑定、订单创建等高级操作
- 财务操作有额度和频次限制
- 文件上传有类型和大小限制

### 11.5 审计日志
- 所有重要操作都会记录审计日志
- 包含操作人、操作时间、操作内容等信息
- 便于追踪和管理

### 11.6 性能建议
- 统计查询建议使用合理的时间范围
- 大数据量导出建议在业务低峰期进行
- 频繁查询的数据会有缓存机制

## 12. 联系支持

如有问题，请联系技术支持：
- 邮箱: <EMAIL>
- 电话: 400-123-4567
- 技术文档: https://docs.jycb.com

---

**文档版本**: v1.0
**最后更新**: 2024-01-01
**维护团队**: 今夜城堡技术团队
