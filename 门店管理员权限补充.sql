-- 门店管理员权限补充脚本
-- 为门店管理员角色添加缺失的权限

-- 1. 首先查看当前门店管理员角色的权限情况
SELECT 
    r.role_name,
    r.role_code,
    COUNT(p.id) as permission_count
FROM jy_role r
LEFT JOIN jy_role_permission rp ON r.id = rp.role_id
LEFT JOIN jy_permission p ON rp.permission_id = p.id AND p.status = 1
WHERE r.role_code = 'shop_admin'
GROUP BY r.id, r.role_name, r.role_code;

-- 2. 查看缺失的财务相关权限
SELECT p.* 
FROM jy_permission p 
WHERE p.permission LIKE 'shop:finance:%' 
  AND p.status = 1
  AND p.id NOT IN (
    SELECT rp.permission_id 
    FROM jy_role_permission rp 
    INNER JOIN jy_role r ON rp.role_id = r.id 
    WHERE r.role_code = 'shop_admin'
  );

-- 3. 添加缺失的财务权限
-- 首先确保这些权限存在于权限表中
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) VALUES
('shop:finance:read', '门店财务信息查看权限', 1, NOW(), NOW()),
('shop:finance:write', '门店财务操作权限', 1, NOW(), NOW()),
('shop:statistics:read', '门店统计数据查看权限', 1, NOW(), NOW()),
('shop:device:fault', '设备故障上报和管理权限', 1, NOW(), NOW()),
('shop:device:maintenance', '设备维护申请和管理权限', 1, NOW(), NOW()),
('shop:notification:read', '门店通知查看权限', 1, NOW(), NOW()),
('shop:notification:write', '门店通知操作权限', 1, NOW(), NOW()),
('shop:profile:read', '个人信息查看权限', 1, NOW(), NOW()),
('shop:profile:write', '个人信息修改权限', 1, NOW(), NOW());

-- 4. 为门店管理员角色分配这些权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_role r
CROSS JOIN jy_permission p
WHERE r.role_code = 'shop_admin'
  AND p.permission IN (
    'shop:finance:read',
    'shop:finance:write', 
    'shop:statistics:read',
    'shop:device:fault',
    'shop:device:maintenance',
    'shop:notification:read',
    'shop:notification:write',
    'shop:profile:read',
    'shop:profile:write'
  )
  AND p.status = 1;

-- 5. 验证权限分配结果
SELECT
    r.role_name,
    r.role_code,
    p.permission,
    p.description
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.status = 1
  AND p.permission LIKE 'shop:%'
ORDER BY p.permission;

-- 6. 统计最终权限数量
SELECT 
    r.role_name,
    r.role_code,
    COUNT(p.id) as total_permissions
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.status = 1
GROUP BY r.id, r.role_name, r.role_code;

-- 执行完成提示
SELECT '门店管理员权限补充完成！请重新登录以获取最新权限。' AS message;
