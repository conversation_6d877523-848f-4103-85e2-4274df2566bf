-- =====================================================
-- 数据库索引优化脚本
-- 针对门店财务数据查询性能优化
-- 
-- 执行前请务必备份数据库！
-- =====================================================

-- 1. 检查当前索引使用情况
-- =====================================================

-- 查看当前索引统计信息
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    SUB_PART,
    NULLABLE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'jycb_z' 
  AND TABLE_NAME IN ('jy_order', 'jy_device', 'jy_commission_detail', 'jy_finance_account')
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 2. 订单表索引优化
-- =====================================================

-- 2.1 门店订单查询复合索引（shop_id + pay_status + create_time）
-- 优化查询：SELECT * FROM jy_order WHERE shop_id = ? AND pay_status = 1 ORDER BY create_time DESC
CREATE INDEX idx_order_shop_pay_time ON jy_order (shop_id, pay_status, create_time DESC);

-- 2.2 设备订单统计复合索引（device_id + pay_status）
-- 优化查询：SELECT COUNT(*), SUM(amount) FROM jy_order WHERE device_id = ? AND pay_status = 1
CREATE INDEX idx_order_device_pay ON jy_order (device_id, pay_status);

-- 2.3 订单状态查询复合索引（pay_status + order_status + create_time）
-- 优化查询：SELECT * FROM jy_order WHERE pay_status = 1 AND order_status = ? ORDER BY create_time DESC
CREATE INDEX idx_order_status_time ON jy_order (pay_status, order_status, create_time DESC);

-- 2.4 用户订单查询复合索引（user_id + pay_status + create_time）
-- 优化查询：SELECT * FROM jy_order WHERE user_id = ? AND pay_status = 1 ORDER BY create_time DESC
CREATE INDEX idx_order_user_pay_time ON jy_order (user_id, pay_status, create_time DESC);

-- 2.5 订单时间范围查询索引
-- 优化查询：SELECT * FROM jy_order WHERE create_time BETWEEN ? AND ?
CREATE INDEX idx_order_create_time ON jy_order (create_time);

-- 3. 设备表索引优化
-- =====================================================

-- 3.1 门店设备查询复合索引（shop_id + status + create_time）
-- 优化查询：SELECT * FROM jy_device WHERE shop_id = ? AND status = 1 ORDER BY create_time DESC
CREATE INDEX idx_device_shop_status_time ON jy_device (shop_id, status, create_time DESC);

-- 3.2 合作商设备查询复合索引（partner_id + status）
-- 优化查询：SELECT * FROM jy_device WHERE partner_id = ? AND status = 1
CREATE INDEX idx_device_partner_status ON jy_device (partner_id, status);

-- 3.3 业务主体设备查询复合索引（entity_id + status）
-- 优化查询：SELECT * FROM jy_device WHERE entity_id = ? AND status = 1
CREATE INDEX idx_device_entity_status ON jy_device (entity_id, status);

-- 3.4 设备编号查询索引（唯一性约束）
-- 优化查询：SELECT * FROM jy_device WHERE device_no = ?
CREATE UNIQUE INDEX uk_device_no ON jy_device (device_no);

-- 4. 分成明细表索引优化
-- =====================================================

-- 4.1 门店分成查询复合索引（shop_id + create_time）
-- 优化查询：SELECT SUM(shop_amount) FROM jy_commission_detail WHERE shop_id = ? AND create_time >= ?
CREATE INDEX idx_commission_shop_time ON jy_commission_detail (shop_id, create_time);

-- 4.2 合作商分成查询复合索引（partner_id + create_time）
-- 优化查询：SELECT SUM(partner_amount) FROM jy_commission_detail WHERE partner_id = ? AND create_time >= ?
CREATE INDEX idx_commission_partner_time ON jy_commission_detail (partner_id, create_time);

-- 4.3 业务主体分成查询复合索引（entity_id + create_time）
-- 优化查询：SELECT SUM(entity_amount) FROM jy_commission_detail WHERE entity_id = ? AND create_time >= ?
CREATE INDEX idx_commission_entity_time ON jy_commission_detail (entity_id, create_time);

-- 4.4 结算状态查询复合索引（settlement_status + create_time）
-- 优化查询：SELECT * FROM jy_commission_detail WHERE settlement_status = ? ORDER BY create_time DESC
CREATE INDEX idx_commission_settlement_time ON jy_commission_detail (settlement_status, create_time DESC);

-- 5. 财务账户表索引优化
-- =====================================================

-- 5.1 账户类型查询复合索引（account_type + account_id）
-- 优化查询：SELECT * FROM jy_finance_account WHERE account_type = ? AND account_id = ?
CREATE INDEX idx_account_type_id ON jy_finance_account (account_type, account_id);

-- 5.2 账户状态查询复合索引（status + account_type）
-- 优化查询：SELECT * FROM jy_finance_account WHERE status = 1 AND account_type = ?
CREATE INDEX idx_account_status_type ON jy_finance_account (status, account_type);

-- 6. 财务流水表索引优化（如果存在）
-- =====================================================

-- 6.1 账户流水查询复合索引
-- CREATE INDEX idx_finance_log_account_time ON jy_finance_log (account_id, create_time DESC);

-- 6.2 流水类型查询复合索引
-- CREATE INDEX idx_finance_log_type_time ON jy_finance_log (log_type, create_time DESC);

-- 7. 覆盖索引优化（减少回表查询）
-- =====================================================

-- 7.1 订单基本信息覆盖索引
-- 包含常用查询字段，减少回表
CREATE INDEX idx_order_cover_basic ON jy_order (shop_id, pay_status, id, order_no, amount, actual_amount, create_time);

-- 7.2 设备基本信息覆盖索引
-- 包含常用查询字段，减少回表
CREATE INDEX idx_device_cover_basic ON jy_device (shop_id, status, id, device_no, device_name, create_time);

-- 8. 检查索引创建结果
-- =====================================================

-- 查看新创建的索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    INDEX_TYPE
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'jycb_z' 
  AND INDEX_NAME LIKE 'idx_%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 9. 索引使用情况监控
-- =====================================================

-- 查看索引使用统计（需要开启 performance_schema）
-- SELECT 
--     object_schema,
--     object_name,
--     index_name,
--     count_read,
--     count_write,
--     sum_timer_read,
--     sum_timer_write
-- FROM performance_schema.table_io_waits_summary_by_index_usage 
-- WHERE object_schema = 'jycb_z'
-- ORDER BY sum_timer_read DESC;

-- 10. 性能测试查询
-- =====================================================

-- 测试门店订单查询性能
EXPLAIN SELECT o.*, cd.shop_amount 
FROM jy_order o 
LEFT JOIN jy_commission_detail cd ON o.id = cd.order_id 
WHERE o.shop_id = 1 AND o.pay_status = 1 
ORDER BY o.create_time DESC 
LIMIT 20;

-- 测试设备统计查询性能
EXPLAIN SELECT 
    COUNT(*) as total_orders,
    SUM(CASE WHEN pay_status = 1 THEN amount ELSE 0 END) as total_amount
FROM jy_order 
WHERE device_id = 1;

-- 测试门店设备查询性能
EXPLAIN SELECT * 
FROM jy_device 
WHERE shop_id = 1 AND status = 1 
ORDER BY create_time DESC;

-- 测试分成统计查询性能
EXPLAIN SELECT 
    SUM(shop_amount) as total_commission,
    COUNT(*) as commission_count
FROM jy_commission_detail 
WHERE shop_id = 1 AND create_time >= '2024-01-01';

-- =====================================================
-- 索引优化完成
-- 
-- 注意事项：
-- 1. 索引创建可能需要较长时间，建议在业务低峰期执行
-- 2. 创建索引后需要更新表统计信息：ANALYZE TABLE table_name
-- 3. 定期监控索引使用情况，删除未使用的索引
-- 4. 注意索引对写入性能的影响
-- =====================================================
