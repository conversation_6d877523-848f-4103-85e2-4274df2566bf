-- ========================================
-- 财务系统数据库修复脚本
-- 创建时间: 2025-07-17
-- 说明: 修复财务系统数据库结构和数据一致性问题
-- ========================================

-- 设置安全模式
SET SQL_SAFE_UPDATES = 0;
SET FOREIGN_KEY_CHECKS = 0;

-- ========================================
-- 1. 备份现有数据（建议在执行前手动备份）
-- ========================================
-- mysqldump -u username -p jycb_z > jycb_z_backup_$(date +%Y%m%d_%H%M%S).sql

-- ========================================
-- 2. 修复字段类型不一致问题
-- ========================================

-- 修复 jy_finance_log 表的主键类型
ALTER TABLE `jy_finance_log` 
MODIFY COLUMN `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '流水ID';

-- 修复 jy_finance_log 表的关联字段类型
ALTER TABLE `jy_finance_log` 
MODIFY COLUMN `account_id` bigint(20) NOT NULL COMMENT '账户ID',
MODIFY COLUMN `entity_id` bigint(20) DEFAULT NULL COMMENT '业务主体ID',
MODIFY COLUMN `partner_id` bigint(20) DEFAULT NULL COMMENT '合作商ID',
MODIFY COLUMN `shop_id` bigint(20) DEFAULT NULL COMMENT '门店ID',
MODIFY COLUMN `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
MODIFY COLUMN `operator_id` bigint(20) DEFAULT NULL COMMENT '操作人ID';

-- 修复 jy_finance_account 表的字段类型
ALTER TABLE `jy_finance_account` 
MODIFY COLUMN `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
MODIFY COLUMN `account_id` bigint(20) NOT NULL COMMENT '账户所属ID',
MODIFY COLUMN `entity_id` bigint(20) DEFAULT NULL COMMENT '业务主体ID',
MODIFY COLUMN `partner_id` bigint(20) DEFAULT NULL COMMENT '合作商ID',
MODIFY COLUMN `shop_id` bigint(20) DEFAULT NULL COMMENT '门店ID';

-- ========================================
-- 3. 统一账户类型命名规范（使用小写）
-- ========================================

-- 更新 jy_finance_transaction 表的账户类型为小写
UPDATE `jy_finance_transaction` SET `account_type` = 'platform' WHERE `account_type` = 'PLATFORM';
UPDATE `jy_finance_transaction` SET `account_type` = 'entity' WHERE `account_type` = 'ENTITY';
UPDATE `jy_finance_transaction` SET `account_type` = 'partner' WHERE `account_type` = 'PARTNER';
UPDATE `jy_finance_transaction` SET `account_type` = 'shop' WHERE `account_type` = 'SHOP';
UPDATE `jy_finance_transaction` SET `account_type` = 'user' WHERE `account_type` = 'USER';
UPDATE `jy_finance_transaction` SET `account_type` = 'system' WHERE `account_type` = 'SYSTEM';

-- 修改字段注释以反映标准化的值
ALTER TABLE `jy_finance_transaction` 
MODIFY COLUMN `account_type` varchar(50) NOT NULL COMMENT '账户类型：platform-平台账户，entity-业务主体账户，partner-合作商账户，shop-门店账户，user-用户账户，system-系统账户';

-- ========================================
-- 4. 增加金额字段精度（支持更大金额）
-- ========================================

-- 修改金额字段精度为 decimal(15,2)
ALTER TABLE `jy_finance_account` 
MODIFY COLUMN `total_revenue` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总收入金额',
MODIFY COLUMN `available_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '可用余额',
MODIFY COLUMN `frozen_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '冻结余额',
MODIFY COLUMN `total_withdraw` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '已提现总额',
MODIFY COLUMN `device_fee` decimal(15,2) DEFAULT '0.00' COMMENT '设备费',
MODIFY COLUMN `system_fee` decimal(15,2) DEFAULT '0.00' COMMENT '系统使用费';

ALTER TABLE `jy_finance_log` 
MODIFY COLUMN `amount` decimal(15,2) NOT NULL COMMENT '金额',
MODIFY COLUMN `before_balance` decimal(15,2) NOT NULL COMMENT '变动前余额',
MODIFY COLUMN `after_balance` decimal(15,2) NOT NULL COMMENT '变动后余额';

ALTER TABLE `jy_finance_record` 
MODIFY COLUMN `amount` decimal(15,2) NOT NULL COMMENT '金额',
MODIFY COLUMN `fee` decimal(15,2) DEFAULT '0.00' COMMENT '手续费',
MODIFY COLUMN `before_balance` decimal(15,2) NOT NULL COMMENT '操作前余额',
MODIFY COLUMN `after_balance` decimal(15,2) NOT NULL COMMENT '操作后余额';

ALTER TABLE `jy_finance_transaction` 
MODIFY COLUMN `amount` decimal(15,2) NOT NULL COMMENT '交易金额',
MODIFY COLUMN `balance` decimal(15,2) NOT NULL COMMENT '交易后余额';

-- ========================================
-- 5. 添加缺失的索引以优化性能
-- ========================================

-- jy_finance_log 表索引优化
ALTER TABLE `jy_finance_log` 
ADD INDEX `idx_account_type_id_time` (`account_type`, `account_id`, `create_time`),
ADD INDEX `idx_amount` (`amount`),
ADD INDEX `idx_type_time` (`type`, `create_time`);

-- jy_finance_record 表索引优化
ALTER TABLE `jy_finance_record` 
ADD INDEX `idx_account_type_id_time` (`account_type`, `account_id`, `create_time`),
ADD INDEX `idx_type_status` (`type`, `status`),
ADD INDEX `idx_amount` (`amount`),
ADD INDEX `idx_operator` (`operator_id`);

-- jy_finance_transaction 表索引优化
ALTER TABLE `jy_finance_transaction` 
ADD INDEX `idx_account_type_id_time` (`account_type`, `account_id`, `create_time`),
ADD INDEX `idx_transaction_type_time` (`transaction_type`, `create_time`),
ADD INDEX `idx_amount` (`amount`);

-- jy_finance_account 表索引优化
ALTER TABLE `jy_finance_account` 
ADD INDEX `idx_balance` (`available_balance`),
ADD INDEX `idx_status_type` (`status`, `account_type`);

-- ========================================
-- 6. 添加数据完整性约束
-- ========================================

-- 添加账户类型检查约束
ALTER TABLE `jy_finance_account` 
ADD CONSTRAINT `chk_account_type` CHECK (`account_type` IN ('system', 'platform', 'entity', 'partner', 'shop', 'user'));

ALTER TABLE `jy_finance_log` 
ADD CONSTRAINT `chk_log_account_type` CHECK (`account_type` IN ('system', 'platform', 'entity', 'partner', 'shop', 'user'));

ALTER TABLE `jy_finance_record` 
ADD CONSTRAINT `chk_record_account_type` CHECK (`account_type` IN ('system', 'platform', 'entity', 'partner', 'shop', 'user'));

ALTER TABLE `jy_finance_transaction` 
ADD CONSTRAINT `chk_transaction_account_type` CHECK (`account_type` IN ('system', 'platform', 'entity', 'partner', 'shop', 'user'));

-- 添加金额检查约束（金额不能为负数，除了特殊情况）
ALTER TABLE `jy_finance_account` 
ADD CONSTRAINT `chk_available_balance` CHECK (`available_balance` >= 0),
ADD CONSTRAINT `chk_frozen_balance` CHECK (`frozen_balance` >= 0);

-- 添加状态检查约束
ALTER TABLE `jy_finance_account` 
ADD CONSTRAINT `chk_account_status` CHECK (`status` IN (0, 1));

ALTER TABLE `jy_finance_record` 
ADD CONSTRAINT `chk_record_status` CHECK (`status` IN (0, 1, 2));

-- ========================================
-- 7. 修复数据一致性问题
-- ========================================

-- 确保所有账户的余额计算正确
UPDATE `jy_finance_account` 
SET `total_revenue` = `available_balance` + `frozen_balance` + `total_withdraw`
WHERE `total_revenue` != (`available_balance` + `frozen_balance` + `total_withdraw`);

-- ========================================
-- 8. 添加触发器确保数据一致性（可选）
-- ========================================

DELIMITER $$

-- 创建触发器：在更新账户余额时自动更新总收入
CREATE TRIGGER `tr_finance_account_update_revenue` 
BEFORE UPDATE ON `jy_finance_account`
FOR EACH ROW
BEGIN
    -- 自动计算总收入
    SET NEW.total_revenue = NEW.available_balance + NEW.frozen_balance + NEW.total_withdraw;
    
    -- 更新版本号（乐观锁）
    SET NEW.version = OLD.version + 1;
END$$

DELIMITER ;

-- ========================================
-- 9. 恢复安全设置
-- ========================================
SET FOREIGN_KEY_CHECKS = 1;
SET SQL_SAFE_UPDATES = 1;

-- ========================================
-- 10. 验证修复结果
-- ========================================

-- 检查数据类型是否正确
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    COLUMN_TYPE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'jycb_z' 
    AND TABLE_NAME IN ('jy_finance_account', 'jy_finance_log', 'jy_finance_record', 'jy_finance_transaction')
    AND COLUMN_NAME IN ('id', 'account_id', 'amount', 'balance')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 检查账户类型值是否统一
SELECT DISTINCT account_type FROM jy_finance_account
UNION
SELECT DISTINCT account_type FROM jy_finance_log
UNION  
SELECT DISTINCT account_type FROM jy_finance_record
UNION
SELECT DISTINCT account_type FROM jy_finance_transaction;

-- 检查索引是否创建成功
SHOW INDEX FROM jy_finance_account;
SHOW INDEX FROM jy_finance_log;
SHOW INDEX FROM jy_finance_record;
SHOW INDEX FROM jy_finance_transaction;

-- ========================================
-- 修复完成提示
-- ========================================
SELECT 'Database fix completed successfully!' AS status;
