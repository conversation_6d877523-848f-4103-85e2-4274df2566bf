-- 权限数据插入脚本
-- 基于项目中的 @SaCheckPermission 注解提取的权限
-- 表结构：jy_permission (id, permission, description, status, create_time, update_time)

-- 清空现有权限数据（可选，根据需要决定是否执行）
-- DELETE FROM jy_permission;

-- 管理员管理相关权限
INSERT INTO jy_permission (permission, description, status, create_time, update_time) VALUES
('admin:admin:list', '查看管理员列表权限', 1, NOW(), NOW()),
('admin:admin:detail', '查看管理员详情权限', 1, NOW(), NOW()),
('admin:admin:create', '创建管理员权限', 1, NOW(), NOW()),
('admin:admin:update', '更新管理员信息权限', 1, NOW(), NOW()),
('admin:admin:delete', '删除管理员权限', 1, NOW(), NOW()),
('admin:role:list', '查看管理员角色列表权限', 1, NOW(), NOW()),
('admin:permission:list', '查看管理员权限列表权限', 1, NOW(), NOW()),
('admin:delete', '批量删除管理员权限', 1, NOW(), NOW()),

-- 管理员认证相关权限
('admin:auth:online', '查看在线用户列表权限', 1, NOW(), NOW()),
('admin:auth:kickout', '强制下线用户权限', 1, NOW(), NOW()),

-- 权限管理相关权限
('permission:list', '查看权限列表权限', 1, NOW(), NOW()),
('permission:detail', '查看权限详情权限', 1, NOW(), NOW()),
('permission:create', '创建权限权限', 1, NOW(), NOW()),
('permission:update', '更新权限权限', 1, NOW(), NOW()),
('permission:delete', '删除权限权限', 1, NOW(), NOW()),
('permission:check', '检查权限标识是否存在权限', 1, NOW(), NOW()),

-- 角色管理相关权限
('role:list', '查看角色列表权限', 1, NOW(), NOW()),
('role:detail', '查看角色详情权限', 1, NOW(), NOW()),
('role:create', '创建角色权限', 1, NOW(), NOW()),
('role:update', '更新角色', 'BUTTON', NULL, NULL, 33, 1, '更新角色权限', NOW(), NOW()),
('role:delete', '删除角色', 'BUTTON', NULL, NULL, 34, 1, '删除角色权限', NOW(), NOW()),

-- 菜单管理相关权限
('menu:list', '菜单列表', 'BUTTON', NULL, NULL, 40, 1, '查看菜单列表权限', NOW(), NOW()),
('menu:detail', '菜单详情', 'BUTTON', NULL, NULL, 41, 1, '查看菜单详情权限', NOW(), NOW()),
('menu:create', '创建菜单', 'BUTTON', NULL, NULL, 42, 1, '创建菜单权限', NOW(), NOW()),
('menu:update', '更新菜单', 'BUTTON', NULL, NULL, 43, 1, '更新菜单权限', NOW(), NOW()),
('menu:delete', '删除菜单', 'BUTTON', NULL, NULL, 44, 1, '删除菜单权限', NOW(), NOW()),

-- 业务主体管理相关权限
('entity:list', '业务主体列表', 'BUTTON', NULL, NULL, 50, 1, '查看业务主体列表权限', NOW(), NOW()),
('entity:read', '业务主体详情', 'BUTTON', NULL, NULL, 51, 1, '查看业务主体详情权限', NOW(), NOW()),
('entity:create', '创建业务主体', 'BUTTON', NULL, NULL, 52, 1, '创建业务主体权限', NOW(), NOW()),
('entity:update', '更新业务主体', 'BUTTON', NULL, NULL, 53, 1, '更新业务主体权限', NOW(), NOW()),
('entity:delete', '删除业务主体', 'BUTTON', NULL, NULL, 54, 1, '删除业务主体权限', NOW(), NOW()),

-- 合作商管理相关权限
('partner:read', '合作商详情', 'BUTTON', NULL, NULL, 60, 1, '查看合作商详情权限', NOW(), NOW()),
('partner:list', '合作商列表', 'BUTTON', NULL, NULL, 61, 1, '查看合作商列表权限', NOW(), NOW()),
('partner:create', '创建合作商', 'BUTTON', NULL, NULL, 62, 1, '创建合作商权限', NOW(), NOW()),
('partner:update', '更新合作商', 'BUTTON', NULL, NULL, 63, 1, '更新合作商权限', NOW(), NOW()),
('partner:delete', '删除合作商', 'BUTTON', NULL, NULL, 64, 1, '删除合作商权限', NOW(), NOW()),

-- 门店管理相关权限
('shop:view', '门店详情', 'BUTTON', NULL, NULL, 70, 1, '查看门店详情权限', NOW(), NOW()),
('shop:list', '门店列表', 'BUTTON', NULL, NULL, 71, 1, '查看门店列表权限', NOW(), NOW()),
('shop:create', '创建门店', 'BUTTON', NULL, NULL, 72, 1, '创建门店权限', NOW(), NOW()),
('shop:update', '更新门店', 'BUTTON', NULL, NULL, 73, 1, '更新门店权限', NOW(), NOW()),
('shop:delete', '删除门店', 'BUTTON', NULL, NULL, 74, 1, '删除门店权限', NOW(), NOW()),

-- 设备管理相关权限
('device:read', '设备详情', 'BUTTON', NULL, NULL, 80, 1, '查看设备详情权限', NOW(), NOW()),
('device:list', '设备列表', 'BUTTON', NULL, NULL, 81, 1, '查看设备列表权限', NOW(), NOW()),
('device:create', '创建设备', 'BUTTON', NULL, NULL, 82, 1, '创建设备权限', NOW(), NOW()),
('device:update', '更新设备', 'BUTTON', NULL, NULL, 83, 1, '更新设备权限', NOW(), NOW()),
('device:delete', '删除设备', 'BUTTON', NULL, NULL, 84, 1, '删除设备权限', NOW(), NOW()),

-- 订单管理相关权限
('order:list', '订单列表', 'BUTTON', NULL, NULL, 90, 1, '查看订单列表权限', NOW(), NOW()),
('order:detail', '订单详情', 'BUTTON', NULL, NULL, 91, 1, '查看订单详情权限', NOW(), NOW()),
('order:create', '创建订单', 'BUTTON', NULL, NULL, 92, 1, '创建订单权限', NOW(), NOW()),
('order:update', '更新订单', 'BUTTON', NULL, NULL, 93, 1, '更新订单权限', NOW(), NOW()),
('order:delete', '删除订单', 'BUTTON', NULL, NULL, 94, 1, '删除订单权限', NOW(), NOW()),
('order:refund', '订单退款', 'BUTTON', NULL, NULL, 95, 1, '订单退款权限', NOW(), NOW()),

-- 财务管理相关权限
('finance:list', '财务列表', 'BUTTON', NULL, NULL, 100, 1, '查看财务列表权限', NOW(), NOW()),
('finance:detail', '财务详情', 'BUTTON', NULL, NULL, 101, 1, '查看财务详情权限', NOW(), NOW()),
('finance:settlement', '财务结算', 'BUTTON', NULL, NULL, 102, 1, '财务结算权限', NOW(), NOW()),
('finance:withdraw', '提现管理', 'BUTTON', NULL, NULL, 103, 1, '提现管理权限', NOW(), NOW()),

-- 系统管理相关权限
('system:config', '系统配置', 'BUTTON', NULL, NULL, 110, 1, '系统配置权限', NOW(), NOW()),
('system:log', '系统日志', 'BUTTON', NULL, NULL, 111, 1, '查看系统日志权限', NOW(), NOW()),
('system:monitor', '系统监控', 'BUTTON', NULL, NULL, 112, 1, '系统监控权限', NOW(), NOW()),
('system:backup', '系统备份', 'BUTTON', NULL, NULL, 113, 1, '系统备份权限', NOW(), NOW()),

-- 用户管理相关权限
('user:list', '用户列表', 'BUTTON', NULL, NULL, 120, 1, '查看用户列表权限', NOW(), NOW()),
('user:detail', '用户详情', 'BUTTON', NULL, NULL, 121, 1, '查看用户详情权限', NOW(), NOW()),
('user:update', '更新用户', 'BUTTON', NULL, NULL, 122, 1, '更新用户权限', NOW(), NOW()),
('user:delete', '删除用户', 'BUTTON', NULL, NULL, 123, 1, '删除用户权限', NOW(), NOW()),

-- 统计分析相关权限
('statistics:view', '统计查看', 'BUTTON', NULL, NULL, 130, 1, '查看统计数据权限', NOW(), NOW()),
('statistics:export', '统计导出', 'BUTTON', NULL, NULL, 131, 1, '导出统计数据权限', NOW(), NOW()),

-- 数据权限相关
('data:view', '数据查看', 'BUTTON', NULL, NULL, 140, 1, '数据查看权限', NOW(), NOW()),
('data:export', '数据导出', 'BUTTON', NULL, NULL, 141, 1, '数据导出权限', NOW(), NOW()),
('data:import', '数据导入', 'BUTTON', NULL, NULL, 142, 1, '数据导入权限', NOW(), NOW()),

-- 审计日志相关权限
('audit:list', '审计日志列表', 'BUTTON', NULL, NULL, 150, 1, '查看审计日志列表权限', NOW(), NOW()),
('audit:detail', '审计日志详情', 'BUTTON', NULL, NULL, 151, 1, '查看审计日志详情权限', NOW(), NOW()),
('audit:export', '审计日志导出', 'BUTTON', NULL, NULL, 152, 1, '导出审计日志权限', NOW(), NOW()),

-- 微信小程序相关权限
('wechat:config', '微信配置', 'BUTTON', NULL, NULL, 160, 1, '微信小程序配置权限', NOW(), NOW()),
('wechat:user', '微信用户管理', 'BUTTON', NULL, NULL, 161, 1, '微信用户管理权限', NOW(), NOW()),

-- 支付相关权限
('payment:list', '支付列表', 'BUTTON', NULL, NULL, 170, 1, '查看支付列表权限', NOW(), NOW()),
('payment:detail', '支付详情', 'BUTTON', NULL, NULL, 171, 1, '查看支付详情权限', NOW(), NOW()),
('payment:refund', '支付退款', 'BUTTON', NULL, NULL, 172, 1, '支付退款权限', NOW(), NOW()),
('payment:config', '支付配置', 'BUTTON', NULL, NULL, 173, 1, '支付配置权限', NOW(), NOW()),

-- 文件管理相关权限
('file:upload', '文件上传', 'BUTTON', NULL, NULL, 180, 1, '文件上传权限', NOW(), NOW()),
('file:download', '文件下载', 'BUTTON', NULL, NULL, 181, 1, '文件下载权限', NOW(), NOW()),
('file:delete', '文件删除', 'BUTTON', NULL, NULL, 182, 1, '文件删除权限', NOW(), NOW()),
('file:manage', '文件管理', 'BUTTON', NULL, NULL, 183, 1, '文件管理权限', NOW(), NOW()),

-- 通知管理相关权限
('notification:list', '通知列表', 'BUTTON', NULL, NULL, 190, 1, '查看通知列表权限', NOW(), NOW()),
('notification:send', '发送通知', 'BUTTON', NULL, NULL, 191, 1, '发送通知权限', NOW(), NOW()),
('notification:delete', '删除通知', 'BUTTON', NULL, NULL, 192, 1, '删除通知权限', NOW(), NOW()),

-- 定时任务相关权限
('task:list', '任务列表', 'BUTTON', NULL, NULL, 200, 1, '查看定时任务列表权限', NOW(), NOW()),
('task:execute', '执行任务', 'BUTTON', NULL, NULL, 201, 1, '执行定时任务权限', NOW(), NOW()),
('task:stop', '停止任务', 'BUTTON', NULL, NULL, 202, 1, '停止定时任务权限', NOW(), NOW()),
('task:config', '任务配置', 'BUTTON', NULL, NULL, 203, 1, '配置定时任务权限', NOW(), NOW()),

-- 缓存管理相关权限
('cache:view', '缓存查看', 'BUTTON', NULL, NULL, 210, 1, '查看缓存权限', NOW(), NOW()),
('cache:clear', '缓存清理', 'BUTTON', NULL, NULL, 211, 1, '清理缓存权限', NOW(), NOW()),
('cache:refresh', '缓存刷新', 'BUTTON', NULL, NULL, 212, 1, '刷新缓存权限', NOW(), NOW()),

-- 超级管理员权限（通配符权限）
('*:*:*', '超级管理员权限', 'BUTTON', NULL, NULL, 999, 1, '超级管理员拥有所有权限', NOW(), NOW());

-- 提交事务
COMMIT;

-- 查询插入结果
SELECT COUNT(*) as '插入的权限数量' FROM jy_permission;
SELECT permission_code, permission_name, status FROM jy_permission ORDER BY sort_order;
