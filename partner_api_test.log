2025-07-28 06:54:34,663 - INFO - ==================================================
2025-07-28 06:54:34,663 - INFO - 开始测试认证登录API
2025-07-28 06:54:34,663 - INFO - ==================================================
2025-07-28 06:54:34,666 - INFO - ❌ FAIL 管理员登录
2025-07-28 06:54:34,666 - ERROR - Error: Invalid URL 'api.jycb888.com/auth/admin/login': No scheme supplied. Perhaps you meant https://api.jycb888.com/auth/admin/login?
2025-07-28 06:54:35,167 - INFO - ❌ FAIL 获取用户信息
2025-07-28 06:54:35,668 - INFO - ❌ FAIL 获取当前用户信息
2025-07-28 06:54:36,169 - INFO - ❌ FAIL 获取权限列表
2025-07-28 06:54:36,670 - INFO - ❌ FAIL 获取菜单列表
2025-07-28 06:54:37,171 - INFO - ❌ FAIL 验证密码
2025-07-28 06:54:37,171 - ERROR - Error: Invalid URL 'api.jycb888.com/auth/verify-password': No scheme supplied. Perhaps you meant https://api.jycb888.com/auth/verify-password?
2025-07-28 06:55:09,969 - INFO - ==================================================
2025-07-28 06:55:09,970 - INFO - 开始测试认证登录API
2025-07-28 06:55:09,970 - INFO - ==================================================
2025-07-28 06:55:10,506 - INFO - ✅ PASS 管理员登录
2025-07-28 06:55:10,506 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": "06940057-4819-452b-9c7b-8e7a6c53320c",
    "tokenType": "Bearer",
    "expiresIn": 86400,
    "userId": 3,
    "username": "partner_admin",
    "role": "partner_admin",
    "adminType": "partner",
    "entityId": 2,
    "partnerId": 1,
    "shopId": null,
    "realName": "合作商管理员",
    "avatar": null,
    "loginTime": null,
    "loginIp": null
  },
  "success": true
}
2025-07-28 06:55:11,237 - INFO - ✅ PASS 获取用户信息
2025-07-28 06:55:11,237 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 3,
    "username": "partner_admin",
    "realName": "合作商管理员",
    "avatar": null,
    "email": null,
    "mobile": "13800000002",
    "adminType": "partner",
    "entityId": 2,
    "partnerId": 1,
    "shopId": null,
    "status": 1,
    "loginIp": "*************",
    "loginTime": "2025-07-27T22:55:10.000+00:00",
    "roles": [
      "partner_admin"
    ],
    "permissions": [
      "partner:view",
      "partner:add",
      "partner:edit",
      "partner:delete",
      "shop:view",
      "shop:delete",
      "device:delete",
      "user:list",
      "admin:admin:list",
      "admin:admin:detail",
      "partner:read",
      "partner:list",
      "partner:update",
      "shop:list",
      "shop:create",
      "shop:update",
      "device:read",
      "device:list",
      "device:create",
      "device:update",
      "order:list",
      "order:detail",
      "order:update",
      "user:detail",
      "user:update",
      "finance:list",
      "finance:detail",
      "finance:settlement",
      "data:view",
      "data:export"
    ],
    "createTime": "2025-06-21T17:38:07.000+00:00"
  },
  "success": true
}
2025-07-28 06:55:11,959 - INFO - ✅ PASS 获取当前用户信息
2025-07-28 06:55:11,959 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "token": null,
    "tokenType": null,
    "expiresIn": null,
    "userId": 3,
    "username": "partner_admin",
    "role": "partner_admin",
    "adminType": "partner",
    "entityId": 2,
    "partnerId": 1,
    "shopId": null,
    "realName": "合作商管理员",
    "avatar": null,
    "loginTime": null,
    "loginIp": null
  },
  "success": true
}
2025-07-28 06:55:12,678 - INFO - ✅ PASS 获取权限列表
2025-07-28 06:55:12,678 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": [
    "partner:view",
    "partner:add",
    "partner:edit",
    "partner:delete",
    "shop:view",
    "shop:delete",
    "device:delete",
    "user:list",
    "admin:admin:list",
    "admin:admin:detail",
    "partner:read",
    "partner:list",
    "partner:update",
    "shop:list",
    "shop:create",
    "shop:update",
    "device:read",
    "device:list",
    "device:create",
    "device:update",
    "order:list",
    "order:detail",
    "order:update",
    "user:detail",
    "user:update",
    "finance:list",
    "finance:detail",
    "finance:settlement",
    "data:view",
    "data:export"
  ],
  "success": true
}
2025-07-28 06:55:13,397 - INFO - ✅ PASS 获取菜单列表
2025-07-28 06:55:13,397 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": [],
  "success": true
}
2025-07-28 06:55:14,120 - INFO - ✅ PASS 验证密码
2025-07-28 06:55:14,120 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": true,
  "success": true
}
2025-07-28 06:55:14,620 - INFO - ==================================================
2025-07-28 06:55:14,620 - INFO - 开始测试合作商信息管理API
2025-07-28 06:55:14,620 - INFO - ==================================================
2025-07-28 06:55:14,937 - INFO - ✅ PASS 获取合作商详情
2025-07-28 06:55:14,937 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "entityId": 2,
    "entityName": null,
    "partnerName": "北京一号合作商",
    "partnerCode": "BJ_PARTNER_01",
    "contactName": "测试联系人",
    "contactPhone": "***********",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区测试地址",
    "idCard": null,
    "bankName": null,
    "bankAccount": null,
    "accountName": null,
    "revenueRatio": 15.0,
    "deviceFee": 0.0,
    "systemFee": 0.0,
    "deviceCount": 0,
    "cooperationTime": null,
    "salesperson": null,
    "operator": null,
    "status": 1,
    "statusDesc": "启用",
    "remark": "API测试更新",
    "adminId": 3,
    "adminName": "partner_admin",
    "salesId": null,
    "salesName": null,
    "shopCount": 3,
    "deviceTotal": null,
    "totalRevenue": null,
    "createTime": "2025-06-22 01:35:38",
    "updateTime": "2025-07-28 03:30:20"
  }
}
2025-07-28 06:55:15,824 - INFO - ✅ PASS 更新合作商基本信息
2025-07-28 06:55:15,824 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "entityId": 2,
    "partnerName": "北京一号合作商",
    "partnerCode": "BJ_PARTNER_01",
    "contactName": "测试联系人",
    "contactPhone": "***********",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "address": "朝阳区测试地址",
    "idCard": null,
    "bankName": null,
    "bankAccount": null,
    "accountName": null,
    "revenueRatio": 15.0,
    "deviceFee": null,
    "systemFee": null,
    "deviceCount": null,
    "cooperationTime": null,
    "salesperson": null,
    "operator": null,
    "status": 1,
    "remark": "API测试更新",
    "adminId": null,
    "salesId": null,
    "salesName": null,
    "adminName": null,
    "shopCount": null,
    "createTime": null,
    "updateTime": null,
    "entityName": null,
    "childCount": null,
    "deleted": null,
    "version": null,
    "name": "北京一号合作商"
  }
}
2025-07-28 06:55:16,620 - INFO - ✅ PASS 获取合作商统计数据
2025-07-28 06:55:16,621 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "settlementType": "daily",
    "orderAmount": 0,
    "deviceCount": 26,
    "shopCount": 3,
    "orderCount": 2
  }
}
2025-07-28 06:55:17,122 - INFO - ==================================================
2025-07-28 06:55:17,122 - INFO - 开始测试门店管理API
2025-07-28 06:55:17,122 - INFO - ==================================================
2025-07-28 06:55:17,469 - INFO - ✅ PASS 获取门店分页列表
2025-07-28 06:55:17,469 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 3,
    "pages": 1,
    "list": [
      {
        "id": 3,
        "name": "API测试门店",
        "code": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": "北京一号合作商",
        "contactPerson": "测试联系人",
        "contactPhone": "***********",
        "contactEmail": null,
        "province": "北京市",
        "city": "北京市",
        "district": "海淀区",
        "address": "海淀区测试地址",
        "fullAddress": "北京市北京市海淀区海淀区测试地址",
        "businessLicense": null,
        "revenueRatio": 70.0,
        "longitude": null,
        "latitude": null,
        "status": 1,
        "statusText": "启用",
        "remark": "API测试创建的门店",
        "deviceCount": 13,
        "onlineDeviceCount": null,
        "offlineDeviceCount": null,
        "faultDeviceCount": null,
        "todayOrderCount": null,
        "todayIncome": null,
        "monthIncome": null,
        "totalIncome": null,
        "createBy": "3",
        "createTime": "2025-07-28 04:04:26",
        "updateBy": "3",
        "updateTime": "2025-07-28 04:04:26"
      },
      {
        "id": 2,
        "name": "海淀门店",
        "code": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": "北京一号合作商",
        "contactPerson": "张经理",
        "contactPhone": "***********",
        "contactEmail": null,
        "province": null,
        "city": null,
        "district": null,
        "address": "北京市海淀区中关村大街1号",
        "fullAddress": "北京市海淀区中关村大街1号",
        "businessLicense": null,
        "revenueRatio": 0.0,
        "longitude": null,
        "latitude": null,
        "status": 1,
        "statusText": "启用",
        "remark": null,
        "deviceCount": 1,
        "onlineDeviceCount": null,
        "offlineDeviceCount": null,
        "faultDeviceCount": null,
        "todayOrderCount": null,
        "todayIncome": null,
        "monthIncome": null,
        "totalIncome": null,
        "createBy": null,
        "createTime": "2025-07-22 04:16:54",
        "updateBy": null,
        "updateTime": "2025-07-22 04:16:54"
      },
      {
        "id": 1,
        "name": "朝阳门店",
        "code": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": "北京一号合作商",
        "contactPerson": "王店长",
        "contactPhone": "***********",
        "contactEmail": null,
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "北京市朝阳区CBD",
        "fullAddress": "北京市北京市朝阳区北京市朝阳区CBD",
        "businessLicense": null,
        "revenueRatio": 10.0,
        "longitude": null,
        "latitude": null,
        "status": 1,
        "statusText": "启用",
        "remark": null,
        "deviceCount": 12,
        "onlineDeviceCount": null,
        "offlineDeviceCount": null,
        "faultDeviceCount": null,
        "todayOrderCount": null,
        "todayIncome": null,
        "monthIncome": null,
        "totalIncome": null,
        "createBy": null,
        "createTime": "2025-06-22 01:35:01",
        "updateBy": null,
        "updateTime": "2025-06-22 01:35:01"
      }
    ]
  }
}
2025-07-28 06:55:18,240 - INFO - ✅ PASS 获取门店列表
2025-07-28 06:55:18,240 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "pageNum": 1,
    "pageSize": 1000,
    "total": 3,
    "pages": 1,
    "list": [
      {
        "id": 3,
        "name": "API测试门店",
        "code": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": "北京一号合作商",
        "contactPerson": "测试联系人",
        "contactPhone": "***********",
        "contactEmail": null,
        "province": "北京市",
        "city": "北京市",
        "district": "海淀区",
        "address": "海淀区测试地址",
        "fullAddress": "北京市北京市海淀区海淀区测试地址",
        "businessLicense": null,
        "revenueRatio": 70.0,
        "longitude": null,
        "latitude": null,
        "status": 1,
        "statusText": "启用",
        "remark": "API测试创建的门店",
        "deviceCount": 13,
        "onlineDeviceCount": null,
        "offlineDeviceCount": null,
        "faultDeviceCount": null,
        "todayOrderCount": null,
        "todayIncome": null,
        "monthIncome": null,
        "totalIncome": null,
        "createBy": "3",
        "createTime": "2025-07-28 04:04:26",
        "updateBy": "3",
        "updateTime": "2025-07-28 04:04:26"
      },
      {
        "id": 2,
        "name": "海淀门店",
        "code": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": "北京一号合作商",
        "contactPerson": "张经理",
        "contactPhone": "***********",
        "contactEmail": null,
        "province": null,
        "city": null,
        "district": null,
        "address": "北京市海淀区中关村大街1号",
        "fullAddress": "北京市海淀区中关村大街1号",
        "businessLicense": null,
        "revenueRatio": 0.0,
        "longitude": null,
        "latitude": null,
        "status": 1,
        "statusText": "启用",
        "remark": null,
        "deviceCount": 1,
        "onlineDeviceCount": null,
        "offlineDeviceCount": null,
        "faultDeviceCount": null,
        "todayOrderCount": null,
        "todayIncome": null,
        "monthIncome": null,
        "totalIncome": null,
        "createBy": null,
        "createTime": "2025-07-22 04:16:54",
        "updateBy": null,
        "updateTime": "2025-07-22 04:16:54"
      },
      {
        "id": 1,
        "name": "朝阳门店",
        "code": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": "北京一号合作商",
        "contactPerson": "王店长",
        "contactPhone": "***********",
        "contactEmail": null,
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "address": "北京市朝阳区CBD",
        "fullAddress": "北京市北京市朝阳区北京市朝阳区CBD",
        "businessLicense": null,
        "revenueRatio": 10.0,
        "longitude": null,
        "latitude": null,
        "status": 1,
        "statusText": "启用",
        "remark": null,
        "deviceCount": 12,
        "onlineDeviceCount": null,
        "offlineDeviceCount": null,
        "faultDeviceCount": null,
        "todayOrderCount": null,
        "todayIncome": null,
        "monthIncome": null,
        "totalIncome": null,
        "createBy": null,
        "createTime": "2025-06-22 01:35:01",
        "updateBy": null,
        "updateTime": "2025-06-22 01:35:01"
      }
    ]
  }
}
2025-07-28 06:55:19,028 - INFO - ✅ PASS 获取门店详情
2025-07-28 06:55:19,028 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 3,
    "name": "API测试门店",
    "code": null,
    "entityId": 2,
    "entityName": "北京业务主体",
    "partnerId": 1,
    "partnerName": "北京一号合作商",
    "contactPerson": "测试联系人",
    "contactPhone": "***********",
    "contactEmail": null,
    "province": "北京市",
    "city": "北京市",
    "district": "海淀区",
    "address": "海淀区测试地址",
    "fullAddress": "北京市北京市海淀区海淀区测试地址",
    "businessLicense": null,
    "revenueRatio": 70.0,
    "longitude": null,
    "latitude": null,
    "status": 1,
    "statusText": "启用",
    "remark": "API测试创建的门店",
    "deviceCount": 13,
    "onlineDeviceCount": 13,
    "offlineDeviceCount": 0,
    "faultDeviceCount": 0,
    "todayOrderCount": 0,
    "todayIncome": 0,
    "monthIncome": 0,
    "totalIncome": 0,
    "createBy": "3",
    "createTime": "2025-07-28 04:04:26",
    "updateBy": "3",
    "updateTime": "2025-07-28 04:04:26"
  }
}
2025-07-28 06:55:19,791 - INFO - ❌ FAIL 创建门店
2025-07-28 06:55:19,792 - INFO - Response: {
  "code": 500,
  "message": "门店名称已存在",
  "data": null
}
2025-07-28 06:55:20,557 - INFO - ✅ PASS 获取门店统计数据
2025-07-28 06:55:20,558 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "activeCount": 3,
    "inactiveCount": 0,
    "totalCount": 3,
    "recentAddedCount": 2
  }
}
2025-07-28 06:55:21,315 - INFO - ✅ PASS 获取门店营业概况
2025-07-28 06:55:21,315 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "activeShops": 3,
    "todayRevenue": 0.0,
    "monthRevenue": 0.0,
    "totalRevenue": 0.0,
    "totalShops": 3
  }
}
2025-07-28 06:55:21,816 - INFO - ==================================================
2025-07-28 06:55:21,816 - INFO - 开始测试设备管理API
2025-07-28 06:55:21,816 - INFO - ==================================================
2025-07-28 06:55:22,143 - INFO - ✅ PASS 获取设备列表
2025-07-28 06:55:22,144 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 11,
    "pages": 2,
    "list": [
      {
        "id": 1,
        "deviceNo": "TEST001",
        "deviceName": "测试设备",
        "deviceType": 33,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:CD:EF:12:34:56",
        "qrcodeUrl": "https://example.com?device=AB:CD:EF:12:34:56",
        "bindCode": "abcdef123456",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": null,
        "longitude": null,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": "2025-06-27 06:24:37",
        "lastOnlineTime": "2025-06-27 06:47:43",
        "lastLocationTime": "2025-06-27 06:47:41",
        "activateTime": "2025-06-27 06:47:44",
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-06-27 06:22:46",
        "updateTime": "2025-07-28 03:44:18",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 2,
        "deviceNo": "JY20230002",
        "deviceName": "成人仿真娃娃",
        "deviceType": 333,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "4fdcd11a-8acb-76e8-dd10-02979607edaf",
        "qrcodeUrl": null,
        "bindCode": "f1a8af4b-552d-11f0-bf3d-08bfb8bb8b89",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 1,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 0.0,
        "longitude": 0.0,
        "batteryLevel": 0,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": "2025-06-30 05:14:28",
        "lastOnlineTime": "2025-07-02 17:55:15",
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-06-30 05:13:39",
        "updateTime": "2025-07-25 05:22:53",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 3,
        "deviceNo": "JY20230003",
        "deviceName": "成人仿真娃娃",
        "deviceType": 333,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "2aba4802-caeb-dfeb-2127-44a5095bbe3e",
        "qrcodeUrl": "https://example.com?device=2aba4802-caeb-dfeb-2127-44a5095bbe3e",
        "bindCode": "8a0afd7c-5537-11f0-bf3d-08bfb8bb8b89",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 1,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 0.0,
        "longitude": 0.0,
        "batteryLevel": 0,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": "2025-06-30 06:24:30",
        "lastOnlineTime": "2025-07-02 17:55:15",
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-06-30 06:22:20",
        "updateTime": "2025-07-02 17:55:15",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 4,
        "deviceNo": "JY20230004",
        "deviceName": "成人仿真娃娃",
        "deviceType": 333,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "a72fda88-b1a9-95f7-bea4-e2ffe5b550bf",
        "qrcodeUrl": null,
        "bindCode": "a72fda88-b1a9-95f7-bea4-e2ffe5b550bf",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 1,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 0.0,
        "longitude": 0.0,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": "2025-07-02 17:55:13",
        "lastOnlineTime": "2025-07-02 17:55:14",
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-02 06:32:23",
        "updateTime": "2025-07-02 17:55:14",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 5,
        "deviceNo": "20000",
        "deviceName": "KSJAB5602000000",
        "deviceType": 1,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:56:02:00:00:00",
        "qrcodeUrl": "https://www.jycb888.com/qrcode/blue?jycb=1&id=020000",
        "bindCode": "020000",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": null,
        "longitude": null,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": null,
        "lastOnlineTime": null,
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-09 05:36:39",
        "updateTime": "2025-07-10 14:12:32",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 6,
        "deviceNo": "20001",
        "deviceName": "KSJAB5602000100",
        "deviceType": 1,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:56:02:00:01:00",
        "qrcodeUrl": "https://www.jycb888.com/qrcode/blue?jycb=1&id=020001",
        "bindCode": "020001",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 36.672102,
        "longitude": 116.917317,
        "batteryLevel": 93,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": null,
        "lastOnlineTime": null,
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-09 05:36:39",
        "updateTime": "2025-07-10 14:12:35",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 7,
        "deviceNo": "20002",
        "deviceName": "KSJAB5602000200",
        "deviceType": 1,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:56:02:00:02:00",
        "qrcodeUrl": "https://www.jycb888.com/qrcode/blue?jycb=1&id=020002",
        "bindCode": "020002",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 30.453051,
        "longitude": 104.095931,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": null,
        "lastOnlineTime": "2025-07-17 14:56:58",
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-09 05:36:39",
        "updateTime": "2025-07-15 02:35:21",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 8,
        "deviceNo": "20003",
        "deviceName": "KSJAB5602000300",
        "deviceType": 1,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:56:02:00:03:00",
        "qrcodeUrl": "https://www.jycb888.com/qrcode/blue?jycb=1&id=020003",
        "bindCode": "020003",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": null,
        "longitude": null,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": null,
        "lastOnlineTime": null,
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-09 05:36:39",
        "updateTime": "2025-07-10 14:12:38",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 9,
        "deviceNo": "20004",
        "deviceName": "KSJAB5602000400",
        "deviceType": 1,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:56:02:00:04:00",
        "qrcodeUrl": "https://www.jycb888.com/qrcode/blue?jycb=1&id=020004",
        "bindCode": "020004",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 30.452843,
        "longitude": 104.095825,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": null,
        "lastOnlineTime": null,
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-09 05:36:39",
        "updateTime": "2025-07-10 14:12:42",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      },
      {
        "id": 10,
        "deviceNo": "20005",
        "deviceName": "KSJAB5602000500",
        "deviceType": 1,
        "deviceTypeName": null,
        "deviceTypeDesc": null,
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "macAddress": "AB:56:02:00:05:00",
        "qrcodeUrl": "https://www.jycb888.com/qrcode/blue?jycb=1&id=020005",
        "bindCode": "020005",
        "isBound": 1,
        "status": 1,
        "statusName": null,
        "statusDesc": null,
        "onlineStatus": 0,
        "onlineStatusName": null,
        "inUse": 0,
        "inUseName": null,
        "latitude": 30.452885,
        "longitude": 104.095749,
        "batteryLevel": 100,
        "address": null,
        "province": null,
        "city": null,
        "district": null,
        "regionId": null,
        "bindTime": null,
        "lastOnlineTime": null,
        "lastLocationTime": null,
        "activateTime": null,
        "remark": null,
        "salesId": null,
        "salesName": null,
        "createTime": "2025-07-09 05:36:39",
        "updateTime": "2025-07-10 14:12:43",
        "usePrice": null,
        "billingType": null,
        "billingTypeName": null,
        "priceDesc": null,
        "roomNumber": null,
        "totalOrders": null,
        "todayOrders": null,
        "monthOrders": null,
        "totalRevenue": null,
        "todayRevenue": null
      }
    ]
  }
}
2025-07-28 06:55:22,900 - INFO - ✅ PASS 获取设备详情
2025-07-28 06:55:22,900 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "deviceNo": "TEST001",
    "deviceName": "测试设备",
    "deviceType": 33,
    "deviceTypeName": "未知",
    "deviceTypeDesc": "未知",
    "entityId": 2,
    "entityName": null,
    "partnerId": 1,
    "partnerName": null,
    "shopId": 1,
    "shopName": null,
    "macAddress": "AB:CD:EF:12:34:56",
    "qrcodeUrl": "https://example.com?device=AB:CD:EF:12:34:56",
    "bindCode": "abcdef123456",
    "isBound": 1,
    "status": 1,
    "statusName": "ONLINE",
    "statusDesc": "在线",
    "onlineStatus": 0,
    "onlineStatusName": null,
    "inUse": 0,
    "inUseName": null,
    "latitude": null,
    "longitude": null,
    "batteryLevel": 100,
    "address": null,
    "province": null,
    "city": null,
    "district": null,
    "regionId": null,
    "bindTime": "2025-06-27 06:24:37",
    "lastOnlineTime": "2025-06-27 06:47:43",
    "lastLocationTime": "2025-06-27 06:47:41",
    "activateTime": "2025-06-27 06:47:44",
    "remark": null,
    "salesId": null,
    "salesName": null,
    "createTime": "2025-06-27 06:22:46",
    "updateTime": "2025-07-28 03:44:18",
    "usePrice": null,
    "billingType": null,
    "billingTypeName": null,
    "priceDesc": null,
    "roomNumber": null,
    "totalOrders": null,
    "todayOrders": null,
    "monthOrders": null,
    "totalRevenue": null,
    "todayRevenue": null
  }
}
2025-07-28 06:55:23,808 - INFO - ✅ PASS 添加设备
2025-07-28 06:55:23,809 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 187,
    "deviceNo": "DEV20250727609878",
    "deviceName": "API测试设备",
    "deviceType": 1,
    "deviceTypeName": "TYPE_1",
    "deviceTypeDesc": "类型1",
    "entityId": 2,
    "entityName": null,
    "partnerId": 1,
    "partnerName": null,
    "shopId": 3,
    "shopName": null,
    "macAddress": "AA:BB:CC:DD:EE:23",
    "qrcodeUrl": "//qrcode/20250727/6244ab11e245419a8611f39b1f4d0fa9.png",
    "bindCode": "64504258",
    "isBound": 1,
    "status": 1,
    "statusName": "ONLINE",
    "statusDesc": "在线",
    "onlineStatus": 0,
    "onlineStatusName": null,
    "inUse": 0,
    "inUseName": null,
    "latitude": 39.916527,
    "longitude": 116.397128,
    "batteryLevel": 100,
    "address": "测试地址",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "regionId": null,
    "bindTime": null,
    "lastOnlineTime": null,
    "lastLocationTime": null,
    "activateTime": null,
    "remark": "API测试设备",
    "salesId": null,
    "salesName": null,
    "createTime": "2025-07-28 06:55:23",
    "updateTime": "2025-07-28 06:55:23",
    "usePrice": null,
    "billingType": null,
    "billingTypeName": null,
    "priceDesc": null,
    "roomNumber": null,
    "totalOrders": null,
    "todayOrders": null,
    "monthOrders": null,
    "totalRevenue": null,
    "todayRevenue": null
  }
}
2025-07-28 06:55:24,579 - INFO - ✅ PASS 获取设备统计
2025-07-28 06:55:24,579 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "onlineCount": 3,
    "offlineCount": 8,
    "unboundCount": 16,
    "boundCount": 11,
    "inUseCount": 0,
    "normalCount": 27,
    "totalCount": 11,
    "maintenanceCount": 0,
    "faultCount": 0
  }
}
2025-07-28 06:55:25,388 - INFO - ❌ FAIL 绑定设备
2025-07-28 06:55:25,389 - INFO - Response: {
  "code": 500,
  "message": "设备已绑定到门店，无法重复绑定",
  "data": null
}
2025-07-28 06:55:26,140 - INFO - ✅ PASS 获取设备费用列表
2025-07-28 06:55:26,140 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "id": 5,
        "feeName": "设备2标准收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 2,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 120,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-06-30 05:44:00",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 8,
        "feeName": "设备5按次收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 5,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 9,
        "feeName": "设备6按次收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 6,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 10,
        "feeName": "设备7按天收费",
        "feeType": 3,
        "feeTypeName": "包天",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 7,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "天",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": "设备7专用包天费率 - 98元/天",
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 11,
        "feeName": "设备8按次收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 8,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 12,
        "feeName": "设备9按次收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 9,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 13,
        "feeName": "设备10按次收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 10,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      },
      {
        "id": 14,
        "feeName": "设备11按次收费",
        "feeType": 1,
        "feeTypeName": "按时间",
        "entityId": 2,
        "entityName": null,
        "partnerId": 1,
        "partnerName": null,
        "shopId": 1,
        "shopName": null,
        "deviceId": 11,
        "deviceNo": null,
        "deviceName": null,
        "price": 0.01,
        "unit": "次",
        "minTime": 0,
        "maxTime": 0,
        "discountType": 0,
        "discountTypeName": "无优惠",
        "discountValue": 0.0,
        "discountCondition": 0.0,
        "startTime": null,
        "endTime": null,
        "status": 1,
        "statusName": "启用",
        "isDefault": 0,
        "remark": null,
        "createTime": "2025-07-09 05:37:06",
        "updateTime": "2025-07-23 14:58:00",
        "feeRules": null
      }
    ],
    "total": 8,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
2025-07-28 06:55:26,905 - INFO - ✅ PASS 创建设备费用配置
2025-07-28 06:55:26,905 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 17,
    "feeName": "API测试费用配置",
    "feeType": 1,
    "feeTypeName": "按时间",
    "entityId": null,
    "entityName": null,
    "partnerId": 1,
    "partnerName": null,
    "shopId": null,
    "shopName": null,
    "deviceId": 187,
    "deviceNo": null,
    "deviceName": null,
    "price": 10.0,
    "unit": "分钟",
    "minTime": 5,
    "maxTime": 120,
    "discountType": 0,
    "discountTypeName": "无优惠",
    "discountValue": 0.0,
    "discountCondition": 0.0,
    "startTime": null,
    "endTime": null,
    "status": 1,
    "statusName": "启用",
    "isDefault": 0,
    "remark": "API测试创建的费用配置",
    "createTime": "2025-07-28 06:55:26",
    "updateTime": "2025-07-28 06:55:26",
    "feeRules": null
  }
}
2025-07-28 06:55:27,642 - INFO - ✅ PASS 获取设备费用详情
2025-07-28 06:55:27,643 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 17,
    "feeName": "API测试费用配置",
    "feeType": 1,
    "feeTypeName": "按时间",
    "entityId": null,
    "entityName": null,
    "partnerId": 1,
    "partnerName": null,
    "shopId": null,
    "shopName": null,
    "deviceId": 187,
    "deviceNo": null,
    "deviceName": null,
    "price": 10.0,
    "unit": "分钟",
    "minTime": 5,
    "maxTime": 120,
    "discountType": 0,
    "discountTypeName": "无优惠",
    "discountValue": 0.0,
    "discountCondition": 0.0,
    "startTime": null,
    "endTime": null,
    "status": 1,
    "statusName": "启用",
    "isDefault": 0,
    "remark": "API测试创建的费用配置",
    "createTime": "2025-07-28 06:55:26",
    "updateTime": "2025-07-28 06:55:26",
    "feeRules": null
  }
}
2025-07-28 06:55:28,386 - INFO - ✅ PASS 更新设备费用配置
2025-07-28 06:55:28,386 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 17,
    "feeName": "API测试费用配置（已更新）",
    "feeType": 1,
    "feeTypeName": "按时间",
    "entityId": null,
    "entityName": null,
    "partnerId": 1,
    "partnerName": null,
    "shopId": null,
    "shopName": null,
    "deviceId": 187,
    "deviceNo": null,
    "deviceName": null,
    "price": 15.0,
    "unit": "分钟",
    "minTime": 10,
    "maxTime": 180,
    "discountType": 1,
    "discountTypeName": "折扣",
    "discountValue": 0.9,
    "discountCondition": 50.0,
    "startTime": null,
    "endTime": null,
    "status": 1,
    "statusName": "启用",
    "isDefault": 0,
    "remark": "API测试更新的费用配置",
    "createTime": "2025-07-28 06:55:26",
    "updateTime": "2025-07-28 06:55:27",
    "feeRules": null
  }
}
2025-07-28 06:55:29,186 - INFO - ✅ PASS 设置默认费用配置
2025-07-28 06:55:29,186 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": null
}
2025-07-28 06:55:29,917 - INFO - ✅ PASS 删除设备费用配置
2025-07-28 06:55:29,917 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": null
}
2025-07-28 06:55:30,418 - INFO - ==================================================
2025-07-28 06:55:30,418 - INFO - 开始测试财务管理API
2025-07-28 06:55:30,418 - INFO - ==================================================
2025-07-28 06:55:30,683 - INFO - ✅ PASS 获取财务统计信息
2025-07-28 06:55:30,683 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalBalance": 0.0,
    "totalWithdraw": 0.0,
    "monthIncome": 99.03,
    "pendingSettlement": 74.0,
    "shopCount": 3,
    "totalRevenue": 148.0,
    "todayIncome": 0,
    "frozenAmount": 0.0,
    "availableBalance": 0.0
  },
  "success": true
}
2025-07-28 06:55:31,417 - INFO - ✅ PASS 获取财务账户信息
2025-07-28 06:55:31,418 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 3,
    "accountType": "partner",
    "accountId": 1,
    "entityId": 2,
    "partnerId": 1,
    "shopId": null,
    "totalRevenue": 148.0,
    "availableBalance": 0.0,
    "frozenBalance": 0.0,
    "totalWithdraw": 0.0,
    "deviceFee": 0.0,
    "systemFee": 0.0,
    "lastSettlementTime": null,
    "status": 1,
    "version": 10,
    "createTime": "2025-07-17 13:29:52",
    "updateTime": "2025-07-28 04:22:24",
    "withdrawPassword": null,
    "totalIncome": 148.0,
    "frozenAmount": 0.0
  },
  "success": true
}
2025-07-28 06:55:32,163 - INFO - ✅ PASS 获取财务流水列表
2025-07-28 06:55:32,164 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 5,
    "pages": 1,
    "list": [
      {
        "id": 10,
        "accountType": "partner",
        "accountId": 1,
        "entityId": 2,
        "partnerId": 1,
        "shopId": null,
        "userId": null,
        "orderId": "121",
        "type": 1,
        "amount": 49.0,
        "beforeBalance": 50.03,
        "afterBalance": 99.03,
        "description": "订单分成收入",
        "operatorId": null,
        "operatorName": "系统",
        "createTime": "2025-07-17 14:56:56",
        "updateTime": "2025-07-17 14:56:56"
      },
      {
        "id": 6,
        "accountType": "partner",
        "accountId": 1,
        "entityId": 2,
        "partnerId": 1,
        "shopId": null,
        "userId": null,
        "orderId": "120",
        "type": 1,
        "amount": 50.0,
        "beforeBalance": 0.03,
        "afterBalance": 50.03,
        "description": "订单分成收入",
        "operatorId": null,
        "operatorName": "系统",
        "createTime": "2025-07-17 14:26:48",
        "updateTime": "2025-07-17 14:26:48"
      },
      {
        "id": 3,
        "accountType": "partner",
        "accountId": 1,
        "entityId": 2,
        "partnerId": 1,
        "shopId": null,
        "userId": null,
        "orderId": "119",
        "type": 1,
        "amount": 0.01,
        "beforeBalance": 0.02,
        "afterBalance": 0.03,
        "description": "订单分成收入",
        "operatorId": null,
        "operatorName": "系统",
        "createTime": "2025-07-17 14:09:58",
        "updateTime": "2025-07-17 14:09:58"
      },
      {
        "id": 2,
        "accountType": "partner",
        "accountId": 1,
        "entityId": 2,
        "partnerId": 1,
        "shopId": null,
        "userId": null,
        "orderId": "118",
        "type": 1,
        "amount": 0.01,
        "beforeBalance": 0.01,
        "afterBalance": 0.02,
        "description": "订单分成收入",
        "operatorId": null,
        "operatorName": "系统",
        "createTime": "2025-07-17 13:47:26",
        "updateTime": "2025-07-17 13:47:26"
      },
      {
        "id": 1,
        "accountType": "partner",
        "accountId": 1,
        "entityId": 2,
        "partnerId": 1,
        "shopId": null,
        "userId": null,
        "orderId": "117",
        "type": 1,
        "amount": 0.01,
        "beforeBalance": 0.0,
        "afterBalance": 0.01,
        "description": "订单分成收入",
        "operatorId": null,
        "operatorName": "系统",
        "createTime": "2025-07-17 13:29:52",
        "updateTime": "2025-07-17 13:29:52"
      }
    ]
  },
  "success": true
}
2025-07-28 06:55:32,921 - INFO - ✅ PASS 获取提现记录
2025-07-28 06:55:32,921 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 0,
    "pages": 0,
    "list": []
  },
  "success": true
}
2025-07-28 06:55:33,654 - INFO - ✅ PASS 获取银行卡列表
2025-07-28 06:55:33,654 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": 1,
      "userId": null,
      "bankName": "测试银行",
      "cardNo": "****************",
      "holderName": "测试用户",
      "bankBranch": null,
      "reservedPhone": null,
      "idCardNo": null,
      "isDefault": 0,
      "cardType": 1,
      "status": 1,
      "remark": null
    }
  ],
  "success": true
}
2025-07-28 06:55:34,400 - INFO - ❌ FAIL 添加银行卡
2025-07-28 06:55:34,401 - INFO - Response: {
  "code": 500,
  "message": "该银行卡号已存在",
  "data": null
}
2025-07-28 06:55:35,152 - INFO - ✅ PASS 获取可提现余额
2025-07-28 06:55:35,152 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": 0,
  "success": true
}
2025-07-28 06:55:35,653 - INFO - ==================================================
2025-07-28 06:55:35,653 - INFO - 开始测试订单管理API
2025-07-28 06:55:35,653 - INFO - ==================================================
2025-07-28 06:55:35,897 - INFO - ✅ PASS 获取订单列表
2025-07-28 06:55:35,897 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 2,
    "pages": 1,
    "list": [
      {
        "id": 122,
        "orderNo": "TEST_ORDER_TODAY_001",
        "deviceId": 1,
        "deviceNo": "DEV001",
        "entityId": 2,
        "partnerId": 1,
        "shopId": 1,
        "userId": 1,
        "orderStatus": 3,
        "startTime": "2025-07-21 02:58:11",
        "endTime": "2025-07-24 23:38:52",
        "amount": 50.0,
        "actualAmount": null,
        "duration": 0,
        "actualDuration": null,
        "payStatus": 1,
        "payTime": "2025-07-21 02:58:11",
        "payType": null,
        "transactionId": null,
        "refundStatus": 0,
        "refundTime": null,
        "refundAmount": 0.0,
        "refundReason": null,
        "commissionStatus": 1,
        "commissionTime": null,
        "userPhone": null,
        "useStartTime": null,
        "plannedDuration": null,
        "totalAmount": null,
        "remark": null,
        "createTime": "2025-07-21 02:58:11",
        "updateTime": "2025-07-24 23:38:52",
        "userName": null,
        "devicePosition": null
      },
      {
        "id": 121,
        "orderNo": "OD17527353997260d0b20",
        "deviceId": 7,
        "deviceNo": "20002",
        "entityId": 2,
        "partnerId": 1,
        "shopId": 1,
        "userId": 6,
        "orderStatus": 3,
        "startTime": "2025-07-17 14:56:58",
        "endTime": "2025-07-24 23:38:52",
        "amount": 98.0,
        "actualAmount": null,
        "duration": 0,
        "actualDuration": null,
        "payStatus": 1,
        "payTime": "2025-07-17 14:56:58",
        "payType": "WXPAY",
        "transactionId": "4200002739202507178596271047",
        "refundStatus": 0,
        "refundTime": null,
        "refundAmount": 0.0,
        "refundReason": null,
        "commissionStatus": 1,
        "commissionTime": "2025-07-17 14:56:56",
        "userPhone": null,
        "useStartTime": null,
        "plannedDuration": null,
        "totalAmount": null,
        "remark": "系统自动完成超过12小时的订单",
        "createTime": "2025-07-17 14:56:40",
        "updateTime": "2025-07-24 23:38:52",
        "userName": null,
        "devicePosition": null
      }
    ]
  }
}
2025-07-28 06:55:36,710 - INFO - ✅ PASS 获取订单统计
2025-07-28 06:55:36,710 - INFO - Response: {
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalAmount": 148.0,
    "todayCount": 0,
    "totalCount": 2,
    "completedCount": 0,
    "todayAmount": 0
  }
}
