-- 快速修复：为门店管理员添加订单查看权限
-- 解决 "无此权限：shop:order:read" 问题

-- 1. 确保订单查看权限存在
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) 
VALUES ('shop:order:read', '门店订单查看权限', 1, NOW(), NOW());

-- 2. 为门店管理员角色分配订单查看权限
INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_role r
CROSS JOIN jy_permission p
WHERE r.role_code = 'shop_admin'
  AND p.permission = 'shop:order:read'
  AND p.status = 1;

-- 3. 同时添加订单列表权限（通常一起使用）
INSERT IGNORE INTO jy_permission (permission, description, status, create_time, update_time) 
VALUES ('shop:order:list', '门店订单列表权限', 1, NOW(), NOW());

INSERT IGNORE INTO jy_role_permission (role_id, permission_id, create_time)
SELECT 
    r.id as role_id,
    p.id as permission_id,
    NOW() as create_time
FROM jy_role r
CROSS JOIN jy_permission p
WHERE r.role_code = 'shop_admin'
  AND p.permission = 'shop:order:list'
  AND p.status = 1;

-- 4. 验证权限是否添加成功
SELECT 
    r.role_name,
    p.permission,
    p.description
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.permission IN ('shop:order:read', 'shop:order:list');

-- 5. 查看门店管理员当前权限总数
SELECT 
    r.role_name,
    COUNT(p.id) as permission_count
FROM jy_role r
INNER JOIN jy_role_permission rp ON r.id = rp.role_id
INNER JOIN jy_permission p ON rp.permission_id = p.id
WHERE r.role_code = 'shop_admin'
  AND p.status = 1
GROUP BY r.id, r.role_name;

-- 执行完成提示
SELECT '订单查看权限已添加！门店管理员现在应该可以访问订单相关功能。请重新登录以获取最新权限。' AS message;
